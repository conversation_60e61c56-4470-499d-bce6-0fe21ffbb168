using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class PreferitiTest : TestBase
    {
        private readonly string _username;
        private readonly string _password;
        private OTTradingClient _client;
        private readonly string _market;
        private readonly string _stock;
        private readonly string _groupName;
        private string _preferitiId;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        public PreferitiTest(string name, bool isDummy = false) : base(name, isDummy)
        {
            // Ottieni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            _market = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "XNAS";
            _stock = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "MSFT";
            _groupName = Environment.GetEnvironmentVariable("OT_TEST_GROUP") ?? "Test Group";

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test dei preferiti");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // Implementazione dei test in base alle specifiche dei preferiti

                // 1. Recupera i gruppi di preferiti esistenti
                var existingGroups = await GetPreferitiGroupsAsync();
                LogTestStep("Recupero Gruppi", true, $"Trovati {existingGroups.Count} gruppi di preferiti");
                _testSteps.Add(new TestStepResult("Recupero Gruppi", true, $"Trovati {existingGroups.Count} gruppi di preferiti"));

                // 2. Crea un nuovo gruppo di preferiti
                if (!await CreatePreferitiGroupAsync())
                {
                    LogTestStep("Creazione Gruppo", false, "Impossibile creare il gruppo di preferiti");
                    _testSteps.Add(new TestStepResult("Creazione Gruppo", false, "Impossibile creare il gruppo di preferiti"));
                    return false;
                }
                LogTestStep("Creazione Gruppo", true, $"Gruppo '{_groupName}' creato con ID: {_preferitiId}");
                _testSteps.Add(new TestStepResult("Creazione Gruppo", true, $"Gruppo '{_groupName}' creato con ID: {_preferitiId}"));

                // 3. Aggiungi un titolo al gruppo
                if (!await AddStockToPreferitiAsync())
                {
                    LogTestStep("Aggiunta Titolo", false, $"Impossibile aggiungere il titolo {_stock} al gruppo");
                    _testSteps.Add(new TestStepResult("Aggiunta Titolo", false, $"Impossibile aggiungere il titolo {_stock} al gruppo"));
                    return false;
                }
                LogTestStep("Aggiunta Titolo", true, $"Titolo {_stock} aggiunto al gruppo");
                _testSteps.Add(new TestStepResult("Aggiunta Titolo", true, $"Titolo {_stock} aggiunto al gruppo"));

                // 4. Verifica che il titolo sia stato aggiunto
                if (!await VerifyStockInPreferitiAsync())
                {
                    LogTestStep("Verifica Titolo", false, $"Titolo {_stock} non trovato nel gruppo");
                    _testSteps.Add(new TestStepResult("Verifica Titolo", false, $"Titolo {_stock} non trovato nel gruppo"));
                    return false;
                }
                LogTestStep("Verifica Titolo", true, $"Titolo {_stock} trovato nel gruppo");
                _testSteps.Add(new TestStepResult("Verifica Titolo", true, $"Titolo {_stock} trovato nel gruppo"));

                // 5. Modifica il nome del gruppo
                string newGroupName = $"{_groupName}_Modified";
                if (!await RenamePreferitiGroupAsync(newGroupName))
                {
                    LogTestStep("Modifica Gruppo", false, $"Impossibile rinominare il gruppo in '{newGroupName}'");
                    _testSteps.Add(new TestStepResult("Modifica Gruppo", false, $"Impossibile rinominare il gruppo in '{newGroupName}'"));
                    return false;
                }
                LogTestStep("Modifica Gruppo", true, $"Gruppo rinominato in '{newGroupName}'");
                _testSteps.Add(new TestStepResult("Modifica Gruppo", true, $"Gruppo rinominato in '{newGroupName}'"));

                // 6. Rimuovi il titolo dal gruppo
                if (!await RemoveStockFromPreferitiAsync())
                {
                    LogTestStep("Rimozione Titolo", false, $"Impossibile rimuovere il titolo {_stock} dal gruppo");
                    _testSteps.Add(new TestStepResult("Rimozione Titolo", false, $"Impossibile rimuovere il titolo {_stock} dal gruppo"));
                    return false;
                }
                LogTestStep("Rimozione Titolo", true, $"Titolo {_stock} rimosso dal gruppo");
                _testSteps.Add(new TestStepResult("Rimozione Titolo", true, $"Titolo {_stock} rimosso dal gruppo"));

                // 7. Elimina il gruppo
                if (!await DeletePreferitiGroupAsync())
                {
                    LogTestStep("Eliminazione Gruppo", false, "Impossibile eliminare il gruppo di preferiti");
                    _testSteps.Add(new TestStepResult("Eliminazione Gruppo", false, "Impossibile eliminare il gruppo di preferiti"));
                    return false;
                }
                LogTestStep("Eliminazione Gruppo", true, "Gruppo eliminato con successo");
                _testSteps.Add(new TestStepResult("Eliminazione Gruppo", true, "Gruppo eliminato con successo"));

                Logger.Info("Test dei preferiti completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("Preferiti", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("Preferiti", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("Preferiti", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("Preferiti", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        private async Task<List<JsonElement>> GetPreferitiGroupsAsync()
        {
            try
            {
                // Ottieni le liste personali
                var request = new
                {
                    Broker = 2, // Sella
                    cols = new[] { "LIST_ID", "LIST_NAME", "STOCK_COUNT" },
                    paging = new { pageSize = 100, pageIndex = 0 }
                };

                var response = await _client.SendRequestAsync("POST", "api/List/GetPersonalList", request);
                return JsonResponseHelper.ExtractArrayProperty(response, "rows");
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero Gruppi", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero Gruppi", false, $"Errore: {ex.Message}"));
                return new List<JsonElement>();
            }
        }

        private async Task<bool> CreatePreferitiGroupAsync()
        {
            try
            {
                // Crea una lista personale
                var request = new
                {
                    Broker = 2, // Sella
                    ListName = _groupName,
                    ListDescription = $"Lista creata dal test automatico - {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    IsPublic = false
                };

                var response = await _client.SendRequestAsync("POST", "api/List/CreatePersonalList", request);

                // Estrai l'ID della lista creata
                _preferitiId = JsonResponseHelper.ExtractId(response, "ListId", "LIST_ID", "Id", "ID");

                if (string.IsNullOrEmpty(_preferitiId))
                {
                    // Se non riesce a estrarre l'ID, considera il test fallito
                    LogTestStep("Creazione Gruppo", false, "Impossibile estrarre l'ID della lista creata");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Creazione Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Creazione Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> AddStockToPreferitiAsync()
        {
            try
            {
                // Salva titoli nella lista personale
                var request = new
                {
                    Broker = 2, // Sella
                    ListId = int.Parse(_preferitiId),
                    ListName = _groupName,
                    IsPublic = false,
                    Stocks = new[]
                    {
                        new
                        {
                            MarketCode = _market,
                            StockCode = _stock
                        }
                    }
                };

                var response = await _client.SendRequestAsync("POST", "api/List/SavePersonalList", request);
                return JsonResponseHelper.IsUpdateSuccessful(response);
            }
            catch (Exception ex)
            {
                LogTestStep("Aggiunta Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Aggiunta Titolo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> VerifyStockInPreferitiAsync()
        {
            try
            {
                // Ottieni i dettagli della lista personale
                var request = new
                {
                    Broker = 2, // Sella
                    ListId = int.Parse(_preferitiId),
                    cols = new[] { "MARKET_CODE", "STOCK_CODE", "STOCK_DESCRIPTION" },
                    paging = new { pageSize = 100, pageIndex = 0 }
                };

                var response = await _client.SendRequestAsync("POST", "api/List/GetPersonalListDetail", request);

                // Verifica se esiste un titolo con il mercato e il codice specificati
                var stocks = JsonResponseHelper.ExtractArrayProperty(response, "rows");

                foreach (var stockItem in stocks)
                {
                    string marketCode = JsonResponseHelper.ExtractStringProperty(stockItem, "MARKET_CODE");
                    string stockCode = JsonResponseHelper.ExtractStringProperty(stockItem, "STOCK_CODE");

                    if (marketCode == _market && stockCode == _stock)
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogTestStep("Verifica Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Verifica Titolo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> RenamePreferitiGroupAsync(string newName)
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    newName = newName,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("PUT", "api/Preferiti/RenameGroup", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Modifica Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Modifica Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> RemoveStockFromPreferitiAsync()
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    market = _market,
                    stock = _stock,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("DELETE", "api/Preferiti/RemoveStock", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Rimozione Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Rimozione Titolo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> DeletePreferitiGroupAsync()
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("DELETE", "api/Preferiti/DeleteGroup", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Eliminazione Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Eliminazione Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        // Restituisce i risultati dei test step
        public List<TestStepResult> GetTestSteps()
        {
            return _testSteps;
        }
    }
}