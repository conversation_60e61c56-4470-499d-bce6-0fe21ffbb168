{"info": {"name": "OT", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "2-Customer", "item": [{"name": "GetMarkets", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetMarkets", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetMarkets"], "query": [], "variable": []}}}, {"name": "ImportModels", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Chart/ImportModels", "protocol": "", "host": ["{{url}}"], "path": ["api", "Chart", "ImportModels"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ImportStudies", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Chart/ImportStudies", "protocol": "", "host": ["{{url}}"], "path": ["api", "Chart", "ImportStudies"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"marketCode\": \"MTA\",\n  \"stockCode\": \"AMP\",\n  \"subjectID\": 1101369,\n  \"layoutType\": \"WCW\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Columns", "item": [{"name": "GetAllColumns", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetAllColumns", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetAllColumns"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{ }", "options": {"raw": {"language": "json"}}}}}, {"name": "GetColumns", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetColumns?infoSection=8", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetColumns?infoSection=8"], "query": [{"key": "infoSection", "value": "8"}], "variable": []}, "body": {"mode": "raw", "raw": "{ }", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Settings", "item": [{"name": "GetCustomerProperty", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Settings/GetCustomerProperty", "protocol": "", "host": ["{{url}}"], "path": ["api", "Settings", "GetCustomerProperty"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\":{\n    \"_customerID\": 0,\n    \"_property\": \"DEFAULT_STOCKLIST\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Layout", "item": [{"name": "DeleteLayout", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Layout/DeleteLayout", "protocol": "", "host": ["{{url}}"], "path": ["api", "Layout", "DeleteLayout"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"layoutID\": 234,\n    \"platformType\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DuplicateLayout", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Layout/DuplicateLayout", "protocol": "", "host": ["{{url}}"], "path": ["api", "Layout", "DuplicateLayout"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"newLayout\": {\n    \"_layoutID\": -1,\n    \"_customerID\": 1140,\n    \"_platformType\": 0,\n    \"_name\": \"lay-personale\",\n    \"_description\": \"test2\",\n    \"_isDefault\": false,\n    \"_creationDate\": \"2024-09-17T13:48:23.720732Z\",\n    \"_lastUpdateDate\": \"2024-09-17T13:48:23.720732Z\",\n    \"_lastUseDate\": \"2024-09-17T13:48:23.720732Z\"\n  },\n  \"originalPlatformType\": 0,\n  \"originalLayoutID\": 223\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetLayouts", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Layout/GetLayouts", "protocol": "", "host": ["{{url}}"], "path": ["api", "Layout", "GetLayouts"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"platformType\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SaveLayout", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Layout/SaveLayout", "protocol": "", "host": ["{{url}}"], "path": ["api", "Layout", "SaveLayout"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"layout\": {\n    \"_layoutID\": 231,\n    \"_customerID\": 1140,\n    \"_platformType\": 0,\n    \"_name\": \"Test 2 edit name\",\n    \"_description\": \"Test 2 description\",\n    \"_isDefault\": false,\n    \"_creationDate\": \"2024-09-26T13:13:30.961744Z\",\n    \"_lastUpdateDate\": \"2024-09-26T13:13:30.961744Z\",\n    \"_lastUseDate\": \"2024-09-26T13:13:30.961744Z\",\n    \"_json\": \"\"\n  },\n  \"layoutID\": 231,\n  \"platformType\": 0\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "PersonalList", "item": [{"name": "DeletePersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/DeletePersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "DeletePersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"personalList\": {\n    \"_customerId\": 1087,\n    \"_name\": \"_OPTIQ3\",\n    \"_id\": \"135\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetBrokerPersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/GetPersonalListBroker", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "GetPersonalListBroker"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Broker\": 2,\n  \"Channel\": 1\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPersonalList", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/GetPersonalList?logos=true", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "GetPersonalList?logos=true"], "query": [{"key": "logos", "value": "true"}], "variable": []}}}, {"name": "GetPortfolioAsPersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetPortfolioAsPersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetPortfolioAsPersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"<PERSON><PERSON><PERSON>\",\n    \"MARKET_CODE\",\n    \"STOCK_CODE\",\n    \"PORTFOLIO_QUANTITY\",\n    \"PORTFOLIO_PRICE\",\n    \"PORTFOLIO_GAIN_LOSS_BOOK\"\n  ],\n  \"filter\": {\n    \"_accountFilter\": {\n      \"_brokerName\": null,\n      \"_bondAcctId\": null,\n      \"_dossierId\": null,\n      \"_cashAcctId\": null,\n      \"_customerCode\": null\n    },\n    \"_stockFilter\": {\n      \"_stockTypeGroup\": null,\n      \"_stockType\": null,\n      \"_stockSubType\": null,\n      \"_stockTypeDetail\": null,\n      \"_marketCode\": null,\n      \"_stockCode\": null,\n      \"_searchType\": null,\n      \"_searchText\": null\n    },\n    \"_currency\": null,\n    \"_onlyShortMultiDay\": false,\n    \"_onlyIpoDerivative\": false,\n    \"_includeAfterHour\": true,\n    \"_valorizationMode\": 0,\n    \"_refreshCache\": false,\n    \"_brokerName\": 1,\n    \"_resetPortfolio\": false\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ImportBrokerPersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/ImportBrokerPersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "ImportBrokerPersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Broker\": 2,\n  \"Channel\": 1\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "MergeBrokerPersonalList", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/MergeBrokerPersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "MergeBrokerPersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"PersonalListId\": \"\",\n  \"BrokerListIds\": [\n    \n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SavePersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/List/SavePersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "List", "SavePersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"personalList\": {\n    \"_customerId\": 1087,\n    \"_id\": \"121\",\n    \"_name\": \"PHONE\",\n    \"_viewTemplateName\": \"STOCKS_PHONE2\",\n    \"_viewId\": \"154\",\n    \"_details\": [      \n    ],    \n    \"_tickerAddicted\": 0,\n    \"_systemView\": 1,\n    \"_portfolioView\": 0\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Personal<PERSON>iew", "item": [{"name": "GetPersonalView", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetPersonalView", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetPersonalView"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"platformType\": 4,\n  \"viewName\": \"SELLATRADER_PORTFOLIO\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPersonalViewNEW", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetPersonalViewNew", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetPersonalViewNew"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"platformType\": 4,\n  \"viewName\": \"SELLATRADER_PORTFOLIO\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPersonalViews", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetPersonalViews", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetPersonalViews"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"platformType\": 1\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPersonalViewsNEW", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/GetPersonalViewsNew", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "GetPersonalViewsNew"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"platformType\": 1\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Save<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/PersonalView/SavePersonalView", "protocol": "", "host": ["{{url}}"], "path": ["api", "Personal<PERSON>iew", "Save<PERSON><PERSON><PERSON><PERSON>ie<PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"personalView\": {\n    \"_name\": \"AZIONARIO_XTRADING\",\n    \"_description\": \"Azionario\",\n    \"_templateName\": \"AZIONARIO_XTRADING\",\n    \"_category\": 0,\n    \"_columns\": [\n      {\n        \"Name\": \"LAST\",\n        \"Description\": \"Ultimo Prezzo\",\n        \"Caption\": \"Ultimo\",\n        \"CaptionWeb\": \"Ultimo\",\n        \"PositiveNegative\": false,\n        \"Blink\": true,\n        \"Type\": 70,\n        \"Alignment\": 2,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"STOCK_DESCRIPTION\",\n        \"Description\": \"Descrizione\",\n        \"Caption\": \"Descrizione\",\n        \"CaptionWeb\": \"Mercato\",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 9,\n        \"Alignment\": 0,\n        \"Origin\": 0,\n        \"Section\": 20\n      },\n      {\n        \"Name\": \"VARIATION\",\n        \"Description\": \"Variazione %\",\n        \"Caption\": \"Var %\",\n        \"CaptionWeb\": \"Var %\",\n        \"Precision\": 2,\n        \"PositiveNegative\": true,\n        \"Blink\": true,\n        \"Type\": 6,\n        \"Alignment\": 2,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"BID_QTY_1\",\n        \"Description\": \"Quantità Denaro 1\",\n        \"Caption\": \"Q.ta Den\",\n        \"CaptionWeb\": \"Q.ta Den\",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 80,\n        \"Alignment\": 2,\n        \"Origin\": 1,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"BID_PRICE_1\",\n        \"Description\": \"Denaro 1\",\n        \"Caption\": \"Denaro\",\n        \"CaptionWeb\": \"Denaro\",\n        \"PositiveNegative\": false,\n        \"Blink\": true,\n        \"Type\": 70,\n        \"Alignment\": 2,\n        \"Origin\": 1,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"ASK_PRICE_1\",\n        \"Description\": \"Lettera 1\",\n        \"Caption\": \"Lettera\",\n        \"CaptionWeb\": \"Lettera\",\n        \"PositiveNegative\": false,\n        \"Blink\": true,\n        \"Type\": 70,\n        \"Alignment\": 2,\n        \"Origin\": 1,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"ASK_QTY_1\",\n        \"Description\": \"Quantità Lettera 1\",\n        \"Caption\": \"Q.ta Let\",\n        \"CaptionWeb\": \"Q.ta Let\",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 80,\n        \"Alignment\": 2,\n        \"Origin\": 1,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"VOLUME\",\n        \"Description\": \"Volume\",\n        \"Caption\": \"Volume\",\n        \"CaptionWeb\": \"Volume\",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 80,\n        \"Alignment\": 2,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"MIN_PRICE\",\n        \"Description\": \"Prezzo Minimo\",\n        \"Caption\": \"Min\",\n        \"CaptionWeb\": \"Min\",\n        \"PositiveNegative\": false,\n        \"Blink\": true,\n        \"Type\": 70,\n        \"Alignment\": 2,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"MAX_PRICE\",\n        \"Description\": \"Prezzo Massimo\",\n        \"Caption\": \"Max\",\n        \"CaptionWeb\": \"Max\",\n        \"PositiveNegative\": false,\n        \"Blink\": true,\n        \"Type\": 70,\n        \"Alignment\": 2,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"TIME_LAST\",\n        \"Description\": \"Ora Ultimo Scambio\",\n        \"Caption\": \"Ora Ultimo\",\n        \"CaptionWeb\": \"Ora Ultimo\",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 10,\n        \"Alignment\": 1,\n        \"Origin\": 5,\n        \"Section\": 2\n      },\n      {\n        \"Name\": \"EMPTY\",\n        \"Description\": \" \",\n        \"Caption\": \" \",\n        \"CaptionWeb\": \" \",\n        \"PositiveNegative\": false,\n        \"Blink\": false,\n        \"Type\": 9,\n        \"Alignment\": 0,\n        \"Origin\": 7,\n        \"Section\": 1\n      }\n    ],\n    \"_platformType\": 1\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}]}, {"name": "4-Operatività", "item": [{"name": "GetAccounts", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetAccounts", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetAccounts"], "query": [], "variable": []}}}, {"name": "GetAdvice", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Advice/GetAdvice", "protocol": "", "host": ["{{url}}"], "path": ["api", "Advice", "GetAdvice"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"ADVICE_IMAGE\",\n    \"ADVICE_TIME\",\n    \"ADVICE_TEXT\"\n  ],\n  \"filter\": {\n    \"_newsMinutes\": 0,\n    \"_orderLimit\": 10,\n    \"_orderBy\": 0,\n    \"_types\": [\n      0,\n      1,\n      2,\n      3,\n      4,\n      5,\n      6,\n      7\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetAdviceFull", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Advice/GetAdviceFull", "protocol": "", "host": ["{{url}}"], "path": ["api", "Advice", "GetAdviceFull"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"ADVICE_IMAGE\",\n    \"ADVICE_TIME\",\n    \"ADVICE_TEXT\"\n  ],\n  \"par\": {\n    \"_accountFilter\": {\n      \"_bondAcctId\": null,\n      \"_dossierId\": null,\n      \"_cashAcctId\": null,\n      \"_customerCode\": null\n    },\n    \"_stockFilter\":{\n      \"_searchType\": \"ISIN\"\n    },\n    \"_productType\": null,\n    \"_marketCode\": null,\n    \"_instrumentSearchType\": null,\n    \"_instrumentDescription\": null,\n    \"_orderCode\": null,\n    \"_messageType\": null,\n    \"_startDate\": \"2025-01-05\",\n    \"_endDate\": \"2025-01-08\",\n    \"_includeAfterHour\": null,\n    \"Broker\": 2\n  },\n  \"paging\": {\n    \"page\": 0,\n    \"pageSize\": 50\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetTradingInfo", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetTradingInfo", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetTradingInfo"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"broker\": 2,\n  \"marketCode\": \"MTA\",\n  \"stockCode\": \"A2A\",\n  \"stock\": {},\n  \"gamingId\": 0\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "MarginSimulator", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/MarginSimulator", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "MarginSimulator"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"_broker\": 2,\n  \"_macroChannelGroup\": 0,\n  \"_cashLiquidity\": 0,\n  \"_bondLiquidity\": 0,\n  \"_customerCode\": \"\",\n  \"_isIpo\": false,\n  \"_derMarginIPOAdjustment\": 0,\n  \"_selectedProducts\": [\n    {\n      \"StockCode\": \"FIB0325\",\n      \"MarketCode\": \"IDEM\",\n      \"Quantity\": 1,\n      \"Price\": 34780,\n      \"PositionType\": \"N\",\n      \"Leva\": 100,\n      \"InsSell\": 1,\n      \"medianPriceInsSell\": 35000\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "OptionsCalculator", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/OptionsCalculator", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "OptionsCalculator"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"_broker\": 2,\n  \"_marketCode\": \"IDEM\",\n  \"_stockCode\": \"FIB0325\",\n  \"_customerCode\": \"BB1023\",\n  \"_priceLimit\": 34610,\n  \"_accountCurrencyCode\": \"EUR\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Balance", "item": [{"name": "AccountLiquidityDetail", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/AccountLiquidityDetail?cashAccountId=6315198&accountType=0&vincolati=false", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "AccountLiquidityDetail?cashAccountId=6315198&accountType=0&vincolati=false"], "query": [{"key": "cashAccountId", "value": "6315198"}, {"key": "accountType", "value": "0"}, {"key": "v<PERSON><PERSON><PERSON>", "value": "false"}], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"GamingId\": 0,\n    \"CashAccountId\": \"6315198\",\n    \"Currency\": \"EUR\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetBalance", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/GetBalance", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "GetBalance"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"DESTINED_LIQUIDITY\",\n    \"BLOCKED_LIQUIDITY\",\n    \"AVA<PERSON><PERSON>LE_LIQUIDITY\",\n    \"CASH_TOTAL_COMMISSION\",\n    \"CASH_TOTAL_SELL\",\n    \"CASH_TOTAL_BUY\",\n    \"DER_BLOCKED_LIQUIDITY\",\n    \"DER_TOTAL_COMMISSION\",\n    \"TOTAL_PREMIUM\",\n    \"DER_TOTAL_PROFIT_LOSS\",\n    \"MAX_MARGIN\",\n    \"DER_TOTAL_MARGIN_OVERNIGHT\",\n    \"DER_TOTAL_MARGIN\",\n    \"CASH_TOTAL_MARGIN_OVERNIGHT\",\n    \"CASH_TOTAL_MARGIN\",\n    \"BOND_TOTAL_MARGIN_OVERNIGHT\",\n    \"BOND_DESTINED\",\n    \"BOND_LOCKED\",\n    \"BOND_AVAILABLE\",\n    \"TOTAL_REAL_PROFIT_LOSS\",\n    \"TOTAL_POTENTIAL_PROFIT_LOSS\",\n    \"PROFIT_LOSS_CALCULATED\",\n    \"GAMING_ID\",\n    \"N_FINANCING_AMOUNT\",\n    \"N_SHORT_AMOUNT\",\n    \"N_ACCT_ENGAGED_TIT\",\n    \"N_COVER_SMD\",\n    \"N_PROFIT_LOSS_LP\",\n    \"N_PROFIT_LOSS_IPO\",\n    \"N_MARGIN_SMD\",\n    \"N_PERC_MARGIN_DECREASE_IPO\"\n  ],\n  \"par\": {\n    \"BrokerName\": 2,\n    \"GamingId\": 0,\n    \"CashAccountId\": \"895247\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetBalanceDetail", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/GetBalanceDetail", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "GetBalanceDetail"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"GamingId\": 0,\n    \"CashAccountId\": \"6315198\",\n    \"Currency\": \"EUR\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ReservedAmount", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/ReservedAmount?cashAccountId=895247", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "ReservedAmount?cashAccountId=895247"], "query": [{"key": "cashAccountId", "value": "895247"}], "variable": []}}}, {"name": "SetLiquidity", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/SetLiquidity", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "SetLiquidity"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"GamingId\": 0,\n    \"CashAccountId\": \"895247\",\n    \"LiquidityValue\": 102\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Balance/TitoliDestinati?bondAccountId=6315203&cashAccountId=6315198", "protocol": "", "host": ["{{url}}"], "path": ["api", "Balance", "TitoliDestinati?bondAccountId=6315203&cashAccountId=6315198"], "query": [{"key": "bondAccountId", "value": "6315203"}, {"key": "cashAccountId", "value": "6315198"}], "variable": []}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "GetOrderDetail", "event": [{"listen": "prerequest", "script": {"exec": ["let body = req.getBody();", "", "//body._clientInfo.IbCode = bru.getEnvVar(\"\")", ""]}}], "request": {"method": "POST", "header": [{"key": "user-agent", "value": "customagent", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetOrderDetail", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetOrderDetail"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"brokerName\": 2,\n  \"orderCode\": \"\",  \n  \"_clientInfo\": {\n    \"ChannelType\": 13,\n    \"DeviceType\": 23,\n    \"BankId\": 0,\n    \"IbCode\": \"{{ibcode}}\",\n    \"IpAddress\": \"0.0.0.0\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetOrderStatus", "event": [{"listen": "prerequest", "script": {"exec": ["let body = req.getBody();", "", "if(body.filter._fromDate == null || body.filter._fromDate == \"\"){", "  ", "}", "if(body.filter._toDate == null || body.filter._toDate == \"\"){", "  ", "}", "bru.setEnvVar(\"Date2\", new Date().toISOString().split('T')[0]);", "bru.setEnvVar(\"Date1\", new Date().toISOString().split('T')[0]);"]}}], "request": {"method": "POST", "header": [{"key": "user-agent", "value": "customagent", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetOrderStatus2", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetOrderStatus2"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"VALIDITY_DATE\",\n    \"ISIN\",\n    \"CASH_ACCOUNT_CODE\",\n    \"CALL_PUT\",\n    \"DERIVATIVES_ID\",\n    \"BOND_ACCOUNT_CODE\",\n    \"ORDER_INSERT_DATE\",\n    \"ORDER_TIPOLOGY\",\n    \"ORDER_STATUS\",\n    \"STOCK_CODE\",\n    \"STOCK_DESCRIPTION\",\n    \"PRICE_TYPE\",\n    \"STOP_PRICE\",\n    \"VARIATION\",\n    \"VOLUME\",\n    \"LAST\",\n    \"TIME_LAST\"\n  ],\n  \"filter\": {\n    \"_brokerName\": 2,\n    \"_fromDate\": \"{{Date1}}\",\n    \"_toDate\": \"{{Date2}}\",\n    \"_orderShowType\": 0,\n    \"_accountFilter\": {},\n    \"_stockFilter\": {\n      \"_stockTypeGroup\": null,\n      \"_stockType\": null,\n      \"_stockSubType\": null,\n      \"_stockTypeDetail\": null,\n      \"_marketCode\": null,\n      \"_stockCode\": null,\n      \"_searchType\": null,\n      \"_searchText\": null\n    },\n    \"_showBuyOrders\": true,\n    \"_showSellOrders\": true,\n    \"_showLeverageOrders\": false,\n    \"_showSpecialOrders\": false,\n    \"_onlyMyOrders\": false,\n    \"_orderTipology\": 0,\n    \"_orderId\": null,\n    \"_orderType\": null,\n    \"_onlyOrdersWithExe\": false,\n    \"_refreshCache\": true,\n    \"_gamingId\": 0,\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19,\n      \"BankId\": 1,\n      \"IbCode\": \"{{ibcode}}\"\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Operazioni", "item": [{"name": "ConfirmDeleteOrder", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/ConfirmDeleteOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "ConfirmDeleteOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"OrderID\": \"TEMP_ID\",\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ConfirmInsert", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/ConfirmInsertOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "ConfirmInsertOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"OrderID\": \"TEMP_ID\",\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ConfirmUpdate", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/InsertOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "InsertOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    //\"OrderID\": null,\n    \"MarketCode\": \"MTA\",\n    \"StockCode\": \"AMP\",\n    \"OrderType\": 0,\n    \"Price\": 35,\n    \"Quantity\": 5,\n    \"ValidityDate\": \"today\",\n    \"EvaluationMode\": 0,\n    \"OrderFast\": false,\n    \"BrokerProperties\": {\n      \"BrokerCustomer\": \"********\",\n      \"BondAcctId\": 1300770,\n      \"CashAcctId\": 895247,\n      \"DossierId\": 898999,\n      \"BankId\": 1\n    },\n    \"OrderPhase\": 3,\n    \"ManualOrder\": false,\n    \"BestExecution\": false,\n    \"BatchOrder\": false,\n    \"ParkingOrder\": false,\n    \"LeverageOrder\": false,\n    \"StopPrice\": 0,\n    \"IcebergOrderQty\": 1,\n    \"OrderParameter\": 8,\n    \"GamingId\": 0,\n    \"PriceType\": 0,\n    \"StrategyEvaluationMode\": 0,\n    \"StrategyConditionType\": 0,\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "DeleteOrder", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/DeleteOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "DeleteOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"OrderID\": \"ORDER_ID\",\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Insert", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/InsertOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "InsertOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    //\"OrderID\": null,\n    \"MarketCode\": \"MTA\",\n    \"StockCode\": \"AMP\",\n    \"OrderType\": 0,\n    \"Price\": 33,\n    \"Quantity\": 1,\n    \"ValidityDate\": \"today\",\n    \"EvaluationMode\": 0,\n    \"OrderFast\": false,\n    \"BrokerProperties\": {\n      \"BrokerCustomer\": \"********\",\n      \"BondAcctId\": 1300770,\n      \"CashAcctId\": 895247,\n      \"DossierId\": 898999,\n      \"BankId\": 1\n    },\n    \"Phase\": 3,\n    \"ManualOrder\": false,\n    \"BestExecution\": false,\n    \"BatchOrder\": false,\n    \"ParkingOrder\": false,\n    \"LeverageOrder\": false,\n    \"StopPrice\": 0,\n    \"IcebergOrderQty\": null,\n    \"OrderParameter\": 8,\n    \"GamingId\": 0,\n    \"PriceType\": 0,\n    \"StrategyEvaluationMode\": 0,\n    \"StrategyConditionType\": 0,\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Update", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/UpdateOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "UpdateOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"OrderID\": \"**************\",\n    \"Price\": 0,\n    \"Quantity\":1,\n    \"IsFast\": false,\n    \"StopPrice\": 10,\n    \"GamingId\": 0,\n    \"DeleteAfterInsert\": null,\n    \"ValidityDays\": null,\n    \"OrderTypology\": 1,\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Strategy", "item": [{"name": "GetStrategyStatus", "event": [{"listen": "prerequest", "script": {"exec": ["let body = req.getBody();", "", "if(body.filter._fromDate == null || body.filter._fromDate == \"\"){", "  body.filter._fromDate = new Date().toISOString().split('T')[0];", "}", "if(body.filter._toDate == null || body.filter._toDate == \"\"){", "  body.filter._toDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "user-agent", "value": "customagent", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetStrategyStatus", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetStrategyStatus"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"BrokerName\": 2,\n  \"filter\": {\n    \"_accountFilter\": {\n      \"_brokerName\": null,\n      \"_bondAcctId\": null,\n      \"_dossierId\": null,\n      \"_cashAcctId\": null,\n      \"_customerCode\": null\n    },\n    \"_stockFilter\": {\n      \"_stockTypeGroup\": null,\n      \"_stockType\": null,\n      \"_stockSubType\": null,\n      \"_stockTypeDetail\": null,\n      \"_marketCode\": null,\n      \"_searchType\": null,\n      \"_searchText\": null,\n      \"_stockCode\": null\n    },\n    \"_strategyStatus\": 1,\n    \"_fromDate\": \"\",\n    \"_toDate\": \"\",\n    \"_strategyId\": null,\n    \"_clientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19,\n      \"BankId\": null,\n      \"IbCode\": \"{{ibcode}}\",\n      \"IpAddress\": \"0.0.0.0\",\n      \"Version\": \"1\"\n    },\n    \"_refreshCache\": false\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "InsertStrategy", "event": [{"listen": "prerequest", "script": {"exec": ["if(req.body.par.ValidityDate == \"today\"){", "  req.body.par.ValidityDate = new Date().toISOString().split('T')[0];", "}", ""]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "BrokerProperties\nVB\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```\nXT 464459\n```\n\"BondAcctId\": 0,\n\"CashAcctId\": 0,\n\"DossierId\": 0,\n\"CustomerCode\": \"string\",\n\"BankId\": 0\n```", "url": {"raw": "{{url}}/api/Order/InsertOrder", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "InsertOrder"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    \"MarketCode\": \"MTA\",\n    \"StockCode\": \"AMP\",\n    \"OrderType\": 0,\n    \"Price\": 32.9,\n    \"StopPrice\": null,\n    \"Quantity\": 1,\n    \"OrderFast\": false,\n    \"ValidityDate\": null,\n    \"BrokerProperties\": {\n      \"BondAccountCode\": \"T20278XX59190\",\n      \"CashAccountCode\": \"T25278XX59190\",\n      \"AliasDossier\": \"DIEGX XALGAXXLLA\",\n      \"AccountHolder\": \"DIEGX XALGAXXLLA\",\n      \"CanTrade\": true,\n      \"CashAccountCurrency\": \"EUR\",\n      \"IsDefaultDossier\": true,\n      \"LeverageIntradayEnabled\": false,\n      \"LeverageMultidayEnabled\": false,\n      \"LeveragePlusEnabled\": false,\n      \"LeveragePlusActive\": 0,\n      \"MarginAccountDers\": null,\n      \"MarginAccountShorts\": null,\n      \"MarginAccountShortPluss\": null,\n      \"MarginAccountShortMinuss\": null,\n      \"MarginAccountShortTranss\": null,\n      \"MarginIPOenabled\": false,\n      \"MarginIPOAdjustment\": 0,\n      \"MarginIPOactive\": false,\n      \"DerivativesEnabled\": 0,\n      \"ShortMultidayEnabled\": false,\n      \"EmirLEIActive\": false,\n      \"IsDefaultBondAccount\": true,\n      \"BrokerCustomer\": \"********\",\n      \"BondAcctId\": 1300770,\n      \"CashAcctId\": 895247,\n      \"DossierId\": 898999,\n      \"CustomerCode\": null,\n      \"BankId\": 1,\n      \"Key\": \"1300770.895247.898999.\",\n      \"KeyDispo\": \"1300770.895247.898999\",\n      \"Tag\": {\n        \"BondAcctId\": 1300770,\n        \"CashAcctId\": 895247,\n        \"DossierId\": 898999,\n        \"CustomerCode\": null,\n        \"BankId\": 1\n      }\n    },\n    \"OrderPhase\": null,\n    \"Phase\": \"NegoziazioneContinua\",\n    \"GamingId\": 0,\n    \"ParkingOrder\": false,\n    \"OrderParameter\": 8,\n    \"BestExecution\": false,\n    \"LeverageOrder\": false,\n    \"PriceType\": 0,\n    \"IcebergOrderQty\": 0,\n    \"BatchOrder\": false,\n    \"ClientInfo\": {\n      \"ChannelType\": 13,\n      \"DeviceType\": 23,\n      \"BankId\": 1,\n      \"IbCode\": \"********\"\n    },\n    \"TakeOrderLimitPrice\": \"20\",\n    \"TakeOrderStopPrice\": null,\n    \"StopOrderLimitPrice\": 0,\n    \"StopOrderStopPrice\": \"30\",\n    \"TakeStopOrderActive\": true,\n    \"OperationPurpose\": null\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}]}, {"name": "Portfolio", "item": [{"name": "GetPortfolio", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetPortfolio2", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetPortfolio2"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"<PERSON>R<PERSON><PERSON>_CODE\",\n    \"STOC<PERSON>_CODE\",\n    \"STOCK_DESCRIPTION\",\n    \"POR<PERSON><PERSON><PERSON>_PRICE\",\n    \"POR<PERSON><PERSON>IO_QUANTITY\",\n    \"LAST\",\n    \"POR<PERSON><PERSON><PERSON>_GAIN_LOSS_LAST\",\n    \"<PERSON><PERSON><PERSON><PERSON><PERSON>_GAIN_LOSS_LAST_PERC\",\n    \"<PERSON>ORTFOL<PERSON>_BOOK\",\n    \"<PERSON><PERSON><PERSON><PERSON>IO_GAIN_LOSS_BOOK\",\n    \"PORTFOLIO_GAIN_LOSS_BOOK_PERC\",\n    \"PORTFOLIO_CURRENCY\",\n    \"ACTUAL_LOCAL_EXCHANGE_RATE\",\n    \"POR<PERSON><PERSON><PERSON>_AVERAGE_EXCHANGE_RATE\",\n    \"PORTFOLIO_COUNTERVALUE\",\n    \"P<PERSON><PERSON><PERSON>IO_COUNTERVALUE_LOCAL\",\n    \"PORTFOLIO_PRICE_LOCAL\",\n    \"PORTFOLIO_GAIN_LOSS_LAST_LOCAL\",\n    \"PORTFOLIO_GAIN_LOSS_LAST_PERC_LOCAL\",\n    \"<PERSON><PERSON><PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_LOCAL\",\n    \"<PERSON><PERSON><PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_PERC_LOCAL\",\n    \"<PERSON><PERSON><PERSON><PERSON><PERSON>_BLOCKED\",\n    \"N_AVAILABLE_QTY\",\n    \"REFERENCE_PRICE\",\n    \"N_TAX_EXCHANGE_RATE\",\n    \"UNDERLYING_STOCK_CODE\",\n    \"PORTFOLIO_GAIN_LOSS_LAST_NET_LOCAL\",\n    \"PORTFOLIO_GAIN_LOSS_LAST_PERC_NET_LOCAL\",\n    \"PORTFOLIO_GAIN_LOSS_BOOK_NET_LOCAL\",\n    \"PORTFOLIO_GAIN_LOSS_BOOK_PERC_NET_LOCAL\"\n  ],\n  \"filter\": {\n    \"_accountFilter\": {\n      \"_brokerName\": null,\n      \"_bondAcctId\": null,\n      \"_dossierId\": null,\n      \"_cashAcctId\": null,\n      \"_customerCode\": null\n    },\n    \"_stockFilter\": {\n      \"_stockTypeGroup\": null,\n      \"_stockType\": null,\n      \"_stockSubType\": null,\n      \"_stockTypeDetail\": null,\n      \"_marketCode\": \"\",\n      \"_stockCode\": \"\",\n      \"_searchType\": null,\n      \"_searchText\": null\n    },\n    \"_currency\": null,\n    \"_onlyShortMultiDay\": false,\n    \"_onlyIpoDerivative\": false,\n    \"_includeAfterHour\": true,\n    \"_valorizationMode\": 0,\n    \"_refreshCache\": true,\n    \"_resetPortfolio\": false,\n    \"_brokerName\": 2\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPortfolio3", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetPortfolio3", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetPortfolio3"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"cols\": [\n        \"<PERSON>RKE<PERSON>_CODE\",\n        \"STOC<PERSON>_CODE\",\n        \"STOC<PERSON>_DESCRIPTION\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_PRICE\",\n        \"<PERSON>OR<PERSON><PERSON>IO_QUANTITY\",\n        \"LAST\",\n        \"<PERSON>OR<PERSON><PERSON><PERSON>_GAIN_LOSS_LAST\",\n        \"PORTFOL<PERSON>_GAIN_LOSS_LAST_PERC\",\n        \"PORTFOLIO_BOOK\",\n        \"PORTFOLIO_GAIN_LOSS_BOOK\",\n        \"PORTFOLIO_GAIN_LOSS_BOOK_PERC\",\n        \"PORTFOLIO_CURRENCY\",\n        \"ACTUAL_LOCAL_EXCHANGE_RATE\",\n        \"PORTFOLIO_AVERAGE_EXCHANGE_RATE\",\n        \"PORTFOLIO_COUNTERVALUE\",\n        \"P<PERSON><PERSON><PERSON>IO_COUNTERVALUE_LOCAL\",\n        \"PORTFOLIO_PRICE_LOCAL\",\n        \"PORTF<PERSON>IO_GAIN_LOSS_LAST_LOCAL\",\n        \"PORTFOLIO_GAIN_LOSS_LAST_PERC_LOCAL\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_LOCAL\",\n        \"<PERSON>OR<PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_PERC_LOCAL\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_BLOCKED\"\n    ],\n    \"filter\": {\n        \"_type\": \"PortfolioFilter\",\n        \"_accountFilter\": {\n            \"_type\": \"AccountFilter\",\n            \"_bondAcctId\": null,\n            \"_dossierId\": null,\n            \"_cashAcctId\": null,\n            \"_customerCode\": null\n        },\n        \"_stockFilter\": {\n            \"_type\": \"StockFilter\",\n            \"_stockTypeGroup\": null,\n            \"_stockType\": null,\n            \"_stockSubType\": null,\n            \"_stockTypeDetail\": null,\n            \"_marketCode\": null,\n            \"_stockCode\": \"ENEL\",\n            \"_searchType\": null,\n            \"_searchText\": null\n        },\n        \"ClientInfo\": {\n            \"ChannelType\": 12,\n            \"DeviceType\": 19\n        },\n        \"_currency\": null,\n        \"_onlyShortMultiDay\": false,\n        \"_onlyIpoDerivative\": false,\n        \"_includeAfterHour\": true,\n        \"_valorizationMode\": 0,\n        \"_brokerName\": 2,\n        \"_refreshCache\": true,\n        \"Type\": \"PortfolioFilter\",\n        \"_gamingId\": null\n    }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPortfolioAsPersonalList", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetPortfolioAsPersonalList", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetPortfolioAsPersonalList"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"cols\": [\n        \"<PERSON>RKE<PERSON>_CODE\",\n        \"STOC<PERSON>_CODE\",\n        \"STOC<PERSON>_DESCRIPTION\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_PRICE\",\n        \"<PERSON>OR<PERSON><PERSON>IO_QUANTITY\",\n        \"LAST\",\n        \"<PERSON>OR<PERSON><PERSON><PERSON>_GAIN_LOSS_LAST\",\n        \"PORTFOL<PERSON>_GAIN_LOSS_LAST_PERC\",\n        \"PORTFOLIO_BOOK\",\n        \"PORTFOLIO_GAIN_LOSS_BOOK\",\n        \"PORTFOLIO_GAIN_LOSS_BOOK_PERC\",\n        \"PORTFOLIO_CURRENCY\",\n        \"ACTUAL_LOCAL_EXCHANGE_RATE\",\n        \"PORTFOLIO_AVERAGE_EXCHANGE_RATE\",\n        \"PORTFOLIO_COUNTERVALUE\",\n        \"P<PERSON><PERSON><PERSON>IO_COUNTERVALUE_LOCAL\",\n        \"PORTFOLIO_PRICE_LOCAL\",\n        \"PORTF<PERSON>IO_GAIN_LOSS_LAST_LOCAL\",\n        \"PORTFOLIO_GAIN_LOSS_LAST_PERC_LOCAL\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_LOCAL\",\n        \"<PERSON>OR<PERSON><PERSON><PERSON>_GAIN_LOSS_BOOK_PERC_LOCAL\",\n        \"<PERSON><PERSON><PERSON><PERSON><PERSON>_BLOCKED\"\n    ],\n    \"filter\": {\n        \"_type\": \"PortfolioFilter\",\n        \"_accountFilter\": {\n            \"_type\": \"AccountFilter\",\n            \"_bondAcctId\": null,\n            \"_dossierId\": null,\n            \"_cashAcctId\": null,\n            \"_customerCode\": null\n        },\n        \"_stockFilter\": {\n            \"_type\": \"StockFilter\",\n            \"_stockTypeGroup\": null,\n            \"_stockType\": null,\n            \"_stockSubType\": null,\n            \"_stockTypeDetail\": null,\n            \"_marketCode\": null,\n            \"_stockCode\": \"ENEL\",\n            \"_searchType\": null,\n            \"_searchText\": null\n        },\n        \"ClientInfo\": {\n            \"ChannelType\": 12,\n            \"DeviceType\": 19\n        },\n        \"_currency\": null,\n        \"_onlyShortMultiDay\": false,\n        \"_onlyIpoDerivative\": false,\n        \"_includeAfterHour\": true,\n        \"_valorizationMode\": 0,\n        \"_brokerName\": 2,\n        \"_refreshCache\": true,\n        \"Type\": \"PortfolioFilter\",\n        \"_gamingId\": null\n    }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "NormalizePosition", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/NormalizePosition", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "NormalizePosition"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Broker\": 2,\n  \"PortfolioId\": 502610155,\n  \"QtyToNormalize\": 3,\n  \"NormalizationType\": 2\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Document", "item": [{"name": "GetInformativeNote", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Document/GetInformativeNote?broker=2&documentType=5&documentId=200265796253", "protocol": "", "host": ["{{url}}"], "path": ["api", "Document", "GetInformativeNote?broker=2&documentType=5&documentId=200265796253"], "query": [{"key": "broker", "value": "2"}, {"key": "documentType", "value": "5"}, {"key": "documentId", "value": "200265796253"}], "variable": []}}}, {"name": "SearchInformativeNote", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Document/SearchInformativeNote", "protocol": "", "host": ["{{url}}"], "path": ["api", "Document", "SearchInformativeNote"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Broker\": 2\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "ProfitLoss", "item": [{"name": "GetProfitLoss", "event": [{"listen": "prerequest", "script": {"exec": ["let body = req.getBody();", "", "if(body.filter._fromDate == null || body.filter._fromDate == \"\"){", "  body.filter._fromDate = new Date().toISOString().split('T')[0];", "}", "if(body.filter._toDate == null || body.filter._toDate == \"\"){", "  body.filter._toDate = new Date().toISOString().split('T')[0];", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetProfitLoss", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetProfitLoss"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"MARKET_CODE\",\n    \"STOCK_CODE\",\n    \"PROFIT_LOSS_QUANTITY\",\n    \"PROFIT_LOSS_BUY_PRICE\",\n    \"PROFIT_LOSS_SELL_PRICE\",\n    \"PROFIT_LOSS_GAIN_LOSS\"\n  ],\n  \"filter\": {\n    \"_accountFilter\": {},\n    \"_stockFilter\": {\n      \"_stockTypeGroup\": null,\n      \"_stockType\": null,\n      \"_stockSubType\": null,\n      \"_stockTypeDetail\": null,\n      \"_marketCode\": null,\n      \"_stockCode\": null,\n      \"_searchType\": null,\n      \"_searchText\": null\n    },\n    \"BrokerName\": 1,\n    \"_fromDate\": \"\",\n    \"_toDate\": \"\",\n    \"_gamingId\": 0,\n    \"_refreshCache\": false,\n    \"_currency\": null,\n    \"_closingOrderId\": null,\n    \"_valorizationMode\": 0,\n    \"_profitLossType\": 0,\n    \"ClientInfo\": {\n      \"ChannelType\": 13,\n      \"DeviceType\": 23,\n      \"IbCode\": \"{{ibcode}}\"\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}]}, {"name": "3-Anagrafica", "item": [{"name": "GetBaskets", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetBaskets", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetBaskets"], "query": [], "variable": []}}}, {"name": "TimeAndSale", "item": [{"name": "GetTicks", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/TimeAndSale/GetTicks", "protocol": "", "host": ["{{url}}"], "path": ["api", "TimeAndSale", "GetTicks"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"stock\": {\n    \"MarketCode\": \"MTA\",\n    \"StockCode\": \"A2A\"\n  },\n  \"filter\": {\n    \"_filterMode\": 0,\n    \"_ticks\": 100,\n    \"_minutes\": 30,\n    \"_startTime\": {\n      \"_days\": 0,\n      \"_hours\": 0,\n      \"_minutes\": 0,\n      \"_seconds\": 0,\n      \"_milliseconds\": 0\n    },\n    \"_endTime\": {\n      \"_days\": 0,\n      \"_hours\": 0,\n      \"_minutes\": 0,\n      \"_seconds\": 0,\n      \"_milliseconds\": 0\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "Book", "item": [{"name": "GetBook", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Book/GetBook", "protocol": "", "host": ["{{url}}"], "path": ["api", "Book", "GetBook"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"stock\":{\n    \"MarketCode\": \"\",\n    \"StockCode\": \"\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}]}, {"name": "StockList", "item": [{"name": "GetFastSearchInstrumentsGridPaged", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetFastSearchInstrumentsGridPaged", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetFastSearchInstrumentsGridPaged"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"STOCK_BUY_SELL\",\n    \"logo_dark\",\n    \"DELAY\",\n    \"STOCK_DESCRIPTION\",\n    \"LAST\",\n    \"VARIATION\",\n    \"BID_QTY_1\",\n    \"BID_PRICE_1\",\n    \"ASK_PRICE_1\",\n    \"ASK_QTY_1\",\n    \"VOLUME\",\n    \"MIN_PRICE\",\n    \"MAX_PRICE\",\n    \"TIME_LAST\",\n    \"EMPTY\",\n    \"OPEN_GRAPH\",\n    \"OPEN_BOOK\",\n    \"OPEN_ALARM\",\n    \"OPEN_PERSONAL_LIST\"\n  ],\n  \"search\": \"a2a\",\n  \"paging\": {\n    \"page\": 0,\n    \"pageSize\": 50\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStock", "event": [{"listen": "prerequest", "script": {"exec": ["const cookie = \"INGRESSCOOKIE=1737564304.21.70.113896|a0ceacac09434b56de20de757adf6762; .AspNetCore.Antiforgery.mcRt-dTccYw=CfDJ8H-b17o0GB5Nlycbu4Vhg1rFoirTiS944b6xH8C1CCNDY711dxKtE6wWXreqOvQGfOQ5m5VFEm6ipWKFKIWuRu0ZQ6YDuc6baL_9c1NTf7ETkWccTKcIPG9F67vDVo6sWcHSb1xcTtSSjgS5xzF4NK4; .AspNet.SharedCookieOTW=CfDJ8H-b17o0GB5Nlycbu4Vhg1pmHvjYpxQPyvgepuZkIq9_htV6P1fTuCHiCIefT1hwqt2tCOODn89OXVTIx-yNiEAtee_3cTRRXJc1fM18idFRl73gPxuzwTYTwJEQhuaKRiblWCXR0Tif6rWGpU6ionWrMnOnZkjYYfw7DKKuHfGzxUm1qs6KYQuJSuZz-DTrxTRFat0H3pTmhSVR_oBJ8m7HV7fkLnwDwaAh7MZCgnzIayOoexl0_pZBV1kcVboF8_p4Q1lrHozIoJfoWqFL6Uej-XWxNi85qR0bg1adXikVLDOVIA4WxC7zNwHMqCKCANhKGyFp7kXc_X86kfMo9TpsCqApnicVd4Ur0Rgg-3hgTTAf0YGEHNsmkcstouMoQbBzf_s54vGP5esGx-7Nays--wmiJJ4s3dOd1kyRfelpyoaROlbIFx77gzcm2G3XlXXnXMPl-CwzJpq4WL1PgtnqdaztDBdGhQqulWKxchAOUUfDqmMSGtf-qZ6Y2pb8v2VaDYuPatAzFCeT-VxJJNUIoDEbHRGmrK6jMLVMwMMuX8YujakFbDpUP8FLPggi_Sg-sahWYaYMiPq2DUkulrey_hRzQRNekzN2Bs7wfMSHHZMobM7jjTADEXc45DUMOrEbx0VZaDpdPvW30JP7C2r8M25wAlXPBc9LW9vGPYern7moZqucoJJe1ElZZIYbmLQhLHo2BpExymye6ng2-JKhWyIli-rrWKtkLck1yzGTfC5QwelvkPfbAo-ogUJFAFbZhigbm1B8r9-imiuimH0; path=/; samesite=lax; httponly\";", "", "if(cookie) {", "  req.set<PERSON><PERSON><PERSON>(\"cookie\", cookie)", "}"]}}], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStock", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStock"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"MarketCode\": \"MCW\",\n  \"StockCode\": \"F59937\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockForDetail", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStockForDetail", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockForDetail"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"MarketCode\": \"\",\n  \"StockCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockForDetail2", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStockForDetail2", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockForDetail2"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"STOCK_DESCRIPTION\",\n    \"STOC<PERSON>_CODE\",\n    \"STOCK_TYPE\",\n    \"MARKET_SYMBOL\",\n    \"ISIN\",\n    \"PLACE\",\n    \"MARKET_CODE\",\n    \"SEGMENT_CODE\",\n    \"COMODITY_GROUP\",\n    \"SECTOR\",\n    \"SUB_SECTOR\",\n    \"STOCK_TYPE_DESCRIPTION\",\n    \"STOCK_SUBTYPE_DESCRIPTION\",\n    \"AFTERHOUR\",\n    \"TOTAL_STOCK_NUMBER\",\n    \"CONTRACT_SIZE\",\n    \"MIN_TRADE_QUANTITY\",\n    \"START_QUOTING_DATE\",\n    \"END_QUOTING_DATE\",\n    \"EXPIRY_DATE\",\n    \"REGULATION_DATE\",\n    \"STOCK_CURRENCY\",\n    \"OFFICIAL_PRICE\",\n    \"FSI\",\n    \"UNDERLYING_DESCRIPTION\",\n    \"UNDERLYING_STOCK_CODE\",\n    \"UNDERLYING_MARKET_CODE\",\n    \"UNDERLYING_TYPE\",\n    \"UNDERLYING_ISIN\",\n    \"CALL_PUT\",\n    \"ISSUER_CODE\",\n    \"STRIKE_PRICE\",\n    \"VOLAT<PERSON>ITY\",\n    \"OPEN_INTEREST\",\n    \"SETTLEMENT_PRICE\",\n    \"SETTLEMENT_DATE\",\n    \"DERIVATIVES_GROUP\",\n    \"DERIVATIVES_CLASS\",\n    \"DERIVATIVES_TYPE\",\n    \"TICK_VALUE\",\n    \"ISSUER_CODE_DETAIL\",\n    \"NOMINAL_VALUE\",\n    \"ISSUE_PRICE\",\n    \"ISSUE_DATE\",\n    \"YEARLY_COUPON_DETAIL\",\n    \"YEARLY_RATE\",\n    \"MIN_COUPON\",\n    \"MAX_COUPON\",\n    \"AMORTIZATION_TYPE\",\n    \"AMORTIZATION_CERTO_PROBAB\",\n    \"AMORTIZATION_FREQUENCY\",\n    \"FIRST_EXTRACTION_DATE\",\n    \"FIRST_REFOUND_DATE\",\n    \"PERCENT_REFOUND\",\n    \"FISCAL_TREATMENT\",\n    \"TAX_RATE\",\n    \"DISCOUNT\",\n    \"IS_LISTED\",\n    \"IS_WARRANT\",\n    \"COUPON_ACCRUAL_BASIS\",\n    \"CAPITAL_ACCRUAL_BASIS\",\n    \"COUNTRY_ISSUER\",\n    \"WARRANT_TYPE\",\n    \"PRICE_OBB\",\n    \"TEL_QUEL_GROSS_PRICE\",\n    \"TEL_QUEL_NET_PRICE\",\n    \"NET_ACCRUAL\",\n    \"GROSS_ACCRUAL\",\n    \"DAYS_TO_EXP\",\n    \"COUPON\",\n    \"COUPON_TYPE_DETAIL\",\n    \"TREND_COUPON\",\n    \"GROSS_INTEREST_RATE\",\n    \"NET_INTEREST_RATE\",\n    \"GROSS_ISMA_INTEREST_RATE\",\n    \"NET_ISMA_INTEREST_RATE\",\n    \"GROSS_IMM_INTEREST_RATE\",\n    \"NET_IMM_INTEREST_RATE\",\n    \"DURATION_MAC\",\n    \"DURATION_MOD\",\n    \"CONVEXITY\",\n    \"ASSET_SWAP_SPREAD\",\n    \"LAST\",\n    \"VARIATION\",\n    \"LAST_TRADE_QUANTITY\",\n    \"TIME_LAST\",\n    \"BID_QTY_1\",\n    \"BID_PRICE_1\",\n    \"ASK_QTY_1\",\n    \"ASK_PRICE_1\",\n    \"MIN_PRICE\",\n    \"VARIATION_FROM_MIN\",\n    \"YEAR_MIN_PRICE\",\n    \"VARIATION_FROM_YEAR_MIN\",\n    \"MAX_PRICE\",\n    \"VARIATION_FROM_MAX\",\n    \"YEAR_MAX_PRICE\",\n    \"VARIATION_FROM_YEAR_MAX\",\n    \"REFERENCE_PRICE\",\n    \"VOLUME\",\n    \"COUNTERVALUE\",\n    \"NUMBER_OF_CONTRACTS\",\n    \"IMBALANCE\",\n    \"TREND\",\n    \"SPREAD\",\n    \"VWAP\",\n    \"PHASE\",\n    \"STATUS\",\n    \"ORDER_ENTRY_STATUS\",\n    \"RANDOM_AUCTION_TIME\",\n    \"OPEN_TIME\",\n    \"CLOSE_TIME\",\n    \"OPEN_PRICE\",\n    \"OPEN_VOLUME\",\n    \"OPEN_CONTRACTS\",\n    \"CLOSE_PRICE\",\n    \"CLOSE_VOLUME\",\n    \"CLOSE_CONTRACTS\"\n  ],\n  \"stocks\": [\n    {\n      \"MarketCode\": \"\",\n      \"StockCode\": \"\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockList3", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "# Baskets\n## FTSEMIB\n```\n\"code\": \"FTSEMIB\",\n\"extra_filters\": \"MKT|TipoRichiesta=STOCKLIST~TipoView=1~TipoQuery=BASKET~paniere=FTSEMIB~HasAfterhour=1\"\n```\n\n## IDEM\n```\n\"code\": \"IDEM\",\n\"extra_filters\": \"MKT|TipoRichiesta=STOCKLIST~TipoView=2~TipoQuery=MARKET_STOCKTYPE~mercato=IDEM~tipoTitolo=50~sottotipoTitolo=INDEXFUT\",\n```", "url": {"raw": "{{url}}/api/StockList/GetStockList3", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockList3"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"code\": \"IDEM\",\n  \"cols\": [\n    \"STOCK_BUY_SELL\",\n    \"MARKET_CODE\",\n    \"STOCK_CODE\",\n    \"LAST\",\n    \"VARIATION\",\n    \"VOLUME\",\n    \"BID_QTY_1\",\n    \"BID_PRICE_1\",\n    \"ASK_PRICE_1\",\n    \"ASK_QTY_1\",\n    \"MAX_PRICE\",\n    \"MIN_PRICE\",\n    \"TIME_LAST\",\n    \"EMPTY\"\n  ],\n  \"extra_filters\": \"MKT|TipoRichiesta=STOCKLIST~TipoView=2~TipoQuery=MARKET_STOCKTYPE~mercato=IDEM~tipoTitolo=50~sottotipoTitolo=INDEXFUT\",\n  \"filter\": null,\n  \"paging\": {\n    \"page\": 0,\n    \"pageSize\": 50\n  },\n  \"type\": 0,\n  \"sorting\": {\n    \"columnName\": \"LAST\",\n    \"direction\": 1\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockListByFilter2", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStockListByFilter2", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockListByFilter2"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"cols\": [\n    \"STOCK_BUY_SELL\",\n    \"MARKET_CODE\",\n    \"STOCK_CODE\",\n    \"STOCK_DESCRIPTION\",\n    \"LAST\",\n    \"VOLUME\",\n    \"BID_QTY_1\",\n    \"BID_PRICE_1\",\n    \"ASK_PRICE_1\",\n    \"ASK_QTY_1\",\n    \"MAX_PRICE\",\n    \"MIN_PRICE\",\n    \"TIME_LAST\"\n  ],\n  \"filter\": {\n    \"_searchFilterType\": 0,\n    \"_globalSearchObject\": {\n      \"_description\": \"Ftse Mib\",\n      \"_isin\": null,\n      \"_marketCode\": null,\n      \"_stockCode\": null,\n      \"_criteria\": 1,\n      \"_afterhour\": null\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockUserInfo", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStockUserInfo", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockUserInfo"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"marketCode\": \"\",\n  \"stockCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetStockUserInfo2", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/StockList/GetStockUserInfo", "protocol": "", "host": ["{{url}}"], "path": ["api", "StockList", "GetStockUserInfo"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"marketCode\": \"MOT\",\n  \"stockCode\": \"IT0005532715\"\n}", "options": {"raw": {"language": "json"}}}}}]}]}, {"name": "1-<PERSON><PERSON>", "item": [{"name": "FederationLogin", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/Federation/Bansel", "protocol": "", "host": ["{{url}}"], "path": ["Federation", "Bansel"], "query": [], "variable": []}}}, {"name": "LoginOld", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "disabled": false, "type": "default"}, {"key": "xhrFields", "value": "", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/Account/LoginOld", "protocol": "", "host": ["{{url}}"], "path": ["Account", "LoginOld"], "query": [], "variable": []}, "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "alias", "value": "1", "disabled": false, "type": "default"}, {"key": "password", "value": "1", "disabled": false, "type": "default"}]}}}, {"name": "Platform", "item": [{"name": "PrestartChecks", "event": [], "request": {"method": "POST", "header": [{"key": "app-version", "value": "2.0.31", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Security/PrestartChecks", "protocol": "", "host": ["{{url}}"], "path": ["api", "Security", "PrestartChecks"], "query": [], "variable": []}}}, {"name": "TokenCheck", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Security/TokenCheck", "protocol": "", "host": ["{{url}}"], "path": ["api", "Security", "TokenCheck"], "query": [], "variable": []}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Security/UrlCheck", "protocol": "", "host": ["{{url}}"], "path": ["api", "Security", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"username\": \"464459\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "API", "item": [{"name": "Dummy", "event": [{"listen": "prerequest", "script": {"exec": ["var ibcode = req.getBody().UserName;", "bru.setEnvVar(\"ibcode\", ibcode);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/DummyLoginApi", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"UserName\": \"464459\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Logout", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/LogoutApi", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "LogoutApi"], "query": [], "variable": []}}}, {"name": "LoginAPI", "item": [{"name": "Login Step 0", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/LoginApi", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "Login<PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{ }", "options": {"raw": {"language": "json"}}}}}, {"name": "Login Step 1", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/LoginApi", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "Login<PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Step\": 1,\n  \"AuthId\": \"\",\n  \"Username\": \"\",\n  \"Password\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Login Step 2", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "#### ApiLoginStep\n|Value|Step|\n|---|---|\n|0|FirstStep|\n|1|UserPassword|\n|2|PasswordChars|\n|3|Birthday|\n|4|Token|\n|90|DummyLogin|\n|100|Ok|\n", "url": {"raw": "{{url}}/api/Account/LoginApi", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "Login<PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"Step\": 4,\n  \"Chars\": [\n    2,\n    3\n  ],\n  \"AuthId\": \"8f5b9a84-742e-45e2-8b49-e9498b575bd3\",\n  \"Char1\": \"5\",\n  \"Char2\": \"5\",\n  \"Token\": \"111111\"\n}", "options": {"raw": {"language": "json"}}}}}]}]}, {"name": "APP", "item": [{"name": "ExternalCalls", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/ExternalCalls/SellaTraderLogin", "protocol": "", "host": ["{{url}}"], "path": ["api", "ExternalCalls", "SellaTrader<PERSON><PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"brokerName\": 2,\n  \"authParams\": {\n    \"_cnctr_access_token\": \"c9c327c1-d344-4486-a0f6-99a1290583d4-4f50454e544f4c5f574542\",\n    \"_user\": \"581216\"\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ExternalCallsDummy", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "", "protocol": "", "host": [], "path": [], "query": [], "variable": []}}}, {"name": "SSO_0", "event": [], "request": {"method": "POST", "header": [{"key": "X-Cnctr-Channel-Id", "value": "OPENTOL_WEB", "disabled": false, "type": "default"}, {"key": "X-Cnctr-Device-Id", "value": "SELLA_TRADER_MOBILE", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "https://www.sella.pre/sso/rest/api/connector/access_token", "protocol": "https", "host": ["www", "sella", "pre"], "path": ["sso", "rest", "api", "connector", "access_token"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}, {"name": "SSO_1", "event": [], "request": {"method": "POST", "header": [{"key": "X-Cnctr-Channel-Id", "value": "OPENTOL_WEB", "disabled": false, "type": "default"}, {"key": "X-Cnctr-Device-Id", "value": "SELLA_TRADER_MOBILE", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "https://www.sella.pre/sso/rest/api/connector/access_token", "protocol": "https", "host": ["www", "sella", "pre"], "path": ["sso", "rest", "api", "connector", "access_token"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"auth_id\": \"AQIC5wM2LY4SfcyeXm9OkMnMRayvetxdoSKEaI3sNzf55u0.*AAJTSQACMDIAAlNLABM4MTU2MTA2NDY1MDMwMDQ2OTM1AAJTMQACMDk.*\",\n    \"callback_input\": [\n      {\n        \"key\": \"USERNAME\",\n        \"value\": \"464459\"\n      },\n      {\n        \"key\": \"PASSWORD\",\n        \"value\": \"1111\"\n      }\n    ]\n  }", "options": {"raw": {"language": "json"}}}}}, {"name": "SSO_2", "event": [], "request": {"method": "POST", "header": [{"key": "X-Cnctr-Channel-Id", "value": "OPENTOL_WEB", "disabled": false, "type": "default"}, {"key": "X-Cnctr-Device-Id", "value": "SELLA_TRADER_MOBILE", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "https://www.sella.pre/sso/rest/api/connector/access_token", "protocol": "https", "host": ["www", "sella", "pre"], "path": ["sso", "rest", "api", "connector", "access_token"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n    \"auth_id\": \"AQIC5wM2LY4SfcyVrbNuUfGs8Ux70s6-bCPkFtVd1l0wsdM.*AAJTSQACMDIAAlNLABMxNjI0NDA0NzE0NzUzNjAwMzUzAAJTMQACMDk.*\",\n    \"callback_input\": [\n      {\n        \"key\": \"USERNAME\",\n        \"value\": \"464459\"\n      },\n      {\n        \"key\": \"PASSWORD\",\n        \"value\": \"1111\"\n      }\n    ]\n  }", "options": {"raw": {"language": "json"}}}}}]}, {"name": "WEB", "item": [{"name": "LoginOld", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/Account/LoginOld?alias=<EMAIL>&password=12345", "protocol": "", "host": ["{{url}}"], "path": ["Account", "LoginOld?alias=<EMAIL>&password=12345"], "query": [{"key": "alias", "value": "<EMAIL>"}, {"key": "password", "value": "12345"}], "variable": []}, "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "alias", "value": "<EMAIL>", "disabled": true, "type": "default"}, {"key": "password", "value": "12345", "disabled": true, "type": "default"}]}}}, {"name": "Logout", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/Account/Logout", "protocol": "", "host": ["{{url}}"], "path": ["Account", "Logout"], "query": [], "variable": []}}}, {"name": "Register", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/Account/Register", "protocol": "", "host": ["{{url}}"], "path": ["Account", "Register"], "query": [], "variable": []}, "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "alias", "value": "CustodyAgent", "disabled": false, "type": "default"}, {"key": "email", "value": "CustodyAgent@sella", "disabled": false, "type": "default"}, {"key": "password", "value": "3aa1b911-22db-49d3", "disabled": false, "type": "default"}, {"key": "confirmPassword", "value": "3aa1b911-22db-49d3", "disabled": false, "type": "default"}]}}}]}]}, {"name": "Brokers", "item": [{"name": "BrokerProperties", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/BrokerProperties?broker=2", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "BrokerProperties?broker=2"], "query": [{"key": "broker", "value": "2"}], "variable": []}}}, {"name": "GetAccounts", "event": [], "request": {"method": "POST", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Order/GetAccounts", "protocol": "", "host": ["{{url}}"], "path": ["api", "Order", "GetAccounts"], "query": [], "variable": []}}}, {"name": "LoggedBrokers", "event": [], "request": {"method": "GET", "header": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Account/LoggedBrokers", "protocol": "", "host": ["{{url}}"], "path": ["api", "Account", "LoggedBrokers"], "query": [], "variable": []}}}, {"name": "VB", "item": [{"name": "<PERSON><PERSON>", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Security/Login", "protocol": "", "host": ["{{url}}"], "path": ["api", "Security", "<PERSON><PERSON>"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{\n  \"brokerName\": 1,\n  \"authParams\": {}\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Logout", "event": [], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "disabled": false, "type": "default"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "description": "", "url": {"raw": "{{url}}/api/Security/Logout", "protocol": "", "host": ["{{url}}"], "path": ["api", "Security", "Logout"], "query": [], "variable": []}, "body": {"mode": "raw", "raw": "{ \"brokerName\": 1 }", "options": {"raw": {"language": "json"}}}}}]}]}]}], "variable": [{"key": "url", "value": "", "type": "default"}, {"key": "ibcode", "value": "", "type": "default"}, {"key": "Date1", "value": "", "type": "default"}, {"key": "Date2", "value": "", "type": "default"}, {"key": "Data1", "value": "", "type": "default"}]}