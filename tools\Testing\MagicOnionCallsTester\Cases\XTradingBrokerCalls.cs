﻿using MagicOnionCallsTester.Helpers;
using MagicOnionCallsTester.Models.Customer;
using Microsoft.Extensions.Logging;
using OT.Common.Broker.Grpc;
using OT.Common.Broker.Grpc.V2;
using OT.Common.Broker.Models;
using OT.Common.Broker.Models.Auth;
using OT.Common.Broker.Models.Balance;
using OT.Common.Broker.Models.Documents;
using OT.Common.Broker.Models.Messages.Notices;
using OT.Common.Broker.Models.Operations;
using OT.Common.Broker.Models.Operations.Result;
using OT.Common.Broker.Models.Orders.Parameters;
using OT.Common.Broker.Models.Portfolio.Parameters;
using System.Text;
using System.Text.Json;

namespace MagicOnionCallsTester.Cases
{
    public class XTradingBrokerCalls : BaseCalls
    {
        private readonly MagicOnionClient<IBrokerAuthService> _authService;
        private readonly MagicOnionClient<IBrokerServiceV2> _brokerService;
        private readonly MagicOnionClient<IBrokerDocumentService> _documentService;
        private readonly MagicOnionClient<IBrokerMessageService> _messageService;
        private readonly MagicOnionClient<IBrokerBalanceService> _balanceService;

        private readonly ILogger _logger;

        private readonly BrokerCustomerCredentials _customerCredentials;

        public XTradingBrokerCalls(
            BrokerCustomerCredentials customerCredentials,
            ILogger logger
            ) : base()
        {
            _customerCredentials = customerCredentials;
            _logger = logger;

            _brokerService = new MagicOnionClient<IBrokerServiceV2>("localhost", 6009);
            _authService = new MagicOnionClient<IBrokerAuthService>("localhost", 6009);
            _documentService = new MagicOnionClient<IBrokerDocumentService>("localhost", 6009);
            _messageService = new MagicOnionClient<IBrokerMessageService>("localhost", 6009);
            _balanceService = new MagicOnionClient<IBrokerBalanceService>("localhost", 6009);
        }

        public XTradingBrokerCalls Login()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Starting] Login");
                await _authService.Client.Login(new AuthParams()
                {
                    OTAccessToken = _customerCredentials.OTAccessToken,
                    UseDummy = true,
                    User = _customerCredentials.Username
                }).ResponseAsync;
                _logger.LogInformation("[Login] Done");
            });

            return this;
        }

        public XTradingBrokerCalls InsertOrder(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[InsertOrder] Starting");
                InsertOrderParameters par = JsonSerializer.Deserialize<InsertOrderParameters>(json)!;

                par.AccessToken = _customerCredentials.OTAccessToken;
                par.CustomerID = _customerCredentials.CustomerId;

                _logger.LogInformation("Parametere Deserialized");
                var res = await _brokerService.Client.InsertOrder(par).ResponseAsync;

                if (res.ErrorCode == ErrorCode.Ok || res.ErrorCode == ErrorCode.Undefined)
                {
                    _logger.LogInformation("[InsertOrder] Starting ConfirmOrder");
                    await _brokerService.Client.ConfirmInsertOrder(new ConfirmOrderParameters()
                    {
                        BrokerName = BrokerName.Sella,
                        AccessToken = _customerCredentials.OTAccessToken,
                        CustomerID = _customerCredentials.CustomerId,
                        OrderID = res.OrderID
                    }).ResponseAsync;
                    _logger.LogInformation("[InsertOrder] Done ConfirmOrder");
                }
                else
                {
                    _logger.LogWarning("[InsertOrder] Error: {e}", string.Join(';', res.BrokerMessages));
                }
                _logger.LogInformation("[InsertOrder] Done");
            });

            return this;
        }
        public XTradingBrokerCalls GetOrderStatus(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[GetOrderStatus] Starting");
                _logger.LogDebug("[GetOrderStatus] Input: {j}", json);

                OrderStatusFilter filter;
                if (!string.IsNullOrEmpty(json))
                {
                    filter = JsonSerializer.Deserialize<OrderStatusFilter>(json)!;
                }
                else
                {
                    filter = new OrderStatusFilter()
                    {
                        FromDate = DateTime.Today.AddDays(-1),
                        ToDate = DateTime.Today
                    };
                }

                filter.BrokerName = BrokerName.Sella;
                filter.AccessToken = _customerCredentials.OTAccessToken;
                filter.CustomerID = _customerCredentials.CustomerId;

                var result = await _brokerService.Client.GetOrderStatus(filter).ResponseAsync;

                StringBuilder sb = new StringBuilder();

                if (result.Success)
                {
                    foreach (var item in result.Orders)
                    {
                        sb.AppendLine(item.GetFlowData(false).ToString());
                    }
                    _logger.LogInformation("[GetOrderStatus] Result: \n{r}", sb.ToString());
                }


                _logger.LogInformation("[GetOrderStatus] Done");
            });

            return this;
        }
        public XTradingBrokerCalls GetOrderDetail(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[GetOrderDetail] Starting");
                _logger.LogDebug("[GetOrderDetail] Input: {j}", json);

                OrderDetailFilter filter;
                if (!string.IsNullOrEmpty(json))
                {
                    filter = JsonSerializer.Deserialize<OrderDetailFilter>(json)!;
                }
                else
                {
                    filter = new OrderDetailFilter()
                    {
                        OrderCode = "20240904200002",
                    };
                }

                filter.BrokerName = BrokerName.Sella;
                filter.CustomerID = _customerCredentials.CustomerId;
                filter.AccessToken = _customerCredentials.OTAccessToken;

                var result = await _brokerService.Client.GetOrderDetail(filter).ResponseAsync;

                if (result != null)
                {
                    _logger.LogInformation("[GetOrderDetail] Result: \n{fd}", result.GetFlowData(false).ToString());
                }


                _logger.LogInformation("[GetOrderDetail] Done");
            });

            return this;
        }
        public XTradingBrokerCalls GetPortfolio(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[GetPortfolio] Starting");
                _logger.LogDebug("[GetPortfolio] Input {j}", json);

                PortfolioFilter filter;
                if (!string.IsNullOrEmpty(json))
                {
                    filter = JsonSerializer.Deserialize<PortfolioFilter>(json)!;
                }
                else
                {
                    filter = new PortfolioFilter()
                    {
                        RefreshCache = true,
                    };
                }

                filter.BrokerName = BrokerName.Sella;
                filter.CustomerID = _customerCredentials.CustomerId;
                filter.AccessToken = _customerCredentials.OTAccessToken;

                var result = await _brokerService.Client.GetPortfolio(filter).ResponseAsync;
                _logger.LogInformation("[GetPortfolio] Done");
            });

            return this;
        }

        public XTradingBrokerCalls TestGeneric()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Generic] Starting");

                // NON COMMITTARE
                // ...

                _logger.LogInformation("[Generic] Done");
            });

            return this;
        }


        // TODO fix
        public XTradingBrokerCalls GetInformativeNotes(string json)
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetOrderStatus with {j}", json);

                InformativeNoteSearchParameters filter;
                if (!string.IsNullOrEmpty(json))
                {
                    filter = JsonSerializer.Deserialize<InformativeNoteSearchParameters>(json)!;
                    filter.AccessToken = "MANUALTEST";
                }
                else
                {
                    filter = new InformativeNoteSearchParameters()
                    {
                        AccessToken = "MANUALTEST",
                        Broker = BrokerName.Sella,
                        BondAcctId = null,
                        CashAcctId = null,
                        DossierId = null,
                        DocumentType = DocumentTypeSella.NotesInfo,
                        MarketCode = null,
                        CustomerCode = null,
                        DateFrom = new DateTime(2024, 3, 6),
                        DateTo = new DateTime(2024, 7, 24),
                        ClientInfo = new ClientInfo()
                        {
                            ChannelType = ChannelType.OPENTOL,
                            DeviceType = DeviceType.OpenTol
                        }
                    };
                }

                var result = _documentService.Client.SearchInformativeNote(filter).ResponseAsync.Result;
                _logger.LogInformation("Done GetOrderStatus");
            });

            return this;
        }
        public XTradingBrokerCalls NormalizePosition(string json)
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting NormalizePosition with {j}", json);

                NormalizePositionParameters par;
                if (!string.IsNullOrEmpty(json))
                {
                    par = JsonSerializer.Deserialize<NormalizePositionParameters>(json)!;
                    par.AccessToken = "MANUALTEST";
                }
                else
                {
                    par = new NormalizePositionParameters()
                    {
                        AccessToken = "MANUALTEST",
                        Broker = BrokerName.Sella,
                        PortfolioId = 502610155,
                        QtyToNormalize = 3,
                        Type = NormalizationType.Leverage,
                        ClientInfo = new ClientInfo()
                        {
                            ChannelType = ChannelType.OPENTOL,
                            DeviceType = DeviceType.OpenTol
                        }
                    };
                }

                var result = _brokerService.Client.NormalizePosition(par).ResponseAsync.Result;
                _logger.LogInformation("Done NormalizePosition");
            });

            return this;
        }
        public XTradingBrokerCalls OptionsCalculator(string json)
        {
            //_startingTask.ContinueWith(t =>
            //{
            //    _logger.LogInformation("Starting OptionsCalculator with {j}", json);

            //    OptionsCalculatorParameters par;
            //    if (!string.IsNullOrEmpty(json))
            //    {
            //        par = JsonSerializer.Deserialize<OptionsCalculatorParameters>(json);
            //        par.AccessToken = "MANUALTEST";
            //    }
            //    else
            //    {
            //        par = new OptionsCalculatorParameters()
            //        {
            //            AccessToken = "MANUALTEST",
            //            Broker = OT.Hello.Base.BrokerName.Sella,
            //            MarketCode = "IDEM",
            //            StockCode = "FIB0325",
            //            CustomerCode = "BB1023",
            //            PriceLimit = "34610",
            //            AccountCurrencyCode = "EUR",
            //            ClientInfo = new SharedConfiguration.Models.ClientInfo()
            //            {
            //                ChannelType = SharedConfiguration.Enums.ChannelType.OPENTOL,
            //                DeviceType = SharedConfiguration.Enums.DeviceType.OpenTol
            //            }
            //        };
            //    }

            //    var result = _brokerService.Client.OptionCalculator(par).ResponseAsync.Result;
            //    _logger.LogInformation("Done OptionsCalculator");
            //});

            return this;
        }
        public XTradingBrokerCalls GetMessages()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetMessages");

                var result = _messageService.Client.GetMessages("MANUALTEST", "0.0.0.0").ResponseAsync.Result;
                _logger.LogInformation("Done GetMessages");

                StringBuilder sb = new StringBuilder();
                foreach (var message in result)
                {
                    sb.Append($"{message.GetFlowData()}");
                }

                _logger.LogInformation($"GetMessages result: \n{sb.ToString()}");
            });

            return this;
        }
        public XTradingBrokerCalls GetAdvice()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetAdvice");

                var result = _messageService.Client.GetAdvices(new AdviceParameters()
                {
                    AccessToken = "MANUALTEST",
                    Broker = BrokerName.Sella,
                    CustomerId = 1038,

                    FromDate = DateTime.Today.AddDays(-1),
                    ToDate = DateTime.Today,
                    ClientInfo = new ClientInfo()
                    {
                        IpAddress = "0.0.0.0"
                    }

                }).ResponseAsync.Result;
                _logger.LogInformation("Done GetAdvice");

                StringBuilder sb = new StringBuilder();
                foreach (var message in result)
                {
                    sb.Append($"{message.GetFlowData()}");
                }

                _logger.LogInformation("GetAdvice result: \n{r}", sb.ToString());
            });

            return this;
        }
        public XTradingBrokerCalls GetBalance()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetBalance");

                var result = _balanceService.Client.GetBalance(new BalanceParam()
                {
                    BrokerName = BrokerName.Sella,
                    AccessToken = "MANUALTEST",
                    CustomerId = 1165,
                    CashAccountId = ********,  //  { 78711: ******** } { 464459: 895247 }
                    ClientInfo = new ClientInfo()
                }).ResponseAsync.Result;

                _logger.LogInformation("Balance: ");
                _logger.LogInformation("Done GetBalance");
            });

            return this;
        }
        public XTradingBrokerCalls GetTitoliDestinati()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetTitoliDestinati");

                var result = _balanceService.Client.GetTitoliDestinati(new TitoliDestinatiParameters()
                {
                    Broker = BrokerName.Sella,
                    AccessToken = _customerCredentials.OTAccessToken,
                    CustomerId = _customerCredentials.CustomerId,
                    CashAccountId = 6315198,
                    BondAccountId = 6315203,
                    ClientInfo = new ClientInfo()
                }).ResponseAsync.Result;

                _logger.LogInformation("Balance: ");
                _logger.LogInformation("Done GetTitoliDestinati");
            });

            return this;
        }
        //public XTradingBrokerCalls GetAccountDetails()
        //{
        //    _startingTask.ContinueWith(t =>
        //    {
        //        _logger.LogInformation("Starting GetBalanceDetails");

        //        var result = _balanceService.Client.GetAccountLiquidityDetails(new AccountLiquidityDetailsParameters()
        //        {
        //            Broker = BrokerName.Sella,
        //            AccessToken = "MANUALTEST",
        //            CustomerId = 1165,
        //            CashAccountId = 895247,  //  { 78711: ******** } { 464459: 895247 }
        //            Currency = "EUR",
        //            ClientInfo = new ClientInfo()
        //        }).ResponseAsync.Result;

        //        _logger.LogInformation("Balance: ");
        //        _logger.LogInformation("Done GetBalanceDetails");
        //    });

        //    return this;
        //}
        public XTradingBrokerCalls GetBalanceDetails()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetBalanceDetails");

                var result = _balanceService.Client.GetBalanceDetails(new BalanceDetailParameters()
                {
                    BrokerName = BrokerName.Sella,
                    AccessToken = "MANUALTEST",
                    CustomerId = 1165,
                    CashAccountId = 895247,  //  { 78711: ******** } { 464459: 895247 }
                    Currency = "EUR",
                    ClientInfo = new ClientInfo()
                }).ResponseAsync.Result;

                _logger.LogInformation("Balance: ");
                _logger.LogInformation("Done GetBalanceDetails");
            });

            return this;
        }
        public XTradingBrokerCalls SetLiquidity()
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting SetLiquidity");

                var result = _balanceService.Client.SetLiquidity(new LiquidityParam()
                {
                    BrokerName = BrokerName.Sella,
                    AccessToken = "MANUALTEST",
                    CustomerId = 1031,
                    CashAccountId = 895247,
                    LiquidityValue = ************,
                    ClientInfo = new ClientInfo()
                }).ResponseAsync.Result;

                _logger.LogInformation("Balance: ");
                _logger.LogInformation("Done SetLiquidity");
            });

            return this;
        }
        public XTradingBrokerCalls TestConsiglio()
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting Generic");

                _brokerService.Client.ElaboraConsiglio(new ElaboraConsiglioParameters()
                {
                    Broker = BrokerName.Sella,
                    AccessToken = _customerCredentials.OTAccessToken,
                    CustomerId = _customerCredentials.CustomerId,
                    ClientInfo = new ClientInfo(),

                    IdConsiglio = "15061DB91D05A02CD42FD0F7CDB1ADF250C8EB900CF0167A975EB5459C660C216404D805909FA90371BDFD6C89CB802A1617C52F54A982CBF58E59A2CA9A6956C12F56BA9FEABEE67C9C5D8263039D2D"
                }).ResponseAsync.Wait();

                _logger.LogInformation("Done Generic");
            });

            return this;
        }
    }
}
