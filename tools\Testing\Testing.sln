Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MagicOnionCallsTester", "MagicOnionCallsTester\MagicOnionCallsTester.csproj", "{038D4B3C-6AF4-466F-42D8-B6DDC61D9AA5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestAutomations", "TestAutomations\TestAutomations.csproj", "{98117F67-E538-41E4-E722-D00822CFC3CF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{038D4B3C-6AF4-466F-42D8-B6DDC61D9AA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{038D4B3C-6AF4-466F-42D8-B6DDC61D9AA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{038D4B3C-6AF4-466F-42D8-B6DDC61D9AA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{038D4B3C-6AF4-466F-42D8-B6DDC61D9AA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{98117F67-E538-41E4-E722-D00822CFC3CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98117F67-E538-41E4-E722-D00822CFC3CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98117F67-E538-41E4-E722-D00822CFC3CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98117F67-E538-41E4-E722-D00822CFC3CF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {13B0139A-2834-40CD-A781-1056F068C745}
	EndGlobalSection
EndGlobal
