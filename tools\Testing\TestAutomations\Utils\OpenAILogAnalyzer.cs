using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;

namespace TestAutomations.Utils
{
    /// <summary>
    /// Classe per l'analisi dei log utilizzando l'API di OpenAI
    /// </summary>
    public class OpenAILogAnalyzer
    {
        private readonly HttpClient _httpClient;
        private readonly string _openAiApiKey;
        private readonly string _openAiModel;
        private readonly int _maxTokens;
        private readonly string _logDir = "logs";

        public OpenAILogAnalyzer()
        {
            _httpClient = new HttpClient();
            _openAiApiKey = GetConfigValue("OPENAI_API_KEY");
            _openAiModel = GetConfigValue("OPENAI_MODEL") ?? "gpt-3.5-turbo";
            _maxTokens = int.TryParse(GetConfigValue("OPENAI_MAX_TOKENS"), out int tokens) ? tokens : 1000;
            
            // Crea la directory dei log se non esiste
            Directory.CreateDirectory(_logDir);
        }

        /// <summary>
        /// Analizza i log utilizzando OpenAI API
        /// </summary>
        /// <param name="logs">Contenuto dei log da analizzare</param>
        /// <param name="prompt">Prompt personalizzato per l'analisi (opzionale)</param>
        /// <returns>Analisi dei log o messaggio di errore</returns>
        public async Task<string> AnalyzeLogsAsync(string logs, string prompt = null)
        {
            if (string.IsNullOrEmpty(_openAiApiKey))
            {
                return "API key OpenAI non configurata. Imposta la variabile d'ambiente OPENAI_API_KEY o aggiungi OPENAI_API_KEY in secrets.json.";
            }

            if (string.IsNullOrEmpty(logs))
            {
                return "Nessun log da analizzare.";
            }

            // Limita la dimensione dei log per evitare di superare i limiti dell'API
            const int maxLogLength = 12000; // Aumentato per supportare più contesto
            if (logs.Length > maxLogLength)
            {
                logs = logs.Substring(logs.Length - maxLogLength);
                logs = $"[Log troncati a {maxLogLength} caratteri...]\n\n" + logs;
            }

            // Prompt predefinito se non specificato
            if (string.IsNullOrEmpty(prompt))
            {
                prompt = "Analizza questi log e identifica eventuali errori o problemi. Fornisci un riassunto dei problemi trovati e possibili soluzioni. Se non ci sono errori evidenti, indica che tutto sembra funzionare correttamente.";
            }

            try
            {
                var requestBody = new
                {
                    model = _openAiModel,
                    messages = new[]
                    {
                        new { role = "system", content = "Sei un esperto analista di log che aiuta a identificare e risolvere problemi nei sistemi software. Sei specializzato nell'analisi di log Kubernetes e applicazioni .NET Core. Fornisci risposte concise e pratiche." },
                        new { role = "user", content = $"{prompt}\n\nLOG:\n{logs}" }
                    },
                    max_tokens = _maxTokens,
                    temperature = 0.3 // Valore basso per risposte più deterministiche e precise
                };

                var content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _openAiApiKey);

                var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    return $"Errore API OpenAI: {responseBody}";
                }

                var responseJson = JsonDocument.Parse(responseBody);
                var choices = responseJson.RootElement.GetProperty("choices");
                if (choices.GetArrayLength() > 0)
                {
                    var message = choices[0].GetProperty("message");
                    var analysisContent = message.GetProperty("content").GetString();
                    
                    // Salva l'analisi su file
                    SaveAnalysisToFile(analysisContent, "openai_analysis");
                    
                    return analysisContent;
                }

                return "Nessuna analisi disponibile.";
            }
            catch (Exception ex)
            {
                return $"Errore durante l'analisi dei log: {ex.Message}";
            }
        }

        /// <summary>
        /// Analizza i log di un deployment Kubernetes
        /// </summary>
        /// <param name="deployment">Nome del deployment (default: cacheprovider)</param>
        /// <param name="ns">Namespace (default: ot)</param>
        /// <param name="prompt">Prompt personalizzato per l'analisi (opzionale)</param>
        /// <param name="tailLines">Numero di righe da recuperare (default: 1000)</param>
        /// <param name="sinceSeconds">Recupera log degli ultimi N secondi (default: null)</param>
        /// <returns>Analisi dei log o messaggio di errore</returns>
        public async Task<string> AnalyzeKubernetesLogsAsync(
            string deployment = "cacheprovider", 
            string ns = "ot", 
            string prompt = null, 
            int tailLines = 1000, 
            int? sinceSeconds = null)
        {
            try
            {
                Console.WriteLine($"Recupero log dal deployment {deployment} nel namespace {ns}...");
                var logs = await KubernetesLogFetcher.FetchLatestLogsAsync(deployment, ns, tailLines, sinceSeconds);
                
                if (logs.StartsWith("Errore"))
                {
                    return logs;
                }

                Console.WriteLine($"Analisi dei log in corso (lunghezza: {logs.Length} caratteri)...");
                return await AnalyzeLogsAsync(logs, prompt);
            }
            catch (Exception ex)
            {
                return $"Errore durante l'analisi dei log Kubernetes: {ex.Message}";
            }
        }

        /// <summary>
        /// Analizza i log di più deployment Kubernetes e fornisce un'analisi aggregata
        /// </summary>
        /// <param name="deployments">Lista di deployment da analizzare</param>
        /// <param name="ns">Namespace (default: ot)</param>
        /// <param name="prompt">Prompt personalizzato per l'analisi (opzionale)</param>
        /// <param name="tailLines">Numero di righe da recuperare per ogni deployment (default: 500)</param>
        /// <param name="sinceSeconds">Recupera log degli ultimi N secondi (default: 3600 = 1 ora)</param>
        /// <returns>Analisi aggregata dei log o messaggio di errore</returns>
        public async Task<string> AnalyzeMultipleKubernetesLogsAsync(
            List<string> deployments,
            string ns = "ot",
            string prompt = null,
            int tailLines = 500,
            int? sinceSeconds = 3600)
        {
            if (deployments == null || deployments.Count == 0)
            {
                return "Nessun deployment specificato per l'analisi.";
            }

            try
            {
                var allLogs = new StringBuilder();
                
                foreach (var deployment in deployments)
                {
                    Console.WriteLine($"Recupero log dal deployment {deployment} nel namespace {ns}...");
                    var logs = await KubernetesLogFetcher.FetchLatestLogsAsync(deployment, ns, tailLines, sinceSeconds);
                    
                    if (!logs.StartsWith("Errore"))
                    {
                        allLogs.AppendLine($"=== LOGS DA {deployment.ToUpper()} ===");
                        allLogs.AppendLine(logs);
                        allLogs.AppendLine();
                    }
                    else
                    {
                        allLogs.AppendLine($"=== ERRORE RECUPERO LOGS DA {deployment.ToUpper()} ===");
                        allLogs.AppendLine(logs);
                        allLogs.AppendLine();
                    }
                }

                if (allLogs.Length == 0)
                {
                    return "Nessun log recuperato dai deployment specificati.";
                }

                // Prompt personalizzato per l'analisi aggregata
                string aggregatePrompt = prompt ?? 
                    $"Analizza i log dei seguenti deployment Kubernetes: {string.Join(", ", deployments)}. " +
                    "Identifica eventuali errori o problemi e le possibili correlazioni tra i problemi nei diversi servizi. " +
                    "Fornisci un riassunto dei problemi trovati e possibili soluzioni.";

                Console.WriteLine($"Analisi dei log aggregati in corso (lunghezza: {allLogs.Length} caratteri)...");
                return await AnalyzeLogsAsync(allLogs.ToString(), aggregatePrompt);
            }
            catch (Exception ex)
            {
                return $"Errore durante l'analisi aggregata dei log Kubernetes: {ex.Message}";
            }
        }

        /// <summary>
        /// Salva l'analisi su file
        /// </summary>
        /// <param name="analysis">Contenuto dell'analisi</param>
        /// <param name="prefix">Prefisso per il nome del file</param>
        private void SaveAnalysisToFile(string analysis, string prefix)
        {
            try
            {
                var file = Path.Combine(_logDir, $"{prefix}_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                File.WriteAllText(file, analysis);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Errore durante il salvataggio dell'analisi su file: {ex.Message}");
            }
        }

        /// <summary>
        /// Ottiene un valore di configurazione da IConfiguration o variabili d'ambiente
        /// </summary>
        private string GetConfigValue(string key)
        {
            // Prova prima da IConfiguration
            var config = Program.Configuration;
            if (config != null)
            {
                var value = config[key];
                if (!string.IsNullOrEmpty(value))
                {
                    return value;
                }
            }
            
            // Fallback su variabili d'ambiente
            return Environment.GetEnvironmentVariable(key);
        }
    }
}
