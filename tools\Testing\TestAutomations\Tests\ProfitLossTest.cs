using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class ProfitLossTest : TestBase
    {
        private readonly string _username;
        private readonly string _password;
        private OTTradingClient _client;
        private readonly string _market;
        private readonly string _stock;
        private readonly int _days;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        public ProfitLossTest(string name, bool isDummy = false) : base(name, isDummy)
        {
            // O<PERSON>eni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            _market = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "XNAS";
            _stock = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "MSFT";
            _days = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_DAYS"), out var d) ? d : 30;

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test di profitti e perdite");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // Implementazione del test di profitti e perdite

                // 1. Recupera lo stato del portafoglio attuale
                var currentPortfolio = await GetPortfolioStatusAsync();
                LogTestStep("Recupero Portafoglio", true, "Stato del portafoglio recuperato");
                _testSteps.Add(new TestStepResult("Recupero Portafoglio", true, "Stato del portafoglio recuperato"));

                // 2. Recupera i profitti e le perdite giornaliere
                var dailyPL = await GetDailyProfitLossAsync();
                LogTestStep("Recupero P&L Giornaliero", true, "Profitti e perdite giornalieri recuperati");
                _testSteps.Add(new TestStepResult("Recupero P&L Giornaliero", true, "Profitti e perdite giornalieri recuperati"));

                // 3. Recupera i profitti e le perdite storici (ultimi 30 giorni)
                var historicalPL = await GetHistoricalProfitLossAsync(_days);
                LogTestStep("Recupero P&L Storico", true, $"Profitti e perdite storici degli ultimi {_days} giorni recuperati");
                _testSteps.Add(new TestStepResult("Recupero P&L Storico", true, $"Profitti e perdite storici degli ultimi {_days} giorni recuperati"));

                // 4. Recupera i profitti e le perdite per singolo titolo
                var stockPL = await GetStockProfitLossAsync(_market, _stock);
                LogTestStep("Recupero P&L Titolo", true, $"Profitti e perdite per il titolo {_stock} recuperati");
                _testSteps.Add(new TestStepResult("Recupero P&L Titolo", true, $"Profitti e perdite per il titolo {_stock} recuperati"));

                // 5. Verifica la consistenza dei dati
                if (!VerifyProfitLossConsistency(currentPortfolio, dailyPL, historicalPL, stockPL))
                {
                    LogTestStep("Verifica Consistenza", false, "I dati di profitti e perdite non sono consistenti");
                    _testSteps.Add(new TestStepResult("Verifica Consistenza", false, "I dati di profitti e perdite non sono consistenti"));
                    return false;
                }
                LogTestStep("Verifica Consistenza", true, "I dati di profitti e perdite sono consistenti");
                _testSteps.Add(new TestStepResult("Verifica Consistenza", true, "I dati di profitti e perdite sono consistenti"));

                Logger.Info("Test di profitti e perdite completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("ProfitLoss", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("ProfitLoss", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("ProfitLoss", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("ProfitLoss", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        private async Task<JsonElement> GetPortfolioStatusAsync()
        {
            try
            {
                var response = await _client.GetPortfolioAsync((int)BrokerName.Sella);
                return response;
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero Portafoglio", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero Portafoglio", false, $"Errore: {ex.Message}"));
                throw;
            }
        }

        private async Task<JsonElement> GetDailyProfitLossAsync()
        {
            try
            {
                // Ottieni i dati di Profit & Loss
                var request = new
                {
                    filter = new
                    {
                        _brokerName = "Sella",
                        _profitLossType = 1, // Daily
                        _fromDate = DateTime.Today.AddDays(-30).ToString("yyyy-MM-dd"),
                        _toDate = DateTime.Today.ToString("yyyy-MM-dd")
                    },
                    cols = new[] { "STOCK_CODE", "MARKET_CODE", "PROFIT_LOSS", "PERCENTAGE", "DATE" },
                    paging = new { pageSize = 100, pageIndex = 0 }
                };

                var response = await _client.SendRequestAsync("POST", "api/Order/GetProfitLoss", request);
                return response;
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero P&L Giornaliero", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero P&L Giornaliero", false, $"Errore: {ex.Message}"));
                throw;
            }
        }

        private async Task<JsonElement> GetHistoricalProfitLossAsync(int days)
        {
            try
            {
                // Ottieni i dati storici di Profit & Loss
                var request = new
                {
                    filter = new
                    {
                        _brokerName = "Sella",
                        _profitLossType = 2, // Historical
                        _fromDate = DateTime.Today.AddDays(-days).ToString("yyyy-MM-dd"),
                        _toDate = DateTime.Today.ToString("yyyy-MM-dd")
                    },
                    cols = new[] { "STOCK_CODE", "MARKET_CODE", "PROFIT_LOSS", "PERCENTAGE", "DATE", "QUANTITY" },
                    paging = new { pageSize = 200, pageIndex = 0 }
                };

                var response = await _client.SendRequestAsync("POST", "api/Order/GetProfitLoss2", request);
                return response;
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero P&L Storico", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero P&L Storico", false, $"Errore: {ex.Message}"));
                throw;
            }
        }

        private async Task<JsonElement> GetStockProfitLossAsync(string market, string stock)
        {
            try
            {
                // Ottieni i dati di Profit & Loss filtrati per titolo specifico
                var request = new
                {
                    filter = new
                    {
                        _brokerName = "Sella",
                        _marketCode = market,
                        _stockCode = stock,
                        _profitLossType = 1, // Daily per titolo specifico
                        _fromDate = DateTime.Today.AddDays(-90).ToString("yyyy-MM-dd"),
                        _toDate = DateTime.Today.ToString("yyyy-MM-dd")
                    },
                    cols = new[] { "STOCK_CODE", "MARKET_CODE", "PROFIT_LOSS", "PERCENTAGE", "DATE", "QUANTITY", "AVERAGE_PRICE" },
                    paging = new { pageSize = 50, pageIndex = 0 }
                };

                var response = await _client.SendRequestAsync("POST", "api/Order/ProfitLossFilter", request);
                return response;
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero P&L Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero P&L Titolo", false, $"Errore: {ex.Message}"));
                throw;
            }
        }

        private bool VerifyProfitLossConsistency(JsonElement portfolio, JsonElement dailyPL, JsonElement historicalPL, JsonElement stockPL)
        {
            try
            {
                // Per scopi dimostrativi, consideriamo i dati consistenti
                // In un'implementazione reale, dovresti verificare:
                // - Che i profitti giornalieri siano coerenti con il portafoglio attuale
                // - Che i dati storici siano coerenti con i dati giornalieri
                // - Che i dati per titolo siano coerenti con il portafoglio

                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Verifica Consistenza", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Verifica Consistenza", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        // Restituisce i risultati dei test step
        public List<TestStepResult> GetTestSteps()
        {
            return _testSteps;
        }
    }
}