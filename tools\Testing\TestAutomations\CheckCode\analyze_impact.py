# analyze_impact.py
# Script per analizzare l'impatto dei commit Git sulle API documentate utilizzando un LLM.

# Assicurati di aver installato le librerie necessarie:
# pip install openai python-dotenv

import os
import argparse
import re
import datetime # Per il timestamp nel report
from openai import OpenAI
from dotenv import load_dotenv

# Limiti di contesto approssimativi dei modelli (in token)
MODEL_CONTEXT_LIMITS = {
    "gpt-3.5-turbo": 16384,      # Trattato come 16k per sicurezza, anche se alcune versioni sono 4k
    "gpt-3.5-turbo-16k": 16384,
    "gpt-3.5-turbo-0125": 16384, # Contesto input 16k, output 4k
    "gpt-3.5-turbo-1106": 16384, 
    "gpt-4": 8192,
    "gpt-4-0613": 8192,
    "gpt-4-32k": 32768,
    "gpt-4-32k-0613": 32768,
    "gpt-4-turbo": 128000,        # Alias per versioni più recenti di gpt-4-turbo-preview
    "gpt-4-turbo-preview": 128000,
    "gpt-4-0125-preview": 128000,
    "gpt-4-1106-preview": 128000,
    "gpt-4o": 128000, # Il più recente
    "gpt-4o-2024-05-13": 128000,
    # Gemini modelli principali
    "gemini-2.0-pro": 1048576,  # 1M token (contesto lungo)
    "gemini-2.0-flash": 1048576,
    "gemini-2.0-flash-spark": 1048576,
    "gemini-2.0-flash-001": 1048576,
    "gemini-2.5-pro-preview-05-06": 1048576,
    "gemini-2.5-flash-preview-04-17": 1048576,
    "gemini-2.5-pro": 1048576,
    "gemini-2.5-flash": 1048576,
}

# Buffer di token per il prompt di sistema, i dettagli del commit, le istruzioni e la risposta attesa
TOKEN_BUFFER_FOR_PROMPT_COMMIT_RESPONSE = 3500 # Aumentato leggermente per prompt più dettagliato
AVG_CHARS_PER_TOKEN = 3.5  # Stima conservativa (può variare, 4 è comune per l'inglese)

def load_api_docs(docs_dir):
    """
    Carica il contenuto dei file .md da una directory specificata.
    Restituisce un dizionario {nome_file: contenuto}.
    """
    api_docs_map = {}
    print(f"Caricamento documentazione API da: {docs_dir}")
    if not os.path.isdir(docs_dir):
        print(f"ERRORE: La directory delle API '{docs_dir}' non esiste.")
        return None

    for filename in os.listdir(docs_dir):
        if filename.endswith(".md"):
            filepath = os.path.join(docs_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    api_docs_map[filename] = content
                    char_count = len(content)
                    estimated_tokens = char_count / AVG_CHARS_PER_TOKEN
                    print(f"  - Letto {filename} ({char_count} caratteri, ~{estimated_tokens:.0f} token stimati)")
            except Exception as e:
                print(f"ERRORE: Impossibile leggere il file API '{filepath}': {e}")
    
    if not api_docs_map:
        print("ERRORE: Nessun file .md trovato o leggibile nella directory delle API.")
        return None
    return api_docs_map

def parse_commit_report(report_filepath):
    """
    Analizza il report dei commit (multi-repo, formato git log --stat) generato da getcommits.bat/ps1.
    Restituisce una lista di dizionari, ognuno rappresentante un commit con il suo repository_path.
    """
    print(f"Analisi del report dei commit da: {report_filepath}")
    if not os.path.isfile(report_filepath):
        print(f"ERRORE: Il file report dei commit '{report_filepath}' non esiste.")
        return None

    commits = []
    current_repository_path = None
    current_repository_name = None
    current_commit = None
    current_commit_lines = []
    current_commit_stats = []
    commit_line_regex = re.compile(r"^([0-9a-f]{7,})\s+(.+?)\s+(\d{4}-\d{2}-\d{2})\s+(.+)$")
    stat_line_regex = re.compile(r"^\s*([\w\./\\-]+)\s*\|\s*\d+.*$")
    summary_line_regex = re.compile(r"^\s*\d+ files? changed.*$")
    repo_header_regex = re.compile(r"^=+\s*(.+?)\s*\((.+)\)\s*=+")

    with open(report_filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    for idx, line in enumerate(lines):
        line = line.rstrip('\n')
        repo_header_match = repo_header_regex.match(line)
        if repo_header_match:
            # Se c'è un commit in corso, salvalo prima di cambiare repo
            if current_commit:
                current_commit['changes'] = '\n'.join(current_commit_stats).strip()
                commits.append(current_commit)
                current_commit = None
                current_commit_lines = []
                current_commit_stats = []
            current_repository_name = repo_header_match.group(1).strip()
            current_repository_path = repo_header_match.group(2).strip()
            continue
        # Riconosci un commit
        commit_match = commit_line_regex.match(line)
        if commit_match:
            # Se c'è un commit in corso, salvalo
            if current_commit:
                current_commit['changes'] = '\n'.join(current_commit_stats).strip()
                commits.append(current_commit)
            # Nuovo commit
            current_commit = {
                'repository_path': current_repository_path or 'Sconosciuto',
                'repository_name': current_repository_name or 'Sconosciuto',
                'hash': commit_match.group(1),
                'author': commit_match.group(2),
                'date': commit_match.group(3),
                'message': commit_match.group(4),
                'changes': '',
            }
            current_commit_stats = []
            continue
        # Se siamo dentro un commit, raccogli le statistiche
        if current_commit:
            if stat_line_regex.match(line) or summary_line_regex.match(line) or line.strip() == '':
                current_commit_stats.append(line)
            else:
                # Linea non di statistica, non fare nulla
                pass
    # Salva l'ultimo commit se presente
    if current_commit:
        current_commit['changes'] = '\n'.join(current_commit_stats).strip()
        commits.append(current_commit)

    if not commits:
        print("ERRORE: Nessun commit valido trovato nel report. Controlla il formato del report o il file stesso.")
        return None
    print(f"  - Trovati {len(commits)} commit validi nel report multi-repo.")
    return commits

def extract_relevant_md_sections(md_content, repo_name, files_changed):
    """
    Estrae solo le sezioni rilevanti del .md:
    - Sezioni che contengono il nome del repo, nomi file modificati, o endpoint simili
    - Se non trova match, restituisce solo l'intestazione e la descrizione (prime 30 righe o fino al primo heading di secondo livello)
    """
    import re
    lines = md_content.split('\n')
    keywords = set([repo_name.lower()])
    for f in files_changed:
        # Prendi solo il nome file senza path e senza estensione
        base = re.sub(r"\\|/", " ", f).split()
        for b in base:
            if b:
                keywords.add(b.lower().split('.')[0])
    # Cerca blocchi che contengono almeno una keyword
    relevant_blocks = []
    current_block = []
    found = False
    for line in lines:
        if line.strip().startswith('## '):
            if current_block:
                block_text = '\n'.join(current_block)
                if any(kw in block_text.lower() for kw in keywords):
                    relevant_blocks.append(block_text)
                    found = True
                current_block = []
        current_block.append(line)
    # Check last block
    if current_block:
        block_text = '\n'.join(current_block)
        if any(kw in block_text.lower() for kw in keywords):
            relevant_blocks.append(block_text)
            found = True
    if found and relevant_blocks:
        return '\n\n'.join(relevant_blocks)
    # Fallback: prime 30 righe o fino al primo heading di secondo livello
    fallback = []
    for line in lines:
        if line.strip().startswith('## '):
            break
        fallback.append(line)
        if len(fallback) >= 30:
            break
    return '\n'.join(fallback)

def analyze_commit_with_llm(client, commit_details, api_doc_name, api_doc_content, model):
    """
    Invia i dettagli del commit (incluso il repository path) e un singolo documento API all'LLM per l'analisi.
    """
    commit_hash = commit_details.get('hash', 'N/D')
    repo_path = commit_details.get('repository_path', 'Sconosciuto')
    files_changed = commit_details.get('files_changed', [])
    api_related_files = commit_details.get('api_related_files', [])
    files_changed_str = '\n'.join(files_changed) if files_changed else 'Nessun file modificato rilevato.'
    api_related_files_str = '\n'.join(api_related_files) if api_related_files else 'Nessun file API/Controller individuato.'
    print(f"  Analisi LLM per commit {commit_hash} (da {repo_path}) rispetto a '{api_doc_name}'")
    prompt_template = f"""
Analizza il seguente commit Git proveniente dal repository '{repo_path}' e determina i potenziali impatti negativi sulle API descritte NEL SEGUENTE DOCUMENTO API SPECIFICO.

DOCUMENTO API SPECIFICO ({api_doc_name}):
---
{api_doc_content}
---

DETTAGLI DEL COMMIT (dal repository: {repo_path}):
Commit Hash: {commit_hash}
Autore: {commit_details.get('author', 'N/D')}
Data: {commit_details.get('date', 'N/D')}
Messaggio:
{commit_details.get('message', 'Nessun messaggio')}

Modifiche ai file (statistiche dal repository {repo_path}):
{commit_details.get('changes', 'Nessuna statistica dei file' if not commit_details.get('changes') else commit_details.get('changes'))}

Elenco file modificati:
{files_changed_str}

File che sembrano toccare API/Controller:
{api_related_files_str}
---

TASK:
Considerando che il commit proviene dal repository '{repo_path}', basati ESCLUSIVAMENTE sul DOCUMENTO API SPECIFICO ({api_doc_name}) e sui dettagli del commit forniti:
1. Identifica e elenca eventuali impatti negativi diretti o indiretti che questo commit (considerando il suo repository di origine '{repo_path}') potrebbe introdurre alle funzionalità descritte nel documento API fornito.
2. Considera modifiche a endpoint, formati di richiesta/risposta, autenticazione, strutture dati, comportamento atteso o qualsiasi funzionalità descritta nel documento API ({api_doc_name}) che potrebbe essere influenzata dai file modificati (nel contesto di '{repo_path}') o dal messaggio di commit.
3. Per ogni impatto potenziale, sii specifico e, se possibile, menziona quale parte del documento API ({api_doc_name}) potrebbe essere rilevante.
4. Se ritieni che il commit (da '{repo_path}') modifichi file NON direttamente correlati al documento API ({api_doc_name}) o se le modifiche sembrano non avere impatti negativi evidenti su quanto descritto in {api_doc_name}, indicalo chiaramente.
5. Se le informazioni (commit o documento API) non sono sufficienti per una valutazione completa rispetto a {api_doc_name} e al contesto del repository '{repo_path}', menzionalo.
6. **In particolare, valuta se le modifiche possono impattare le chiamate ai servizi FE-Sella che utilizzano le API descritte nel documento {api_doc_name}. Se sì, indica quali endpoint, servizi o funzionalità FE-Sella potrebbero essere impattati e in che modo.**
7. Fornisci la tua analisi in italiano.

Formatta la risposta come segue:

**Analisi Impatto Commit {commit_hash} (Repo: {repo_path}) su Documento API '{api_doc_name}':**

**Potenziali Impatti Negativi:**
- [Impatto 1]: (Descrizione e riferimento a {api_doc_name} se applicabile)
- [Impatto 2]: ...
(Elencare tutti gli impatti identificati. Se nessuno, scrivere "Nessun impatto negativo diretto identificato sulle API descritte in {api_doc_name} per questo commit da {repo_path}.")

**Possibile impatto sulle chiamate FE-Sella:**
- (Descrivi se e come le modifiche potrebbero impattare le chiamate ai servizi FE-Sella che usano queste API. Se nessun impatto, scrivi "Nessun impatto rilevante sulle chiamate FE-Sella individuato.")

**Altre Osservazioni:**
- (Eventuali osservazioni aggiuntive)

"""
    try:
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "Sei un assistente AI specializzato nell'analisi di codice e documentazione API per identificare potenziali impatti negativi derivanti da modifiche software, focalizzandoti sul documento API specifico fornito e considerando il repository di origine del commit."
                },
                {
                    "role": "user",
                    "content": prompt_template,
                }
            ],
            model=model,
        )
        analysis = chat_completion.choices[0].message.content
        print(f"    - Analisi LLM completata per commit {commit_hash} (da {repo_path}) su '{api_doc_name}'.")
        return analysis
    except Exception as e:
        error_message = f"**Analisi Impatto Commit {commit_hash} (Repo: {repo_path}) su Documento API '{api_doc_name}':**\n\nAnalisi fallita: {e}"
        print(f"ERRORE: Chiamata API OpenAI fallita per commit {commit_hash} (da {repo_path}) su '{api_doc_name}': {e}")
        return error_message

def get_openai_client(model_name):
    """
    Restituisce un client OpenAI configurato per OpenAI o Gemini in base al nome modello.
    """
    if model_name.lower().startswith("gemini"):
        api_key = os.getenv("GEMINI_API_KEY")
        base_url = os.getenv("GEMINI_BASE_URL", "https://generativelanguage.googleapis.com/v1beta/openai/")
        if not api_key:
            raise RuntimeError("GEMINI_API_KEY non trovata nel .env o nelle variabili d'ambiente!")
        return OpenAI(api_key=api_key, base_url=base_url)
    else:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise RuntimeError("OPENAI_API_KEY non trovata nel .env o nelle variabili d'ambiente!")
        return OpenAI(api_key=api_key)

def main():
    parser = argparse.ArgumentParser(description="Analizza i commit Git (da report multi-repo) e valuta l'impatto sulle API documentate usando un LLM.")
    parser.add_argument("commit_report_file", help="Percorso del file di report dei commit generato da getcommits.bat (formato multi-repo).")
    parser.add_argument("api_docs_dir", help="Percorso della directory contenente i file .md della documentazione API.")
    parser.add_argument("-o", "--output_file", help="Percorso del file di output per il report di analisi. Se non specificato, stampa a console.", default="impact_report.md")
    parser.add_argument("--model", help="Modello OpenAI o Gemini da utilizzare (es. gpt-3.5-turbo, gpt-4o, gemini-2.0-pro, gemini-2.0-flash, gemini-2.5-pro, gemini-2.5-flash, gemini-2.5-pro-preview-05-06, gemini-2.5-flash-preview-04-17). Default: gpt-3.5-turbo.", default="gpt-3.5-turbo")
    
    args = parser.parse_args()

    # Carica variabili d'ambiente da .env
    # Cerca .env nella directory root del progetto OT, assumendo che TestAutomations sia una subdir di OT
    project_root_candidate = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    # Se __file__ è in /c/dev/OT/TestAutomations/CheckCode/analyze_impact.py
    # allora project_root_candidate sarà /c/dev/OT
    
    dotenv_path = os.path.join(project_root_candidate, '.env')
    
    if os.path.exists(dotenv_path):
        load_dotenv(dotenv_path=dotenv_path)
        print(f"File .env caricato da: {dotenv_path}")
    else:
        # Tentativo di fallback se .env non è nella root del progetto OT ma magari vicino allo script o cwd
        # Questo è meno ideale ma fornisce una possibilità in più
        print(f"File .env non trovato in {dotenv_path}. Tento caricamento da posizione standard (es. directory corrente o home).")
        load_dotenv() 
        if os.getenv("OPENAI_API_KEY"):
            print("File .env caricato da posizione standard di python-dotenv.")
        else:
            print(f"ATTENZIONE: File .env non trovato in '{dotenv_path}' o in posizioni standard. Assicurati che OPENAI_API_KEY sia impostata come variabile d'ambiente.")


    # Scegli il client in base al modello richiesto
    try:
        client = get_openai_client(args.model)
    except Exception as e:
        print(f"ERRORE: {e}")
        return

    # 1. Carica documentazione API (mappa nome_file: contenuto)
    api_documentation_map = load_api_docs(args.api_docs_dir)
    if not api_documentation_map:
        return

    # 2. Parsa il report dei commit (multi-repo)
    commits_to_analyze = parse_commit_report(args.commit_report_file)
    if not commits_to_analyze:
        return
    
    # Raggruppa commit per repo
    repo_commits = {}
    for c in commits_to_analyze:
        repo = c.get('repository_name') or c.get('repository_path')
        if repo not in repo_commits:
            repo_commits[repo] = []
        repo_commits[repo].append(c)

    # Analisi di impatto per ogni repo (accorpata)
    impact_results = {}
    summary_rows = []
    for repo, commits in repo_commits.items():
        # Lista di tutti i file modificati nel repo
        all_files_changed = set()
        for commit in commits:
            if 'changes' in commit and commit['changes']:
                for line in commit['changes'].split('\n'):
                    if '|' in line:
                        all_files_changed.add(line.split('|')[0].strip())
        # Estrai solo le parti rilevanti di ogni doc API
        relevant_api_docs = {}
        for doc_name, doc_content in api_documentation_map.items():
            relevant_api_docs[doc_name] = extract_relevant_md_sections(doc_content, repo, all_files_changed)
        # Prepara il prompt unico per tutti i commit del repo
        commits_summary = []
        for c in commits:
            commits_summary.append(f"- `{c['hash']}` {c['date']} {c['author']}: {c['message']}")
        prompt_commits = '\n'.join(commits_summary)
        impact_results[repo] = []
        api_impact = False
        notes = []
        for doc_name, relevant_doc in relevant_api_docs.items():
            print(f"[INFO] Analisi in corso: {repo} su {doc_name}...")
            prompt_template = f"""
Analizza l'impatto dei seguenti commit Git provenienti dal repository '{repo}' sulle API descritte nel documento API specifico fornito.

DOCUMENTO API RILEVANTE ({doc_name}):
---
{relevant_doc}
---

DETTAGLI DEI COMMIT (repository: {repo}):
{prompt_commits}
---

TASK:
1. Elenca eventuali impatti negativi diretti o indiretti che questi commit potrebbero introdurre alle funzionalità descritte nel documento API fornito.
2. Considera modifiche a endpoint, formati di richiesta/risposta, autenticazione, strutture dati, comportamento atteso o qualsiasi funzionalità descritta nel documento API che potrebbe essere influenzata dai commit.
3. Sii specifico e, se possibile, menziona quale parte del documento API potrebbe essere rilevante.
4. Se i commit modificano file NON direttamente correlati al documento API o se le modifiche sembrano non avere impatti negativi evidenti, indicalo chiaramente.
5. Se le informazioni (commit o documento API) non sono sufficienti per una valutazione completa, menzionalo.
6. **In particolare, valuta se le modifiche possono impattare le chiamate ai servizi FE-Sella che utilizzano le API descritte nel documento {doc_name}. Se sì, indica quali endpoint, servizi o funzionalità FE-Sella potrebbero essere impattati e in che modo.**
7. Fornisci la tua analisi in italiano.

Formatta la risposta come segue:

**Analisi Impatto Commit su Documento API '{doc_name}' (Repo: {repo}):**

**Potenziali Impatti Negativi:**
- [Impatto 1]: ...
- [Impatto 2]: ...
(Elencare tutti gli impatti identificati. Se nessuno, scrivere "Nessun impatto negativo diretto identificato sulle API descritte in {doc_name} per questi commit da {repo}.")

**Possibile impatto sulle chiamate FE-Sella:**
- (Descrivi se e come le modifiche potrebbero impattare le chiamate ai servizi FE-Sella che usano queste API. Se nessun impatto, scrivi "Nessun impatto rilevante sulle chiamate FE-Sella individuato.")

**Altre Osservazioni:**
- (Eventuali osservazioni aggiuntive)
"""
            try:
                chat_completion = client.chat.completions.create(
                    messages=[
                        {
                            "role": "system",
                            "content": "Sei un assistente AI specializzato nell'analisi di codice e documentazione API per identificare potenziali impatti negativi derivanti da modifiche software, focalizzandoti sul documento API specifico fornito e considerando il repository di origine dei commit."
                        },
                        {
                            "role": "user",
                            "content": prompt_template,
                        }
                    ],
                    model=args.model,
                )
                analysis = chat_completion.choices[0].message.content
                impact_results[repo].append({
                    'doc': doc_name,
                    'analysis': analysis
                })
                if any(word in analysis.lower() for word in ["impatto", "endpoint", "modifica", "breaking"]):
                    api_impact = True
                if "osservazioni" in analysis.lower():
                    notes.append("Osservazioni presenti")
                print(f"[INFO] Analisi completata: {repo} su {doc_name}")
            except Exception as e:
                print(f"[ERRORE] Analisi fallita: {repo} su {doc_name} - {e}")
                impact_results[repo].append({
                    'doc': doc_name,
                    'analysis': f"Analisi fallita: {e}"
                })
        summary_rows.append({
            'repo': repo,
            'num_commits': len(commits),
            'api_impact': "Sì" if api_impact else "Da verificare",
            'notes': ", ".join(set(notes)) if notes else ""
        })

    # Costruzione report markdown
    from datetime import datetime
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    md = []
    md.append(f"# Report Analisi Impatto Commit su API\n")
    md.append(f"*Generato il: {now}*\n")
    md.append(f"\n## Servizi/Librerie modificati da rilasciare\n")
    for row in summary_rows:
        md.append(f"- {row['repo']}")
    md.append("\n---\n\n## Analisi di Impatto\n")
    for repo, analyses in impact_results.items():
        md.append(f"### {repo}\n")
        for a in analyses:
            md.append(f"**Analisi su '{a['doc']}':**\n{a['analysis']}\n")
        md.append("---\n")
    md.append("\n## Tabella Riassuntiva\n")
    md.append("\n| Servizio/Libreria | # Commit | Impatto API | Note |")
    md.append("|-------------------|----------|-------------|------|")
    for row in summary_rows:
        md.append(f"| {row['repo']} | {row['num_commits']} | {row['api_impact']} | {row['notes']} |")
    md.append("\n")
    final_report_content = "\n".join(md)

    try:
        output_path = args.output_file or "impact_report.md"
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_report_content)
        print(f"Report di analisi salvato in: {output_path}")
    except Exception as e:
        print(f"ERRORE: Impossibile salvare il report di analisi in '{output_path}': {e}")
        print("\nReport di Analisi (stampa a console a causa di errore di salvataggio):\n", final_report_content)

    print("Analisi completata.")

if __name__ == "__main__":
    main() 