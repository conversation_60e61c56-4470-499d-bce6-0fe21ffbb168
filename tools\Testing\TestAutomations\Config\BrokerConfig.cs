using Microsoft.Extensions.Configuration;

namespace TestAutomations.Config
{
    public class BrokerConfig
    {
        private readonly IConfiguration _configuration;

        public BrokerConfig(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        // Proprietà broker con valori predefiniti e lettura da configurazione
        public string BrokerCustomer => _configuration["OT_BROKER_CUSTOMER"] ?? "********";
        public int BondAcctId => int.TryParse(_configuration["OT_BOND_ACCT_ID"], out var id) ? id : 1300770;
        public int CashAcctId => int.TryParse(_configuration["OT_CASH_ACCT_ID"], out var id) ? id : 895247;
        public int DossierId => int.TryParse(_configuration["OT_DOSSIER_ID"], out var id) ? id : 898999;
        public int BankId => int.TryParse(_configuration["OT_BANK_ID"], out var id) ? id : 1;
        public int BrokerId => int.TryParse(_configuration["OT_BROKER_ID"], out var id) ? id : 2; // Sella
        public int CustomerId => int.TryParse(_configuration["OT_CUSTOMER_ID"], out var id) ? id : 1116;
        
        // Parametri di test
        public string TestMarket => _configuration["OT_TEST_MARKET"] ?? "MTA";
        public string TestStock => _configuration["OT_TEST_STOCK"] ?? "AMP";
        public int TestOrderType => int.TryParse(_configuration["OT_TEST_ORDER_TYPE"], out var type) ? type : 0;
        public double TestPrice => double.TryParse(_configuration["OT_TEST_PRICE"], out var price) ? price : 10.5;
        public int TestQuantity => int.TryParse(_configuration["OT_TEST_QUANTITY"], out var qty) ? qty : 1;
        
        // Canali e dispositivi
        public int ChannelType => int.TryParse(_configuration["OT_CHANNEL_TYPE"], out var type) ? type : 13;
        public int DeviceType => int.TryParse(_configuration["OT_DEVICE_TYPE"], out var type) ? type : 23;
    }
}
