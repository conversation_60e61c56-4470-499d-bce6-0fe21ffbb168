using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using TestAutomations.Models;
using TestAutomations.Common;
using TestAutomations.Config;
using System.Net;

namespace TestAutomations.Clients
{
    public class OTTradingClient
    {
        public string BaseUrl { get; }
        public string AuthToken { get; private set; }
        public string IbCode { get; private set; }
        public List<RequestLog> RequestLogs { get; } = new();
        public BrokerConfig BrokerConfig { get; }
        private HttpClient _httpClient;

        public OTTradingClient(string baseUrl, BrokerConfig brokerConfig = null)
        {
            BaseUrl = baseUrl;
            if (string.IsNullOrWhiteSpace(BaseUrl))
                throw new ArgumentException("OT_API_BASE_URL non impostato.");

            // Usa la configurazione fornita o quella globale
            BrokerConfig = brokerConfig ?? Program.BrokerConfig;

            _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(30) };
            _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("OTTestClient/1.0");
        }

        private async Task<JsonElement> PostAndParseAsync(string endpoint, string jsonPayload)
        {
            try
            {
                var resp = await PostAsync(endpoint, jsonPayload);

                if (!resp.IsSuccessStatusCode)
                {
                    var errorContent = await resp.Content.ReadAsStringAsync();
                    LogRequestError(endpoint, jsonPayload, resp, errorContent);
                    throw new ApiException(
                        $"Errore chiamata API {endpoint}: {resp.StatusCode}",
                        (int)resp.StatusCode,
                        endpoint,
                        errorContent
                    );
                }

                var json = await resp.Content.ReadAsStringAsync();
                using var doc = JsonDocument.Parse(json);
                return doc.RootElement.Clone();
            }
            catch (HttpRequestException ex)
            {
                LogRequestError(endpoint, jsonPayload, null, ex.Message);
                throw new ApiException(
                    $"Errore di connessione per {endpoint}: {ex.Message}",
                    0,
                    endpoint,
                    ex.Message
                );
            }
            catch (JsonException ex)
            {
                LogRequestError(endpoint, jsonPayload, null, ex.Message);
                throw new ApiException(
                    $"Errore di parsing JSON per {endpoint}: {ex.Message}",
                    0,
                    endpoint,
                    ex.Message
                );
            }
            catch (Exception ex) when (!(ex is ApiException))
            {
                LogRequestError(endpoint, jsonPayload, null, ex.Message);
                throw new ApiException(
                    $"Errore generico per {endpoint}: {ex.Message}",
                    0,
                    endpoint,
                    ex.Message
                );
            }
        }

        private async Task<HttpResponseMessage> PostAsync(string endpoint, string jsonPayload)
        {
            var startTime = DateTime.Now;
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(BaseUrl.TrimEnd('/') + endpoint, content);
            var endTime = DateTime.Now;

            var log = new RequestLog
            {
                Timestamp = startTime,
                Endpoint = endpoint,
                Method = "POST",
                Payload = jsonPayload,
                RequestBody = jsonPayload,
                StatusCode = (int)response.StatusCode,
                ResponseTime = endTime - startTime
            };

            if (!response.IsSuccessStatusCode)
            {
                log.Error = $"HTTP {(int)response.StatusCode} - {response.ReasonPhrase}";
                log.ResponseContent = await response.Content.ReadAsStringAsync();
                log.ResponseBody = log.ResponseContent;
            }
            else
            {
                log.ResponseContent = await response.Content.ReadAsStringAsync();
                log.ResponseBody = log.ResponseContent;
            }

            RequestLogs.Add(log);
            return response;
        }

        private void LogRequestError(string endpoint, string payload, HttpResponseMessage resp, string error = null)
        {
            var log = new RequestLog
            {
                Timestamp = DateTime.Now,
                Endpoint = endpoint,
                Method = "POST", // Assumiamo POST come default
                Payload = payload,
                RequestBody = payload,
                StatusCode = resp?.StatusCode != null ? (int)resp.StatusCode : 0,
                Error = error ?? resp?.ReasonPhrase
            };
            RequestLogs.Add(log);
        }

        private void LogRequestInfo(string endpoint, string infoMessage)
        {
            var log = new RequestLog
            {
                Timestamp = DateTime.Now,
                Endpoint = endpoint,
                Method = "INFO",
                InfoMessage = infoMessage
            };
            RequestLogs.Add(log);
        }

        public async Task<bool> LoginAsync(string username = null, string password = null, bool isDummy = false)
        {
            username ??= Environment.GetEnvironmentVariable("OT_USERNAME");
            password ??= Environment.GetEnvironmentVariable("OT_PASSWORD");
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("OT_USERNAME o OT_PASSWORD non impostati.");

            if (isDummy)
            {
                return await DummyLoginAsync(username, password);
            }
            else
            {
                return await NormalLoginAsync(username, password);
            }
        }

        private async Task<bool> DummyLoginAsync(string username, string password)
        {
            Console.WriteLine($"Esecuzione dummy login come {username}...");
            string endpoint = "/api/Account/DummyLoginApi";
            var payload = JsonSerializer.Serialize(new { UserName = username, Password = password });

            // Utilizziamo PostAsync invece di PostAndParseAsync per accedere ai cookie
            var response = await PostAsync(endpoint, payload);
            var json = await response.Content.ReadAsStringAsync();
            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement.Clone();

            IbCode = username;

            // Prova a estrarre il token dalla risposta JSON
            AuthToken = root.TryGetProperty("token", out var t) ? t.GetString() :
                        root.TryGetProperty("accessToken", out var at) ? at.GetString() :
                        root.TryGetProperty("authToken", out var aut) ? aut.GetString() : null;

            // Se il token non è stato trovato nella risposta JSON, cerca nei cookie
            if (string.IsNullOrEmpty(AuthToken))
            {
                // Estrai i cookie dalla risposta
                if (response.Headers.TryGetValues("Set-Cookie", out var cookies))
                {
                    Console.WriteLine($"Trovati {cookies.Count()} cookie nella risposta");
                    foreach (var cookie in cookies)
                    {
                        Console.WriteLine($"Cookie ricevuto: {cookie.Split(';')[0]}");
                        // Cerca cookie di autenticazione
                        if (cookie.Contains("auth", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains("token", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains(".AspNetCore.Identity", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains(".AspNetCore.Session", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains("AspNet.SharedCookieOTW", StringComparison.OrdinalIgnoreCase))
                        {
                            // Estrai il valore del cookie
                            var parts = cookie.Split(';')[0].Split('=');
                            if (parts.Length >= 2)
                            {
                                var cookieName = parts[0].Trim();
                                var cookieValue = parts[1].Trim();

                                Console.WriteLine($"Cookie di autenticazione trovato: {cookieName}");

                                // Salva il cookie per le richieste future
                                var cookieContainer = new CookieContainer();
                                cookieContainer.Add(new Uri(BaseUrl), new Cookie(cookieName, cookieValue));

                                // Aggiorna l'HttpClient per utilizzare i cookie
                                var handler = new HttpClientHandler { CookieContainer = cookieContainer };
                                _httpClient = new HttpClient(handler) { Timeout = TimeSpan.FromSeconds(30) };
                                _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("OTTestClient/1.0");

                                // Imposta il token di autenticazione
                                AuthToken = cookieValue;
                                break;
                            }
                        }
                    }
                }
            }

            if (!string.IsNullOrEmpty(AuthToken))
            {
                // Se abbiamo un token, aggiungiamolo all'header Authorization
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AuthToken);
                return true;
            }

            // Per il dummy login, consideriamo il login riuscito anche senza token
            Console.WriteLine("Modalità dummy: login considerato riuscito anche senza token");
            return true;
        }

        private async Task<bool> NormalLoginAsync(string username, string password)
        {
            Console.WriteLine($"Esecuzione login normale (multi-step) come {username}...");

            try
            {
                // Step 0: Richiesta iniziale senza credenziali
                Console.WriteLine("Login normale - Step 0");
                string endpoint = "/api/Account/LoginApi";
                var payload = JsonSerializer.Serialize(new {});

                var response0 = await PostAsync(endpoint, payload);
                var json0 = await response0.Content.ReadAsStringAsync();
                Console.WriteLine($"Risposta Step 0: {json0}");

                using var doc0 = JsonDocument.Parse(json0);
                var root0 = doc0.RootElement.Clone();

                // Estrai AuthId e Step dalla risposta
                string authId = null;
                int step = 0;

                if (root0.TryGetProperty("AuthId", out var authIdElement))
                {
                    authId = authIdElement.GetString();
                }
                else if (root0.TryGetProperty("authId", out authIdElement))
                {
                    authId = authIdElement.GetString();
                }

                if (root0.TryGetProperty("Step", out var stepElement))
                {
                    step = stepElement.GetInt32();
                }
                else if (root0.TryGetProperty("step", out stepElement))
                {
                    step = stepElement.GetInt32();
                }

                if (string.IsNullOrEmpty(authId))
                {
                    Console.WriteLine("Login normale - AuthId non trovato nella risposta di Step 0");
                    return false;
                }

                Console.WriteLine($"Login normale - AuthId: {authId}, Step richiesto: {step}");

                // Step 1: Invio credenziali
                Console.WriteLine("Login normale - Step 1");
                var payload1 = JsonSerializer.Serialize(new
                {
                    Step = 1,
                    AuthId = authId,
                    Username = username,
                    Password = password
                });

                var response1 = await PostAsync(endpoint, payload1);
                var json1 = await response1.Content.ReadAsStringAsync();
                Console.WriteLine($"Risposta Step 1: {json1}");

                using var doc1 = JsonDocument.Parse(json1);
                var root1 = doc1.RootElement.Clone();

                // Verifica se il login è completato
                int step1 = 0;
                if (root1.TryGetProperty("Step", out var step1Element))
                {
                    step1 = step1Element.GetInt32();
                }
                else if (root1.TryGetProperty("step", out step1Element))
                {
                    step1 = step1Element.GetInt32();
                }

                // Step 100 o null indica login completato
                bool loginCompleted = (step1 == 100 || step1 == 0);

                if (!loginCompleted)
                {
                    Console.WriteLine($"Login normale - Login non completato, step successivo richiesto: {step1}");
                    return false;
                }

                Console.WriteLine("Login normale - Login completato con successo");

                // Estrai token se presente
                AuthToken = root1.TryGetProperty("token", out var t) ? t.GetString() :
                            root1.TryGetProperty("accessToken", out var at) ? at.GetString() :
                            root1.TryGetProperty("authToken", out var aut) ? aut.GetString() : null;

                // Estrai cookie di autenticazione
                if (string.IsNullOrEmpty(AuthToken) && response1.Headers.TryGetValues("Set-Cookie", out var cookies))
                {
                    Console.WriteLine($"Trovati {cookies.Count()} cookie nella risposta di Step 1");
                    foreach (var cookie in cookies)
                    {
                        Console.WriteLine($"Cookie ricevuto: {cookie.Split(';')[0]}");
                        // Cerca cookie di autenticazione
                        if (cookie.Contains("auth", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains("token", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains(".AspNetCore.Identity", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains(".AspNetCore.Session", StringComparison.OrdinalIgnoreCase) ||
                            cookie.Contains("AspNet.SharedCookieOTW", StringComparison.OrdinalIgnoreCase))
                        {
                            // Estrai il valore del cookie
                            var parts = cookie.Split(';')[0].Split('=');
                            if (parts.Length >= 2)
                            {
                                var cookieName = parts[0].Trim();
                                var cookieValue = parts[1].Trim();

                                Console.WriteLine($"Cookie di autenticazione trovato: {cookieName}");

                                // Salva il cookie per le richieste future
                                var cookieContainer = new CookieContainer();
                                cookieContainer.Add(new Uri(BaseUrl), new Cookie(cookieName, cookieValue));

                                // Aggiorna l'HttpClient per utilizzare i cookie
                                var handler = new HttpClientHandler { CookieContainer = cookieContainer };
                                _httpClient = new HttpClient(handler) { Timeout = TimeSpan.FromSeconds(30) };
                                _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("OTTestClient/1.0");

                                // Imposta il token di autenticazione
                                AuthToken = cookieValue;
                                break;
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(AuthToken))
                {
                    // Se abbiamo un token, aggiungiamolo all'header Authorization
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AuthToken);
                }

                // Anche se non abbiamo un token esplicito, consideriamo il login riuscito se il processo è completato
                IbCode = username;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Errore durante il login normale: {ex.Message}");
                return false;
            }
        }

        // Metodo per verificare se il client è autenticato
        public bool IsAuthenticated()
        {
            // Se abbiamo un token o un cookie di autenticazione, consideriamo il client autenticato
            return !string.IsNullOrEmpty(AuthToken);
        }

        public async Task<JsonElement> InsertOrderAsync(int broker, string market, string stock, int orderType, double price, int quantity)
        {
            // Utilizziamo l'endpoint corretto per inserire un ordine
            var request = new
            {
                par = new
                {
                    BrokerName = broker,
                    MarketCode = market,
                    StockCode = stock,
                    OrderType = orderType,
                    Price = price,
                    Quantity = quantity,
                    ValidityDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    EvaluationMode = 0,
                    OrderFast = false,
                    BrokerProperties = new
                    {
                        BrokerCustomer = IbCode,
                        BondAcctId = BrokerConfig.BondAcctId,
                        CashAcctId = BrokerConfig.CashAcctId,
                        DossierId = BrokerConfig.DossierId,
                        BankId = BrokerConfig.BankId
                    },
                    Phase = 3,
                    ManualOrder = false,
                    BestExecution = false,
                    BatchOrder = false,
                    ParkingOrder = false,
                    LeverageOrder = false,
                    StopPrice = 0,
                    OrderParameter = 8,
                    GamingId = 0,
                    PriceType = 0,
                    StrategyEvaluationMode = 0,
                    StrategyConditionType = 0,
                    ClientInfo = new
                    {
                        ChannelType = BrokerConfig.ChannelType,
                        DeviceType = BrokerConfig.DeviceType,
                        IbCode = IbCode
                    }
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/InsertOrder", request);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="brokerName"></param>
        /// <param name="orderId"></param>
        /// <param name="type">Confirm 0: Insert, 1: Update, 2: Delete</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<JsonElement> ConfirmOrderAsync(int brokerName, string orderId, int type = 0)
        {
            if (string.IsNullOrEmpty(orderId))
                throw new ArgumentException("L'ID dell'ordine non può essere vuoto", nameof(orderId));

            // Utilizziamo l'endpoint corretto per confermare un ordine
            var request = new
            {
                par = new
                {
                    BrokerName = brokerName, // Sella
                    OrderID = orderId,
                    ClientInfo = new
                    {
                        ChannelType = 13,
                        DeviceType = 23,
                        IbCode = IbCode
                    }
                }
            };

            string endpoint = type switch
            {
                0 => "/api/Order/ConfirmInsertOrder", // Inserimento
                1 => "/api/Order/ConfirmUpdateOrder", // Aggiornamento
                2 => "/api/Order/ConfirmDeleteOrder", // Eliminazione
                _ => throw new ArgumentException("Tipo di conferma non valido", nameof(type))
            };

            return await SendRequestAsync(HttpMethod.Post, endpoint, request);
        }

        public async Task<JsonElement> GetOrderStatusAsync(int brokerName, string orderId)
        {
            if (string.IsNullOrEmpty(orderId))
                throw new ArgumentException("L'ID dell'ordine non può essere vuoto", nameof(orderId));

            // Utilizziamo l'endpoint corretto per ottenere lo stato di un ordine
            // Utilizziamo GetOrderStatus2 che è l'endpoint corretto secondo la documentazione
            var fromDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd");
            var toDate = DateTime.Now.ToString("yyyy-MM-dd");

            var request = new
            {
                cols = new[]
                {
                    "VALIDITY_DATE",
                    "ISIN",
                    "CASH_ACCOUNT_CODE",
                    "CALL_PUT",
                    "DERIVATIVES_ID",
                    "BOND_ACCOUNT_CODE",
                    "ORDER_INSERT_DATE",
                    "ORDER_TIPOLOGY",
                    "ORDER_STATUS",
                    "STOCK_CODE",
                    "STOCK_DESCRIPTION",
                    "PRICE_TYPE",
                    "STOP_PRICE",
                    "VARIATION",
                    "VOLUME",
                    "LAST",
                    "TIME_LAST"
                },
                filter = new
                {
                    _brokerName = brokerName,
                    _fromDate = fromDate,
                    _toDate = toDate,
                    _orderShowType = 0,
                    _accountFilter = new { },
                    _stockFilter = new
                    {
                        _stockTypeGroup = (object)null,
                        _stockType = (object)null,
                        _stockSubType = (object)null,
                        _stockTypeDetail = (object)null,
                        _marketCode = (object)null,
                        _stockCode = (object)null,
                        _searchType = (object)null,
                        _searchText = (object)null
                    },
                    _showBuyOrders = true,
                    _showSellOrders = true,
                    _showLeverageOrders = false,
                    _showSpecialOrders = false,
                    _onlyMyOrders = false,
                    _orderTipology = 0,
                    _orderId = orderId,
                    _orderType = (object)null,
                    _onlyOrdersWithExe = false,
                    _refreshCache = true,
                    _gamingId = 0,
                    ClientInfo = new
                    {
                        ChannelType = BrokerConfig.ChannelType,
                        DeviceType = BrokerConfig.DeviceType,
                        BankId = BrokerConfig.BankId,
                        IbCode = IbCode
                    }
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/GetOrderStatus2", request);
        }

        public async Task<JsonElement> GetOrderDetailsAsync(int brokerName, string orderId)
        {
            if (string.IsNullOrEmpty(orderId))
                throw new ArgumentException("L'ID dell'ordine non può essere vuoto", nameof(orderId));

            var request = new
            {
                brokerName = brokerName,
                orderCode = orderId,
                _clientInfo = new
                {
                    ChannelType = 13,
                    DeviceType = 23,
                    BankId = 0,
                    IpAddress = "0.0.0.0",
                    IbCode = IbCode
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/GetOrderDetail", request);
        }

        public async Task<JsonElement> UpdateOrderAsync(int broker, string orderId, int newQuantity, double newPrice)
        {
            if (string.IsNullOrEmpty(orderId))
                throw new ArgumentException("L'ID dell'ordine non può essere vuoto", nameof(orderId));

            // Utilizziamo l'endpoint corretto per aggiornare un ordine
            var request = new
            {
                par = new
                {
                    BrokerName = broker,
                    OrderID = orderId,
                    Price = newPrice,
                    Quantity = newQuantity,
                    CustomerID = BrokerConfig.CustomerId,
                    IsFast = false,
                    GamingId = 0,
                    Parked = false,
                    ClientInfo = new
                    {
                        ChannelType = 13,
                        DeviceType = 23,
                        IbCode = IbCode
                    }
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/UpdateOrder", request);
        }

        public async Task<JsonElement> DeleteOrderAsync(int broker, string orderId)
        {
            if (string.IsNullOrEmpty(orderId))
                throw new ArgumentException("L'ID dell'ordine non può essere vuoto", nameof(orderId));

            // Utilizziamo l'endpoint corretto per eliminare un ordine
            var request = new
            {
                par = new
                {
                    BrokerName = broker,
                    OrderID = orderId,
                    CustomerID = BrokerConfig.CustomerId,
                    OrderTypology = 0, // Standard
                    ClientInfo = new
                    {
                        ChannelType = 13,
                        DeviceType = 23,
                        IbCode = IbCode
                    }
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/DeleteOrder", request);
        }

        public async Task<JsonElement> GetPortfolioAsync(int brokerName)
        {
            // Utilizziamo l'endpoint corretto per ottenere il portafoglio
            var request = new
            {
                cols = new[]
                {
                    "MARKET_CODE", "STOCK_CODE", "STOCK_DESCRIPTION", "PORTFOLIO_PRICE",
                    "PORTFOLIO_QUANTITY", "LAST", "PORTFOLIO_GAIN_LOSS_LAST",
                    "PORTFOLIO_GAIN_LOSS_LAST_PERC", "PORTFOLIO_BOOK", "PORTFOLIO_GAIN_LOSS_BOOK",
                    "PORTFOLIO_GAIN_LOSS_BOOK_PERC", "PORTFOLIO_CURRENCY"
                },
                filter = new
                {
                    _accountFilter = new
                    {
                        _brokerName = (object)null,
                        _bondAcctId = (object)null,
                        _dossierId = (object)null,
                        _cashAcctId = (object)null,
                        _customerCode = (object)null
                    },
                    _stockFilter = new
                    {
                        _stockTypeGroup = (object)null,
                        _stockType = (object)null,
                        _stockSubType = (object)null,
                        _stockTypeDetail = (object)null,
                        _marketCode = "",
                        _stockCode = "",
                        _searchType = (object)null,
                        _searchText = (object)null
                    },
                    _currency = (object)null,
                    _onlyShortMultiDay = false,
                    _onlyIpoDerivative = false,
                    _includeAfterHour = true,
                    _valorizationMode = 0,
                    _refreshCache = true,
                    _resetPortfolio = false,
                    _brokerName = brokerName
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Order/GetPortfolio2", request);
        }

        public async Task<decimal> GetAccountBalanceAsync()
        {
            // Utilizziamo l'endpoint corretto per ottenere il saldo
            // Utilizziamo i valori da BrokerConfig

            var request = new
            {
                cols = new[]
                {
                    "DESTINED_LIQUIDITY",
                    "BLOCKED_LIQUIDITY",
                    "AVAILABLE_LIQUIDITY",
                    "CASH_TOTAL_COMMISSION",
                    "CASH_TOTAL_SELL",
                    "CASH_TOTAL_BUY",
                    "DER_BLOCKED_LIQUIDITY",
                    "DER_TOTAL_COMMISSION",
                    "TOTAL_PREMIUM",
                    "DER_TOTAL_PROFIT_LOSS",
                    "MAX_MARGIN",
                    "DER_TOTAL_MARGIN_OVERNIGHT",
                    "DER_TOTAL_MARGIN",
                    "CASH_TOTAL_MARGIN_OVERNIGHT",
                    "CASH_TOTAL_MARGIN",
                    "BOND_TOTAL_MARGIN_OVERNIGHT",
                    "BOND_DESTINED",
                    "BOND_LOCKED",
                    "BOND_AVAILABLE",
                    "TOTAL_REAL_PROFIT_LOSS",
                    "TOTAL_POTENTIAL_PROFIT_LOSS",
                    "PROFIT_LOSS_CALCULATED",
                    "GAMING_ID",
                    "N_FINANCING_AMOUNT",
                    "N_SHORT_AMOUNT",
                    "N_ACCT_ENGAGED_TIT",
                    "N_COVER_SMD",
                    "N_PROFIT_LOSS_LP",
                    "N_PROFIT_LOSS_IPO",
                    "N_MARGIN_SMD",
                    "N_PERC_MARGIN_DECREASE_IPO"
                },
                par = new
                {
                    BrokerName = BrokerConfig.BrokerId,
                    GamingId = 0,
                    CashAccountId = BrokerConfig.CashAcctId
                }
            };

            var response = await SendRequestAsync(HttpMethod.Post, "/api/Balance/GetBalance", request);

            // Estrai e logga il saldo disponibile
            try
            {
                if (response.TryGetProperty("Data", out var dataElement) &&
                    dataElement.ValueKind == JsonValueKind.Object)
                {
                    if (dataElement.TryGetProperty("Values", out var valuesElement) &&
                        valuesElement.ValueKind == JsonValueKind.Object)
                    {
                        if (valuesElement.TryGetProperty("AVAILABLE_LIQUIDITY", out var liquidityElement))
                        {
                            var availableLiquidity = liquidityElement.GetDecimal();
                            LogRequestInfo("/api/Balance/GetBalance", "Liquidità disponibile: " + availableLiquidity);
                            return availableLiquidity;
                        }
                    }
                }

                // Se non troviamo il valore, logghiamo un avviso e lanciamo un'eccezione
                LogRequestInfo("/api/Balance/GetBalance", "Impossibile trovare il valore della liquidità disponibile nella risposta");
                throw new ApiException("Impossibile trovare il valore della liquidità disponibile nella risposta", 0, "/api/Balance/GetBalance", response.ToString());
            }
            catch (Exception ex)
            {
                LogRequestError("/api/Balance/GetBalance", JsonSerializer.Serialize(request), null,
                    "Errore durante l'estrazione della liquidità disponibile: " + ex.Message);
                throw new ApiException("Errore durante l'estrazione della liquidità disponibile", 0, "/api/Balance/GetBalance", ex.Message);
            }
        }

        // Metodo per ottenere la risposta completa del saldo (per compatibilità)
        public async Task<JsonElement> GetAccountBalanceResponseAsync()
        {
            // Utilizziamo l'endpoint corretto per ottenere il saldo
            // Utilizziamo i valori da BrokerConfig

            var request = new
            {
                cols = new[]
                {
                    "DESTINED_LIQUIDITY",
                    "BLOCKED_LIQUIDITY",
                    "AVAILABLE_LIQUIDITY",
                    "CASH_TOTAL_COMMISSION",
                    "CASH_TOTAL_SELL",
                    "CASH_TOTAL_BUY",
                    "DER_BLOCKED_LIQUIDITY",
                    "DER_TOTAL_COMMISSION",
                    "TOTAL_PREMIUM",
                    "DER_TOTAL_PROFIT_LOSS",
                    "MAX_MARGIN",
                    "DER_TOTAL_MARGIN_OVERNIGHT",
                    "DER_TOTAL_MARGIN",
                    "CASH_TOTAL_MARGIN_OVERNIGHT",
                    "CASH_TOTAL_MARGIN",
                    "BOND_TOTAL_MARGIN_OVERNIGHT",
                    "BOND_DESTINED",
                    "BOND_LOCKED",
                    "BOND_AVAILABLE",
                    "TOTAL_REAL_PROFIT_LOSS",
                    "TOTAL_POTENTIAL_PROFIT_LOSS",
                    "PROFIT_LOSS_CALCULATED",
                    "GAMING_ID",
                    "N_FINANCING_AMOUNT",
                    "N_SHORT_AMOUNT",
                    "N_ACCT_ENGAGED_TIT",
                    "N_COVER_SMD",
                    "N_PROFIT_LOSS_LP",
                    "N_PROFIT_LOSS_IPO",
                    "N_MARGIN_SMD",
                    "N_PERC_MARGIN_DECREASE_IPO"
                },
                par = new
                {
                    BrokerName = BrokerConfig.BrokerId,
                    GamingId = 0,
                    CashAccountId = BrokerConfig.CashAcctId
                }
            };

            return await SendRequestAsync(HttpMethod.Post, "/api/Balance/GetBalance", request);
        }

        // Metodo per recuperare gli allarmi
        public async Task<JsonElement> GetAdviceAsync()
        {
            var request = new
            {
                cols = new[] { "ADVICE_IMAGE", "ADVICE_TIME", "ADVICE_TEXT" },
                filter = new
                {
                    _newsMinutes = 0,
                    _orderLimit = 10,
                    _orderBy = 0,
                    _types = new[] { 0, 1, 2, 3, 4, 5, 6, 7 }
                }
            };

            return await SendRequestAsync("POST", "api/Advice/GetAdvice", request);
        }

        // Metodo generico per inviare richieste
        public async Task<JsonElement> SendRequestAsync(HttpMethod httpMethod, string endpoint, object requestBody = null)
        {
            try
            {
                HttpResponseMessage response;
                string requestUrl = $"{BaseUrl}{endpoint}";

                // Registra la richiesta nei log
                string requestContent = requestBody != null
                    ? JsonSerializer.Serialize(requestBody)
                    : "{}";
                RequestLogs.Add(new RequestLog
                {
                    Endpoint = endpoint,
                    Method = httpMethod.Method,
                    RequestBody = requestContent,
                    Timestamp = DateTime.Now
                });

                if (httpMethod == HttpMethod.Get)
                {
                    response = await _httpClient.GetAsync(requestUrl);
                }
                else if (httpMethod == HttpMethod.Post)
                {
                    var content = new StringContent(
                        JsonSerializer.Serialize(requestBody ?? new {}),
                        Encoding.UTF8,
                        "application/json");
                    response = await _httpClient.PostAsync(requestUrl, content);
                }
                else if (httpMethod == HttpMethod.Put)
                {
                    var content = new StringContent(
                        JsonSerializer.Serialize(requestBody ?? new {}),
                        Encoding.UTF8,
                        "application/json");
                    response = await _httpClient.PutAsync(requestUrl, content);
                }
                else if (httpMethod == HttpMethod.Delete)
                {
                    response = await _httpClient.DeleteAsync(requestUrl);
                }
                else
                {
                    throw new ApiException("Metodo HTTP non supportato", 0, endpoint, "");
                }

                string responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new ApiException(
                        $"Richiesta fallita: {response.StatusCode}",
                        (int)response.StatusCode,
                        endpoint,
                        responseContent);
                }

                // Registra la risposta nei log
                if (RequestLogs.Any())
                {
                    var lastLog = RequestLogs.Last();
                    lastLog.ResponseBody = responseContent;
                    lastLog.ResponseContent = responseContent;
                    lastLog.StatusCode = (int)response.StatusCode;
                }

                // Parse JSON response
                return string.IsNullOrEmpty(responseContent)
                    ? new JsonElement()
                    : JsonDocument.Parse(responseContent).RootElement;
            }
            catch (ApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new ApiException(
                    $"Errore durante la richiesta: {ex.Message}",
                    0,
                    endpoint,
                    ex.ToString());
            }
        }

        public async Task<JsonElement> GetProfitLossAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            fromDate ??= DateTime.Now.AddMonths(-1); // Default: ultimo mese
            toDate ??= DateTime.Now;

            var payload = new
            {
                cols = new[] { "MARKET_CODE", "STOCK_CODE", "PROFIT_LOSS_QUANTITY", "PROFIT_LOSS_BUY_PRICE", "PROFIT_LOSS_SELL_PRICE", "PROFIT_LOSS_GAIN_LOSS" },
                filter = new
                {
                    _accountFilter = new {},
                    _stockFilter = new
                    {
                        _stockTypeGroup = (object)null,
                        _stockType = (object)null,
                        _stockSubType = (object)null,
                        _stockTypeDetail = (object)null,
                        _marketCode = (object)null,
                        _stockCode = (object)null,
                        _searchType = (object)null,
                        _searchText = (object)null
                    },
                    BrokerName = BrokerConfig.BrokerId,
                    _fromDate = fromDate.Value.ToString("yyyy-MM-dd"),
                    _toDate = toDate.Value.ToString("yyyy-MM-dd"),
                    _gamingId = 0,
                    _refreshCache = false,
                    _currency = (object)null,
                    _closingOrderId = (object)null,
                    _valorizationMode = 0,
                    _profitLossType = 0,
                    ClientInfo = new
                    {
                        ChannelType = BrokerConfig.ChannelType,
                        DeviceType = BrokerConfig.DeviceType,
                        IbCode = IbCode
                    }
                }
            };

            var jsonPayload = JsonSerializer.Serialize(payload);
            return await PostAndParseAsync("/api/Order/GetProfitLoss", jsonPayload);
        }


        // Metodo SendRequestAsync con parametro string method
        public async Task<JsonElement> SendRequestAsync(string method, string endpoint, object data)
        {
            HttpMethod httpMethod = method.ToUpper() switch
            {
                "GET" => HttpMethod.Get,
                "POST" => HttpMethod.Post,
                "PUT" => HttpMethod.Put,
                "DELETE" => HttpMethod.Delete,
                _ => throw new ArgumentException($"Metodo HTTP non supportato: {method}", nameof(method))
            };

            return await SendRequestAsync(httpMethod, endpoint, data);
        }
    }
}