﻿using Microsoft.Extensions.Logging;
using Spectre.Console;

namespace MagicOnionCallsTester
{
    internal class Program
    {
        static void Main(string[] args)
        {
            ILoggerFactory factory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole(opts =>
                {
                });
            });

            ILogger<Program> logger = factory.CreateLogger<Program>();

            CaseSelector cs = new CaseSelector(factory.CreateLogger<CaseSelector>());

            while (true)
            {
                (int, string)[] services = [
                    (0, "CacheProviderCalls"),
                    (1, "CustomerProviderCalls"),
                    (2, "VirtualBrokerCalls"),
                    (3, "XtradingBrokerTest"),
                    (99, "Quit")
                ];

                var selectedService = AnsiConsole.Prompt(new SelectionPrompt<int>()
                    .Title("Select service")
                    .AddChoices(services.Select(i => i.Item1))
                    .UseConverter(i => services.Where(j => j.Item1 == i).First().Item2)
                    );

                Task t;
                switch (selectedService)
                {
                    case 0:
                        t = cs.CacheProviderTest();
                        break;
                    case 1:
                        t = cs.CustomerProviderTest();
                        break;
                    case 2:
                        t = cs.VirtualBrokerTest();
                        break;
                    case 3:
                        t = cs.XtradingBrokerTest();
                        break;
                    case 99:
                        return;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(selectedService));
                }

                try
                {
                    t.Wait();
                }
                catch (Exception ex)
                {
                    logger.LogWarning("Error: {e}", ex.Message);
                }

                Console.ReadLine();
            }
        }
    }
}
