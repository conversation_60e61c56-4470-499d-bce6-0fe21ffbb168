#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import time
import logging
import os
import sys
import datetime
import requests
import random
import argparse
from typing import Dict, Any, Optional, List, Union
from dotenv import load_dotenv
import google.generativeai as genai

# Parsing degli argomenti della riga di comando
parser = argparse.ArgumentParser(description='Test del flusso di trading OT')
parser.add_argument('--k', type=int, choices=[0, 1], default=1, 
                    help='Attiva (1) o disattiva (0) lo scaricamento e l\'analisi dei log Kubernetes (default: 1)')
args = parser.parse_args()

# Configurazione logging
log_file = "logs/trading_flow_test.log"
# Creo la directory logs se non esiste
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file, mode='a', encoding='utf-8')
    ]
)
logger = logging.getLogger("trading_test")
logger.info("Logging configurato sia nella console che nel file %s", log_file)
logger.info(f"Parametri: --k={args.k} (Analisi Kubernetes: {'Attiva' if args.k == 1 else 'Disattiva'})")

# Creo anche la directory reports se non esiste
reports_dir = "reports"
os.makedirs(reports_dir, exist_ok=True)

# Carica variabili d'ambiente da .env
load_dotenv()

# Configura Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-04-17') #'gemini-2.5-pro-preview-05-06')
        logger.info("API Gemini configurata con successo")
    except Exception as e:
        logger.warning(f"Impossibile configurare l'API Gemini: {str(e)}")
        gemini_model = None
else:
    logger.warning("GEMINI_API_KEY non trovata nelle variabili d'ambiente")
    gemini_model = None

class OTTradingClient:
    """Client per interagire con le API di trading OT"""
    
    def __init__(self, base_url: str = None, timeout: int = 30):
        """
        Inizializza il client
        
        Args:
            base_url: URL base dell'API (es. https://ot.tst.sella.it)
            timeout: Timeout per le richieste in secondi
        """
        self.base_url = base_url or os.getenv("OT_API_BASE_URL")
        if not self.base_url:
            raise ValueError("URL base non specificato. Imposta OT_API_BASE_URL nell'env o passalo come parametro.")
            
        self.timeout = timeout
        self.session = requests.Session()
        # Header standard
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "OTTestClient/1.0"
        })
        self.auth_token = None
        self.ibcode = None  # Codice cliente
        self.request_logs = []  # Registro delle richieste per analisi errori
        
    def login(self, username: str = None, password: str = None) -> Dict[str, Any]:
        """
        Esegue il login usando l'endpoint Dummy Login
        
        Args:
            username: Nome utente (se non passato, preso da env OT_USERNAME)
            password: Password (se non passata, presa da env OT_PASSWORD)
            
        Returns:
            Risposta JSON dell'API
        """
        username = username or os.getenv("OT_USERNAME")
        password = password or os.getenv("OT_PASSWORD")
        
        if not username or not password:
            raise ValueError("Credenziali non specificate. Imposta OT_USERNAME e OT_PASSWORD nell'env o passale come parametri.")
            
        logger.info(f"Autenticazione come {username}...")
        
        endpoint = "/api/Account/DummyLoginApi"
        payload = {
            "UserName": username,
            "Password": password
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Salva ibcode per le richieste successive
            self.ibcode = username  # Assumiamo che username sia l'ibcode
            
            # Estrai e salva il token di autenticazione
            # token_keys = ['token', 'accessToken', 'access_token', 'authToken', 'auth_token']
            # for key in token_keys:
            #     if key in result:
            #         self.auth_token = result[key]
            #         self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
            #         logger.info(f"Token di autenticazione salvato da campo '{key}'")
            #         break
            
            # Se token non trovato, cerca nei cookie
            # if not self.auth_token:
            #     for cookie in self.session.cookies:
            #         if 'auth' in cookie.name.lower() or 'token' in cookie.name.lower():
            #             self.auth_token = cookie.value
            #             self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})
            #             logger.info(f"Token di autenticazione salvato da cookie '{cookie.name}'")
            #             break
            for cookie in self.session.cookies:
                logger.info(f"Cookie trovato: {cookie.name}={cookie.value}")
                
            logger.info(f"Headers di risposta: {response.headers}")

            logger.info(f"Login effettuato con successo: {result.get('message', 'OK')}")
            return result
            
        except requests.RequestException as e:
            error_msg = f"Errore durante il login: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def insert_order(self, 
                     market_code: str, 
                     stock_code: str, 
                     order_type: int, 
                     price: float, 
                     quantity: int,
                     broker_props: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Inserisce un nuovo ordine
        
        Args:
            market_code: Codice del mercato (es. "MTA")
            stock_code: Codice del titolo (es. "AMP")
            order_type: Tipo ordine (0=acquisto, 1=vendita)
            price: Prezzo
            quantity: Quantità
            broker_props: Proprietà del broker (opzionali)
            
        Returns:
            Risposta JSON con l'ID dell'ordine
        """
        endpoint = "/api/Order/InsertOrder"
        
        # Parametri di default del broker se non specificati
        if not broker_props:
            broker_props = {
                "BrokerCustomer": "********",
                "BondAcctId": 1300770,
                "CashAcctId": 895247,
                "DossierId": 898999,
                "BankId": 1
            }
        
        # Data di validità (oggi)
        validity_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        payload = {
            "par": {
                "BrokerName": 2,  # Broker di default
                "MarketCode": market_code,
                "StockCode": stock_code,
                "OrderType": order_type,  # 0=acquisto, 1=vendita
                "Price": price,
                "Quantity": quantity,
                "ValidityDate": validity_date,
                "EvaluationMode": 0,
                "OrderFast": False,
                "BrokerProperties": broker_props,
                "Phase": 3,  # Sembra essere un valore standard nella collection
                "ManualOrder": False,
                "BestExecution": False,
                "BatchOrder": False,
                "ParkingOrder": False,
                "LeverageOrder": False,
                "StopPrice": 0,
                "IcebergOrderQty": None,
                "OrderParameter": 8,
                "GamingId": 0,
                "PriceType": 0,
                "StrategyEvaluationMode": 0,
                "StrategyConditionType": 0,
                "ClientInfo": {
                    "ChannelType": 12,
                    "DeviceType": 19,
                    "IbCode": self.ibcode
                }
            }
        }
        #insert_order: "raw": "{\n  \"par\": {\n    \"BrokerName\": 2,\n    //\"OrderID\": null,\n    \"MarketCode\": \"MTA\",\n    \"StockCode\": \"AMP\",\n    \"OrderType\": 0,\n    \"Price\": 35,\n    \"Quantity\": 5,\n    \"ValidityDate\": \"today\",\n    \"EvaluationMode\": 0,\n    \"OrderFast\": false,\n    \"BrokerProperties\": {\n      \"BrokerCustomer\": \"********\",\n      \"BondAcctId\": 1300770,\n      \"CashAcctId\": 895247,\n      \"DossierId\": 898999,\n      \"BankId\": 1\n    },\n    \"OrderPhase\": 3,\n    \"ManualOrder\": false,\n    \"BestExecution\": false,\n    \"BatchOrder\": false,\n    \"ParkingOrder\": false,\n    \"LeverageOrder\": false,\n    \"StopPrice\": 0,\n    \"IcebergOrderQty\": 1,\n    \"OrderParameter\": 8,\n    \"GamingId\": 0,\n    \"PriceType\": 0,\n    \"StrategyEvaluationMode\": 0,\n    \"StrategyConditionType\": 0,\n    \"ClientInfo\": {\n      \"ChannelType\": 12,\n      \"DeviceType\": 19\n    }\n  }\n}",

        logger.info(f"Inserimento ordine - Titolo: {stock_code}, Mercato: {market_code}, Tipo: {'Acquisto' if order_type == 0 else 'Vendita'}, Prezzo: {price}, Quantità: {quantity}")
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # L'ID dell'ordine può essere alla radice del JSON o dentro un oggetto data
            order_id = result.get('OrderID') or result.get('data', {}).get('OrderID', 'N/A')
            logger.info(f"Ordine inserito - ID Temporaneo: {order_id}")
            return result
        except requests.RequestException as e:
            error_msg = f"Errore durante l'inserimento dell'ordine: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def confirm_order(self, order_id: str) -> Dict[str, Any]:
        """
        Conferma un ordine precedentemente inserito
        
        Args:
            order_id: ID dell'ordine da confermare
            
        Returns:
            Risposta JSON dell'API
        """
        endpoint = "/api/Order/ConfirmInsertOrder"
        
        payload = {
            "par": {
                "BrokerName": 2,  # Broker di default
                "OrderID": order_id,
                "ClientInfo": {
                    "ChannelType": 12,
                    "DeviceType": 19,
                    "IbCode": self.ibcode
                }
            }
        }
        
        logger.info(f"Conferma ordine - ID: {order_id}")
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Verifica se c'è un nuovo ID dell'ordine nella risposta
            confirmed_order_id = None
            
            # Cerca l'ID all'interno dei campi più comuni
            # Prima cerca nella radice del JSON
            if 'OrderID' in result:
                confirmed_order_id = result['OrderID']
            # Poi cerca nel campo 'data'
            elif 'data' in result and isinstance(result['data'], dict):
                data = result['data']
                if 'OrderID' in data:
                    confirmed_order_id = data['OrderID']
                # Naviga più a fondo se necessario
                elif 'order' in data and isinstance(data['order'], dict):
                    if 'OrderID' in data['order']:
                        confirmed_order_id = data['order']['OrderID']
            
            if confirmed_order_id and confirmed_order_id != order_id:
                logger.info(f"ID ordine cambiato dopo la conferma: {order_id} -> {confirmed_order_id}")
                # Aggiorna result con l'ID confermato per facilitare l'estrazione
                result['confirmed_order_id'] = confirmed_order_id
            else:
                # Se non è stato trovato un nuovo ID o è uguale, mantieni l'ID originale
                result['confirmed_order_id'] = order_id
            
            logger.info(f"Ordine confermato - Risultato: {result.get('message', 'OK')}")
            return result
        except requests.RequestException as e:
            error_msg = f"Errore durante la conferma dell'ordine: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def get_order_status(self, order_id: Optional[str] = None, from_date: Optional[str] = None, to_date: Optional[str] = None, retry_count: int = 2) -> Dict[str, Any]:
        """
        Ottiene lo stato degli ordini, filtrabili per ID o date
        
        Args:
            order_id: ID ordine specifico (opzionale)
            from_date: Data inizio (opzionale)
            to_date: Data fine (opzionale)
            retry_count: Numero di tentativi se l'ordine non viene trovato
            
        Returns:
            Risposta JSON con gli ordini e i loro stati
        """
        endpoint = "/api/Order/GetOrderStatus2"
        
        # Se non sono specificate le date, usa l'intervallo degli ultimi 7 giorni
        if not from_date:
            from_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%Y-%m-%d")
        if not to_date:
            to_date = datetime.datetime.now().strftime("%Y-%m-%d")
            
        payload = {
            "cols": [
                "VALIDITY_DATE",
                "ISIN",
                "CASH_ACCOUNT_CODE",
                "CALL_PUT",
                "DERIVATIVES_ID",
                "BOND_ACCOUNT_CODE",
                "ORDER_INSERT_DATE",
                "ORDER_TIPOLOGY",
                "ORDER_STATUS",
                "STOCK_CODE",
                "STOCK_DESCRIPTION",
                "PRICE_TYPE",
                "STOP_PRICE",
                "VARIATION",
                "VOLUME",
                "LAST",
                "TIME_LAST"
            ],
            "filter": {
                "_brokerName": 2,
                "_fromDate": from_date,
                "_toDate": to_date,
                "_orderShowType": 0,
                "_accountFilter": {},
                "_stockFilter": {
                    "_stockTypeGroup": None,
                    "_stockType": None,
                    "_stockSubType": None,
                    "_stockTypeDetail": None,
                    "_marketCode": None,
                    "_stockCode": None,
                    "_searchType": None,
                    "_searchText": None
                },
                "_showBuyOrders": True,
                "_showSellOrders": True,
                "_showLeverageOrders": False,
                "_showSpecialOrders": False,
                "_onlyMyOrders": False,
                "_orderTipology": 0,
                "_orderId": order_id,  # Questo è l'unico parametro che cambia rispetto al template
                "_orderType": None,
                "_onlyOrdersWithExe": False,
                "_refreshCache": True,
                "_gamingId": 0,
                "ClientInfo": {
                    "ChannelType": 12,
                    "DeviceType": 19,
                    "BankId": 1,
                    "IbCode": self.ibcode
                }
            }
        }
        
        logger.info(f"Verifica stato ordini - {'ID: ' + order_id if order_id else 'Tutti gli ordini recenti'}")
        
        for attempt in range(retry_count + 1):
            try:
                # No logging del payload qui
                if attempt > 0:
                    logger.info(f"Tentativo {attempt}/{retry_count} di recuperare lo stato dell'ordine {order_id}")
                    # Attendi un po' prima di riprovare (con tempo crescente)
                    time.sleep(2 * attempt)
                
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=payload,
                    timeout=self.timeout
                )
                
                response.raise_for_status()
                result = response.json()
                
                # Log della risposta completa
                logger.info(f"Risposta completa GetOrderStatus2: {json.dumps(result, indent=2)}")
                
                # Log della risposta
                logger.info("Riepilogo ordini trovati:")
                if 'Data' in result:  # Risposta nel formato corretto
                    orders = result['Data']
                    if isinstance(orders, list) and orders:
                        for idx, order in enumerate(orders):
                            logger.info(f"Ordine {idx+1}:")
                            # Per la nuova struttura, estrai i campi dal Tag
                            if 'Tag' in order and isinstance(order['Tag'], dict):
                                tag = order['Tag']
                                logger.info(f"  OrderID: {tag.get('OrderID')}")
                                logger.info(f"  Broker: {tag.get('Broker')}")
                                if 'Stock' in tag and isinstance(tag['Stock'], dict):
                                    logger.info(f"  MarketCode: {tag['Stock'].get('MarketCode')}")
                                    logger.info(f"  StockCode: {tag['Stock'].get('StockCode')}")
                                logger.info(f"  Status: {order.get('Status')}")
                                logger.info(f"  Price: {tag.get('Price')}")
                                logger.info(f"  Quantity: {tag.get('Quantity')}")
                            # Per la vecchia struttura, estrai i campi rilevanti
                            else:
                                relevant_fields = ['ORDER_TIPOLOGY', 'ORDER_STATUS', 'STOCK_CODE', 'STOCK_DESCRIPTION', 'PRICE_TYPE', 'VOLUME']
                                for field in relevant_fields:
                                    if field in order:
                                        logger.info(f"  {field}: {order[field]}")
                        
                        # Ordine trovato, esci dal ciclo di retry
                        return result
                    else:
                        if attempt == retry_count:
                            logger.warning(f"Nessun ordine trovato con ID: {order_id} (dopo {retry_count} tentativi)")
                        elif order_id:
                            logger.info(f"Nessun ordine trovato con ID: {order_id}, riproverò...")
                            continue  # Riprova
                
                # Se siamo arrivati qui, restituisci il risultato (anche se non abbiamo trovato ordini)
                return result
                
            except requests.RequestException as e:
                error_msg = f"Errore durante il recupero dello stato dell'ordine: {str(e)}"
                logger.error(error_msg)
                self._log_request_error(endpoint, payload, e)
                if attempt == retry_count:
                    raise Exception(error_msg)
        
        # Questo punto non dovrebbe mai essere raggiunto, ma per sicurezza
        return result
    
    def get_order_details(self, order_id: str) -> Dict[str, Any]:
        """
        Ottiene i dettagli di un ordine specifico
        
        Args:
            order_id: ID dell'ordine
            
        Returns:
            Risposta JSON con i dettagli dell'ordine
        """
        endpoint = "/api/Order/GetOrderDetail"
        
        payload = {
            "brokerName": 2,
            "orderCode": order_id,
            "_clientInfo": {
                "ChannelType": 13,
                "DeviceType": 23,
                "BankId": 0,
                "IpAddress": "0.0.0.0",
                "IbCode": self.ibcode
            }
        }
        
        logger.info(f"Richiesta dettagli ordine - ID: {order_id}")
        logger.info(f"Payload GetOrderDetail: {json.dumps(payload, indent=2)}")
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Log della risposta completa
            logger.info(f"Risposta completa GetOrderDetail: {json.dumps(result, indent=2)}")
            
            # Log dei dati più rilevanti dell'ordine
            if 'data' in result and result['data']:
                order_data = result['data']
                logger.info("Riepilogo dettagli ordine:")
                if isinstance(order_data, dict):
                    # Estrai e logga i campi più importanti
                    important_fields = ['orderCode', 'orderStatus', 'stockCode', 'stockDesc', 'marketCode', 'price', 'quantity']
                    for field in important_fields:
                        if field in order_data:
                            logger.info(f"  {field}: {order_data[field]}")
            
            return result
        except requests.RequestException as e:
            error_msg = f"Errore durante il recupero dei dettagli dell'ordine: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def get_portfolio(self) -> Dict[str, Any]:
        """
        Ottiene il portafoglio dell'utente
        
        Returns:
            Risposta JSON con i titoli in portafoglio
        """
        endpoint = "/api/Order/GetPortfolio2"
        
        payload = {
            "cols": [
                "MARKET_CODE", "STOCK_CODE", "STOCK_DESCRIPTION", "PORTFOLIO_PRICE",
                "PORTFOLIO_QUANTITY", "LAST", "PORTFOLIO_GAIN_LOSS_LAST", 
                "PORTFOLIO_GAIN_LOSS_LAST_PERC", "PORTFOLIO_BOOK", "PORTFOLIO_GAIN_LOSS_BOOK",
                "PORTFOLIO_GAIN_LOSS_BOOK_PERC", "PORTFOLIO_CURRENCY"
            ],
            "filter": {
                "_accountFilter": {
                    "_brokerName": None,
                    "_bondAcctId": None,
                    "_dossierId": None,
                    "_cashAcctId": None,
                    "_customerCode": None
                },
                "_stockFilter": {
                    "_stockTypeGroup": None,
                    "_stockType": None,
                    "_stockSubType": None,
                    "_stockTypeDetail": None,
                    "_marketCode": "",
                    "_stockCode": "",
                    "_searchType": None,
                    "_searchText": None
                },
                "_currency": None,
                "_onlyShortMultiDay": False,
                "_onlyIpoDerivative": False,
                "_includeAfterHour": True,
                "_valorizationMode": 0,
                "_refreshCache": True,
                "_resetPortfolio": False,
                "_brokerName": 2
            }
        }
        
        logger.info("Richiesta portafoglio")
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            return result
        except requests.RequestException as e:
            error_msg = f"Errore durante il recupero del portafoglio: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def get_account_balance(self) -> Dict[str, Any]:
        """
        Ottiene il saldo del conto dell'utente
        
        Returns:
            Risposta JSON con il saldo del conto
        """
        endpoint = "/api/Balance/GetBalance"
        
        # Estrai i dettagli del broker dalle proprietà salvate durante il login o utilizza i valori di default
        broker_props = {
            "BrokerCustomer": "********",
            "BondAcctId": 1300770,
            "CashAcctId": 895247,
            "DossierId": 898999,
            "BankId": 1
        }
        
        payload = {
            "cols": [
                "DESTINED_LIQUIDITY",
                "BLOCKED_LIQUIDITY",
                "AVAILABLE_LIQUIDITY",
                "CASH_TOTAL_COMMISSION",
                "CASH_TOTAL_SELL",
                "CASH_TOTAL_BUY",
                "DER_BLOCKED_LIQUIDITY",
                "DER_TOTAL_COMMISSION",
                "TOTAL_PREMIUM",
                "DER_TOTAL_PROFIT_LOSS",
                "MAX_MARGIN",
                "DER_TOTAL_MARGIN_OVERNIGHT",
                "DER_TOTAL_MARGIN",
                "CASH_TOTAL_MARGIN_OVERNIGHT",
                "CASH_TOTAL_MARGIN",
                "BOND_TOTAL_MARGIN_OVERNIGHT",
                "BOND_DESTINED",
                "BOND_LOCKED",
                "BOND_AVAILABLE",
                "TOTAL_REAL_PROFIT_LOSS",
                "TOTAL_POTENTIAL_PROFIT_LOSS",
                "PROFIT_LOSS_CALCULATED",
                "GAMING_ID",
                "N_FINANCING_AMOUNT",
                "N_SHORT_AMOUNT",
                "N_ACCT_ENGAGED_TIT",
                "N_COVER_SMD",
                "N_PROFIT_LOSS_LP",
                "N_PROFIT_LOSS_IPO",
                "N_MARGIN_SMD",
                "N_PERC_MARGIN_DECREASE_IPO"
            ],
            "par": {
                "BrokerName": 2,  # Broker di default
                "GamingId": 0,
                "CashAccountId": broker_props.get("CashAcctId", 0)
            }
        }
        
        logger.info("Richiesta saldo conto")
        
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Log del saldo disponibile - il valore è in Data.Values.AVAILABLE_LIQUIDITY
            available_balance = None
            if 'Data' in result:
                data = result['Data']
                
                # Naviga alla struttura corretta (Values)
                if isinstance(data, dict) and 'Values' in data:
                    values = data['Values']
                    if 'AVAILABLE_LIQUIDITY' in values:
                        available_balance = values['AVAILABLE_LIQUIDITY']
                        logger.info(f"Liquidità disponibile: {available_balance}")
                
                # Se non abbiamo ancora trovato il saldo, registra l'errore
                if available_balance is None:
                    logger.warning("Impossibile trovare il valore della liquidità disponibile nella risposta")
                
            return result
        except requests.RequestException as e:
            error_msg = f"Errore durante il recupero del saldo: {str(e)}"
            logger.error(error_msg)
            self._log_request_error(endpoint, payload, e)
            raise Exception(error_msg)
    
    def _log_request_error(self, endpoint: str, payload: Dict, error: Exception) -> None:
        """
        Registra le informazioni sulla richiesta fallita per l'analisi successiva
        
        Args:
            endpoint: Endpoint API
            payload: Dati inviati
            error: Errore riscontrato
        """
        error_info = {
            "timestamp": datetime.datetime.now().isoformat(),
            "endpoint": endpoint,
            "payload": payload,
            "error": str(error),
            "response": None
        }
        
        if isinstance(error, requests.RequestException) and error.response is not None:
            try:
                error_info["response"] = {
                    "status_code": error.response.status_code,
                    "content": error.response.text,
                    "headers": dict(error.response.headers)
                }
            except:
                error_info["response"] = "Impossibile serializzare la risposta"
        
        self.request_logs.append(error_info)
        logger.debug(f"Errore di richiesta registrato: {json.dumps(error_info, indent=2)}")


def wait_for_order_completion(client: OTTradingClient, order_id: str, max_attempts: int = 5, 
                             initial_delay: int = 3, max_delay: int = 30, backoff_factor: float = 1.5) -> dict:
    """
    Attende che un ordine venga completato, verificandone periodicamente lo stato con backoff esponenziale
    
    Args:
        client: Client OT
        order_id: ID dell'ordine da monitorare
        max_attempts: Numero massimo di tentativi
        initial_delay: Ritardo iniziale tra i tentativi in secondi
        max_delay: Ritardo massimo tra i tentativi in secondi
        backoff_factor: Fattore di incremento del ritardo
        
    Returns:
        Stato finale dell'ordine
    """
    # Stati finali dell'ordine che indicano che la transazione è completata
    # Basati sull'enum OrderStatus:
    # None = 0, Wait = 1, Accepted = 2, Rejected = 4, PartialDeleted = 8, 
    # Deleted = 16, PartialExecuted = 32, Executed = 64, NotExecuted = 128,
    # Parked = 512, Sent = 1024, Batch = 2048
    final_states = [4, 16, 32, 64, 128]  # Solo valori interi dell'enum C#
    
    # Stati temporanei per cui ha senso riprovare
    retry_states = [0, 1, 2, 512, 1024, 2048]  # Solo valori interi dell'enum C#
    
    # Stati che richiedono un'attenzione particolare
    warning_states = [8, 32]  # Solo valori interi dell'enum C#
    
    current_delay = initial_delay
    last_status = None
    last_status_str = None  # Per il logging della stringa descrittiva
    
    for attempt in range(1, max_attempts + 1):
        logger.info(f"Verifica stato ordine {order_id} - Tentativo {attempt}/{max_attempts}")
        
        try:
            response = client.get_order_status(order_id=order_id)
            
            # Nuova struttura JSON: l'ordine è in Data[i].Tag.OrderID
            target_order = None
            
            if 'Data' in response and isinstance(response['Data'], list):
                for order_data in response['Data']:
                    # Verifica se c'è il Tag con l'OrderID
                    if 'Tag' in order_data and isinstance(order_data['Tag'], dict):
                        tag = order_data['Tag']
                        if 'OrderID' in tag and tag['OrderID'] == order_id:
                            # Trovato l'ordine, crea un dizionario con i dati rilevanti
                            # Lo stato si trova direttamente in order_data.Status, non in Tag
                            target_order = {
                                'ORDER_CODE': tag['OrderID'],
                                'ORDER_STATUS': order_data.get('Status'),  # Questo è il campo Status corretto
                                'STOCK_CODE': tag.get('Stock', {}).get('StockCode'),
                                'MARKET_CODE': tag.get('Stock', {}).get('MarketCode'),
                                'PRICE': tag.get('Price'),
                                'QUANTITY': tag.get('Quantity')
                            }
                            break
            
            # Se non troviamo l'ordine con la nuova struttura, proviamo anche con la vecchia per compatibilità
            if not target_order:
                # Vecchia struttura: check in data.data[i].ORDER_CODE
                orders = response.get('data', {}).get('data', [])
                for order in orders:
                    if order.get('ORDER_CODE') == order_id:
                        target_order = order
                        break
            
            if not target_order:
                logger.warning(f"Ordine {order_id} non trovato nella risposta")
                # Aggiungi jitter al delay (±10%)
                jitter = random.uniform(0.9, 1.1)
                wait_time = current_delay * jitter
                logger.info(f"Ordine ancora non visibile. Attesa di {wait_time:.2f} secondi...")
                time.sleep(wait_time)
                
                # Aumenta il delay per il prossimo tentativo
                current_delay = min(current_delay * backoff_factor, max_delay)
                continue
                
            # Ottieni lo stato dell'ordine (Status nel JSON - sempre un intero in C#)
            status_numerical = target_order.get('ORDER_STATUS')
            
            # Gestisci il caso None
            if status_numerical is None:
                status_numerical = 0  # None = 0 nell'enum
                logger.warning(f"Stato dell'ordine non trovato, assegnato default: 0 (None)")
            
            # Converti l'intero in stringa descrittiva solo per il logging
            # Mappa degli stati numerici basata sull'enum OrderStatus
            status_map = {
                0: "None",
                1: "Wait",
                2: "Accepted",
                4: "Rejected",
                8: "PartialDeleted",
                16: "Deleted",
                32: "PartialExecuted",
                64: "Executed",
                128: "NotExecuted", 
                512: "Parked",
                1024: "Sent",
                2048: "Batch"
            }
            status_str = status_map.get(status_numerical, f"Unknown({status_numerical})")
            
            # Registra cambiamenti nello stato
            if status_numerical != last_status:
                last_status_str = status_map.get(last_status, f"Unknown({last_status})") if last_status is not None else "None"
                logger.info(f"Stato ordine cambiato: {last_status_str} ({last_status}) -> {status_str} ({status_numerical})")
                last_status = status_numerical
                last_status_str = status_str
            else:
                logger.info(f"Stato ordine: {status_str} ({status_numerical})")
            
            # Verifica se l'ordine è stato completato o rifiutato
            # Controlla SOLO il valore numerico
            if status_numerical in final_states:
                logger.info(f"Ordine {order_id} finito con stato: {status_str} (codice: {status_numerical})")
                return target_order
            
            # Verifica stati di warning che potrebbero richiedere attenzione
            if status_numerical in warning_states:
                logger.warning(f"Ordine {order_id} in stato parziale: {status_str} (codice: {status_numerical}). Potrebbe richiedere ulteriori azioni.")
                
            # Per gli altri stati, attesa e retry
            # Aggiungi jitter al delay (±10%)
            jitter = random.uniform(0.9, 1.1)
            wait_time = current_delay * jitter
            logger.info(f"Ordine ancora in elaborazione: {status_str} (codice: {status_numerical}). Attesa di {wait_time:.2f} secondi...")
            time.sleep(wait_time)
            
            # Aumenta il delay per il prossimo tentativo (backoff esponenziale)
            current_delay = min(current_delay * backoff_factor, max_delay)
                
        except Exception as e:
            logger.error(f"Errore durante la verifica dello stato dell'ordine: {str(e)}")
            # Se è un errore temporaneo, continua con backoff
            time.sleep(current_delay)
            current_delay = min(current_delay * backoff_factor, max_delay)
    
    logger.warning(f"Raggiunto numero massimo di tentativi ({max_attempts}). Stato ordine ancora non definitivo.")
    return {"ORDER_CODE": order_id, "ORDER_STATUS": last_status or "UNKNOWN", "error": "Timeout"}


def verify_portfolio_update(client: OTTradingClient, stock_code: str, expected_change: int) -> bool:
    """
    Verifica che il portafoglio sia stato aggiornato con la nuova posizione
    
    Args:
        client: Client OT
        stock_code: Codice del titolo
        expected_change: Variazione attesa nella quantità (positiva=acquisto, negativa=vendita)
        
    Returns:
        True se la variazione è confermata, False altrimenti
    """
    logger.info(f"Verifica aggiornamento portafoglio per {stock_code} (variazione attesa: {expected_change})")
    
    # Implementazione di retry con backoff per il portafoglio
    max_attempts = 5
    delay = 2
    
    for attempt in range(1, max_attempts + 1):
        try:
            response = client.get_portfolio()
            portfolio_items = response.get('data', {}).get('data', [])
            
            # Cerca il titolo nel portafoglio
            for item in portfolio_items:
                if item.get('STOCK_CODE') == stock_code:
                    quantity = item.get('PORTFOLIO_QUANTITY', 0)
                    logger.info(f"Titolo {stock_code} trovato in portafoglio con quantità: {quantity}")
                    
                    # Non possiamo verificare direttamente la variazione, ma solo la presenza
                    # Per una verifica completa, servirebbe salvare lo stato precedente del portafoglio
                    return True
            
            # Se il titolo non è stato trovato
            if expected_change > 0:
                logger.warning(f"Titolo {stock_code} non trovato in portafoglio dopo l'acquisto - tentativo {attempt}/{max_attempts}")
                if attempt < max_attempts:
                    time.sleep(delay)
                    delay *= 1.5  # Backoff
                    continue
                return False
            else:
                # Se era una vendita e il titolo non c'è più, potrebbe essere corretto
                logger.info(f"Titolo {stock_code} non presente in portafoglio (potrebbe essere stato venduto completamente)")
                return True
        
        except Exception as e:
            logger.error(f"Errore durante la verifica del portafoglio (tentativo {attempt}/{max_attempts}): {str(e)}")
            if attempt < max_attempts:
                time.sleep(delay)
                delay *= 1.5  # Backoff
                continue
            return False
    
    return False


def analyze_error_with_gemini(client: OTTradingClient) -> str:
    """
    Analizza i log degli errori usando Gemini, includendo le informazioni di mappatura API
    per determinare la causa principale e il componente coinvolto
    
    Args:
        client: Client OT con log di errore
        
    Returns:
        Analisi dell'errore
    """
    if not gemini_model or not client.request_logs:
        return "Analisi errori non disponibile (Gemini non configurato o nessun errore registrato)"
    
    try:
        # Formatta i log degli errori per l'analisi
        logs_json = json.dumps(client.request_logs, indent=2)
        
        # Legge il file di mappatura API
        api_mapping_file = "API/api_mapping_base_names.md"
        api_mapping_content = ""
        try:
            with open(api_mapping_file, "r", encoding="utf-8") as f:
                api_mapping_content = f.read()
            logger.info(f"File di mappatura API caricato: {api_mapping_file}")
        except Exception as e:
            logger.warning(f"Impossibile leggere il file di mappatura API: {str(e)}")
            api_mapping_content = "File di mappatura API non disponibile"
        
        # Estrae gli endpoint coinvolti dagli errori
        endpoints = []
        for log in client.request_logs:
            if endpoint := log.get("endpoint"):
                # Estrae il nome base dell'endpoint (es. da "/api/Order/InsertOrder" ottiene "InsertOrder")
                endpoint_parts = endpoint.split('/')
                if len(endpoint_parts) > 0:
                    base_name = endpoint_parts[-1]
                    endpoints.append(base_name)
        
        endpoints_str = ", ".join(endpoints) if endpoints else "Nessun endpoint specifico trovato"
        
        # Prepara il prompt per Gemini
        prompt = f"""
        Analizza questi log di errore delle richieste API verso un sistema di trading OT.
        
        Endpoints coinvolti negli errori: {endpoints_str}
        
        Utilizza le seguenti informazioni di mappatura API per analizzare gli errori e determinare 
        in quale componente si verifica il problema:
        
        --- MAPPATURA API ---
        {api_mapping_content[:30000] if len(api_mapping_content) > 30000 else api_mapping_content}
        --- FINE MAPPATURA API ---
        
        Logs di errore:
        {logs_json}
        
        Considerando gli endpoint coinvolti e la mappatura API, identifica:
        1. La causa principale del fallimento
        2. Se l'errore è legato all'autenticazione, ai dati inviati, alle API di Banca Sella, o ai componenti interni
        3. Il componente o modulo specifico in XTradingBroker che potrebbe essere responsabile (basandoti sulla mappatura)
        4. Suggerimenti per risolvere il problema
        
        Fornisci una spiegazione concisa e tecnica del problema, evitando informazioni generiche.
        """
        
        # Invia la richiesta a Gemini
        response = gemini_model.generate_content(prompt)
        analysis = response.text
        
        logger.info("Analisi errori completata con Gemini (con mappatura API)")
        return analysis
    
    except Exception as e:
        logger.error(f"Errore durante l'analisi con Gemini: {str(e)}")
        return f"Impossibile analizzare gli errori: {str(e)}"


def get_kubernetes_logs() -> str:
    """
    Recupera i log dal deployment cacheprovider nel namespace ot usando kubectl
    
    Returns:
        Contenuto dei log o messaggio di errore
    """
    logger.info("Recupero dei log Kubernetes dal deployment cacheprovider nel namespace ot")
    
    cmd = "kubectl -n ot logs deployments/cacheprovider"
    
    try:
        import subprocess
        # Esegue il comando kubectl e cattura l'output
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        logs = result.stdout
        
        # Salva i log in un file separato per riferimento futuro
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        k8s_log_file = f"logs/k8s_logs_{timestamp}.log"
        
        # Creo la directory logs se non esiste
        os.makedirs(os.path.dirname(k8s_log_file), exist_ok=True)
        
        with open(k8s_log_file, "w", encoding="utf-8") as f:
            f.write(logs)
            
        logger.info(f"Log Kubernetes salvati in {k8s_log_file}")
        return logs
    
    except subprocess.CalledProcessError as e:
        error_msg = f"Errore durante l'esecuzione di kubectl: {str(e)}"
        logger.error(error_msg)
        if e.stderr:
            logger.error(f"Errore kubectl: {e.stderr}")
        return f"Impossibile recuperare i log Kubernetes: {str(e)}"
    
    except Exception as e:
        error_msg = f"Errore generico durante il recupero dei log Kubernetes: {str(e)}"
        logger.error(error_msg)
        return error_msg


def analyze_kubernetes_logs(logs: str) -> str:
    """
    Analizza i log di Kubernetes usando Gemini
    
    Args:
        logs: Contenuto dei log
        
    Returns:
        Analisi dei log
    """
    if not gemini_model or not logs:
        return "Analisi log Kubernetes non disponibile (Gemini non configurato o nessun log recuperato)"
    
    try:
        # Limita i log a una dimensione gestibile per non superare i limiti del modello
        max_log_length = 15000  # Caratteri
        truncated_logs = logs[:max_log_length]
        if len(logs) > max_log_length:
            truncated_logs += f"\n... (truncated, {len(logs) - max_log_length} more characters)"
            
        # Prepara il prompt per Gemini
        prompt = f"""
        Analizza questi log Kubernetes dal deployment 'cacheprovider' nel namespace 'ot'.
        Identifica:
        1. Eventuali errori o avvisi rilevanti
        2. Problemi di prestazioni o connettività
        3. Comportamenti anomali che potrebbero influenzare le transazioni di trading
        
        Logs:
        {truncated_logs}
        
        Fornisci un'analisi tecnica e concisa dei problemi più critici trovati nei log.
        """
        
        # Invia la richiesta a Gemini
        response = gemini_model.generate_content(prompt)
        analysis = response.text
        
        logger.info("Analisi log Kubernetes completata con Gemini")
        return analysis
    
    except Exception as e:
        logger.error(f"Errore durante l'analisi dei log Kubernetes con Gemini: {str(e)}")
        return f"Impossibile analizzare i log Kubernetes: {str(e)}"


def generate_markdown_report(client: OTTradingClient, test_params: Dict, 
                           test_result: bool, order_id: Optional[str] = None,
                           order_details: Optional[Dict] = None,
                           error_analysis: Optional[str] = None, k8s_analysis: Optional[str] = None,
                           k8s_logs_file: Optional[str] = None) -> str:
    """
    Genera un report Markdown completo dell'esecuzione del test
    
    Args:
        client: Client OT con i log delle richieste
        test_params: Parametri del test
        test_result: Risultato del test (True=successo, False=fallimento)
        order_id: ID dell'ordine (se disponibile)
        order_details: Dettagli dell'ordine (se disponibili)
        error_analysis: Analisi degli errori (se disponibile)
        k8s_analysis: Analisi dei log Kubernetes (se disponibile)
        k8s_logs_file: Percorso al file dei log Kubernetes (se disponibile)
        
    Returns:
        Percorso del file di report generato
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"reports/trading_flow_report_{timestamp}.md"
    
    # Assicurarsi che la directory reports esista
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    # Intestazione report
    report_content = f"""# Report Test Trading Flow - {datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S")}

## Riepilogo
- **Risultato**: {'✅ SUCCESSO' if test_result else '❌ FALLIMENTO'}
- **Data esecuzione**: {datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S")}
- **ID Ordine**: {order_id if order_id else "Non generato"}
- **Log file**: `{log_file}`
- **K8s logs**: `{k8s_logs_file if k8s_logs_file else "Non disponibile"}`
- **Analisi Kubernetes**: {'Attiva' if args.k == 1 else 'Disattiva'}

## Parametri del test
- **Mercato**: {test_params.get('market_code', 'N/A')}
- **Titolo**: {test_params.get('stock_code', 'N/A')}
- **Tipo ordine**: {'Acquisto' if test_params.get('order_type') == 0 else 'Vendita'}
- **Prezzo**: {test_params.get('price', 'N/A')}
- **Quantità**: {test_params.get('quantity', 'N/A')}
- **API URL**: {client.base_url}

## Dettagli esecuzione
"""
    
    # Aggiunge dettagli sulle richieste effettuate
    request_logs = client.request_logs
    if not request_logs:
        report_content += "Nessun errore registrato nelle richieste API.\n\n"
    else:
        report_content += f"### Log delle richieste con errori ({len(request_logs)})\n\n"
        for idx, log in enumerate(request_logs, 1):
            report_content += f"#### Richiesta {idx}\n"
            report_content += f"- **Timestamp**: {log.get('timestamp', 'N/A')}\n"
            report_content += f"- **Endpoint**: {log.get('endpoint', 'N/A')}\n"
            report_content += f"- **Errore**: {log.get('error', 'N/A')}\n"
            
            # Se c'è una risposta, include i dettagli
            if log.get('response'):
                resp = log['response']
                if isinstance(resp, dict):
                    report_content += f"- **Status code**: {resp.get('status_code', 'N/A')}\n"
                    
                    # Contenuto della risposta (potenzialmente molto lungo)
                    content = resp.get('content', '')
                    if content:
                        # Tronca se troppo lungo
                        if len(content) > 1000:
                            content_preview = content[:1000] + "... (troncato)"
                        else:
                            content_preview = content
                            
                        report_content += f"- **Risposta**:\n```\n{content_preview}\n```\n"
            
            report_content += "\n"
    
    # Dettagli dell'ordine (se disponibili)
    if order_details:
        report_content += "### Dettagli ordine\n\n"
        report_content += "```json\n"
        report_content += json.dumps(order_details, indent=2)
        report_content += "\n```\n\n"
    
    # Analisi degli errori (se disponibile)
    if error_analysis:
        report_content += "## Analisi degli errori\n\n"
        report_content += error_analysis
        report_content += "\n\n"
    
    # Analisi dei log Kubernetes (se disponibile)
    if k8s_analysis:
        report_content += "## Analisi dei log Kubernetes\n\n"
        report_content += k8s_analysis
        report_content += "\n\n"
        
        if k8s_logs_file:
            report_content += f"Log Kubernetes completi disponibili in: `{k8s_logs_file}`\n\n"
    
    # Legge ed include i log dal file principale
    try:
        with open(log_file, "r", encoding="utf-8") as f:
            log_content = f.read()
            
        report_content += "## Log completo dell'esecuzione\n\n"
        report_content += "```\n"
        report_content += log_content
        report_content += "\n```\n"
    except Exception as e:
        report_content += f"Impossibile leggere il file di log: {str(e)}\n"
    
    # Salva il report
    try:
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report_content)
        logger.info(f"Report Markdown generato: {report_file}")
        return report_file
    except Exception as e:
        logger.error(f"Errore durante la generazione del report Markdown: {str(e)}")
        return None


def check_portfolio_balance(client: OTTradingClient, stock_code: str, price: float, quantity: int) -> bool:
    """
    Verifica se c'è abbastanza credito disponibile per l'acquisto
    
    Args:
        client: Client OT
        stock_code: Codice del titolo
        price: Prezzo unitario
        quantity: Quantità
        
    Returns:
        True se c'è abbastanza credito, False altrimenti
    """
    logger.info(f"Verifica saldo disponibile per acquisto di {quantity} {stock_code} a {price}")
    
    try:
        # Ottieni il saldo del conto
        balance_response = client.get_account_balance()
        
        # Estrai il saldo disponibile - accedi al percorso corretto: Data.Values.AVAILABLE_LIQUIDITY
        available_balance = None
        if 'Data' in balance_response:
            data = balance_response['Data']
            
            # Accedi al percorso corretto (Values)
            if isinstance(data, dict) and 'Values' in data:
                values = data['Values']
                if 'AVAILABLE_LIQUIDITY' in values:
                    available_balance = values['AVAILABLE_LIQUIDITY']
                    logger.info(f"Liquidità disponibile from GetBalance in check_portfolio_balance: {available_balance}")
        
        if available_balance is None:
            # Se per qualche motivo non riesce a trovare il valore, registra un log ma continua
            logger.info("Nessun valore di liquidità trovato nella risposta, il test continuerà assumendo saldo sufficiente")
            return True
            
        # Calcola l'importo totale dell'ordine
        total_order_amount = price * quantity
        logger.info(f"Importo totale dell'ordine: {total_order_amount}")
        
        # Verifica se c'è abbastanza credito
        if total_order_amount > available_balance:
            logger.error(f"Saldo insufficiente per l'acquisto: disponibile {available_balance}, necessario {total_order_amount}")
            return False
        
        logger.info(f"Saldo sufficiente per l'acquisto: disponibile {available_balance}, necessario {total_order_amount}")
        return True
    
    except Exception as e:
        logger.error(f"Errore durante la verifica del saldo: {str(e)}")
        return True  # In caso di errore, assumiamo che ci sia abbastanza credito


def run_test_scenario():
    """
    Esegue uno scenario di test completo:
    1. Login
    2. Inserimento ordine
    3. Conferma ordine
    4. Monitoraggio stato ordine
    5. Verifica portafoglio
    """
    # Parametri di test dalle variabili d'ambiente (con valori di default)
    market_code = os.getenv("OT_TEST_MARKET", "MTA")
    stock_code = os.getenv("OT_TEST_STOCK", "BGN")   # Amplifon
    order_type = int(os.getenv("OT_TEST_ORDER_TYPE", "0"))  # 0=acquisto, 1=vendita
    price = float(os.getenv("OT_TEST_PRICE", "0"))
    quantity = int(os.getenv("OT_TEST_QUANTITY", "1"))
    
    test_params = {
        "market_code": market_code,
        "stock_code": stock_code,
        "order_type": order_type,
        "price": price,
        "quantity": quantity
    }
    
    logger.info(f"Parametri di test: Mercato={market_code}, Titolo={stock_code}, Tipo={order_type}, Prezzo={price}, Quantità={quantity}")
    
    # Inizializzazione client
    client = OTTradingClient()
    test_result = False
    order_details = None
    error_analysis = None
    k8s_analysis = None
    k8s_logs_file = None
    order_id = None
    
    try:
        # 1. Login
        client.login()
        
        # Verifica saldo disponibile (solo per acquisti)
        if order_type == 0:  # 0=acquisto
            enough_balance = check_portfolio_balance(client, stock_code, price, quantity)
            if not enough_balance:
                logger.error("Test interrotto: saldo insufficiente per l'acquisto")
                error_analysis = "Saldo insufficiente per effettuare l'acquisto. Verificare il conto o ridurre l'importo dell'ordine."
                return False
        
        # 2. Inserimento ordine
        insert_response = client.insert_order(
            market_code=market_code,
            stock_code=stock_code,
            order_type=order_type,
            price=price,
            quantity=quantity
        )
        
        # Estrazione ID ordine - prova a cercarlo alla radice o dentro data
        order_id = insert_response.get('OrderID') or insert_response.get('data', {}).get('OrderID')
        if not order_id:
            logger.error("Impossibile ottenere l'ID dell'ordine dalla risposta")
            # Analizziamo la struttura della risposta per capire dove si trova l'ID
            logger.info(f"Struttura risposta: {json.dumps(insert_response, indent=2)}")
            # Analizza gli errori con Gemini se disponibile
            error_analysis = analyze_error_with_gemini(client)
            logger.info(f"Analisi errori: {error_analysis}")
            return False
            
        logger.info(f"Ordine inserito con successo - ID: {order_id}")
            
        # 3. Conferma ordine
        confirm_response = client.confirm_order(order_id)
        
        # Ottieni l'ID dell'ordine dalla risposta di conferma
        confirmed_order_id = confirm_response.get('confirmed_order_id', order_id)
        if confirmed_order_id != order_id:
            logger.info(f"Utilizzo l'ID dell'ordine aggiornato dopo la conferma: {order_id} -> {confirmed_order_id}")
            order_id = confirmed_order_id
        
        logger.info(f"Ordine confermato - ID: {order_id} - Risposta: {confirm_response.get('message', 'N/A')}")
        
        # 4. Monitoraggio stato ordine fino al completamento
        final_order_state = wait_for_order_completion(client, order_id)
        
        # Verifica che l'ordine sia stato eseguito con successo
        order_status = final_order_state.get('ORDER_STATUS')
        logger.info(f"Stato finale ordine ID: {order_id} - Status: {order_status}")
        
        if order_status not in ["EXECUTED", "COMPLETED"]:
            logger.error(f"L'ordine ID: {order_id} non è stato eseguito correttamente. Stato finale: {order_status}")
            # Analizza gli errori con Gemini se disponibile
            error_analysis = analyze_error_with_gemini(client)
            logger.info(f"Analisi errori: {error_analysis}")
            return False
            
        # 5. Verifica dettagli ordine
        order_details = client.get_order_details(order_id)
        logger.info(f"Risposta completa get_order_details: {json.dumps(order_details, indent=2)}")
        logger.info(f"Dettagli ordine ID: {order_id} - {json.dumps(order_details.get('data', {}), indent=2)}")
        
        # 6. Verifica aggiornamento portafoglio
        expected_change = quantity if order_type == 0 else -quantity
        portfolio_ok = verify_portfolio_update(client, stock_code, expected_change)
        
        if portfolio_ok:
            logger.info(f"TEST SUPERATO: Ciclo di vita dell'ordine ID: {order_id} completato con successo")
            test_result = True
        else:
            logger.error(f"TEST FALLITO: Il portafoglio non riflette correttamente l'ordine ID: {order_id}")
            # Analizza gli errori con Gemini se disponibile
            error_analysis = analyze_error_with_gemini(client)
            logger.info(f"Analisi errori: {error_analysis}")
            test_result = False
            
    except Exception as e:
        logger.error(f"Errore durante l'esecuzione del test{' per ordine ID: ' + order_id if order_id else ''}: {str(e)}")
        # Analizza gli errori con Gemini se disponibile
        error_analysis = analyze_error_with_gemini(client)
        logger.info(f"Analisi errori: {error_analysis}")
        test_result = False
    
    finally:
        # Recupera e analizza i log Kubernetes solo se richiesto
        if args.k == 1:
            k8s_logs = None
            try:
                logger.info("Recupero dei log Kubernetes...")
                k8s_logs = get_kubernetes_logs()
                # Salva il riferimento al file dei log K8s
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                k8s_logs_file = f"logs/k8s_logs_{timestamp}.log"
                
                # Creo la directory logs se non esiste
                os.makedirs(os.path.dirname(k8s_logs_file), exist_ok=True)
                
                if k8s_logs:
                    logger.info("Analisi dei log Kubernetes...")
                    k8s_analysis = analyze_kubernetes_logs(k8s_logs)
                    logger.info(f"Analisi log Kubernetes: {k8s_analysis}")
            except Exception as e:
                logger.error(f"Errore durante il recupero o l'analisi dei log Kubernetes: {str(e)}")
        else:
            logger.info("Analisi dei log Kubernetes disattivata (--k=0)")
        
        # Genera il report Markdown
        try:
            report_file = generate_markdown_report(
                client=client,
                test_params=test_params,
                test_result=test_result,
                order_id=order_id,
                order_details=order_details,
                error_analysis=error_analysis,
                k8s_analysis=k8s_analysis,
                k8s_logs_file=k8s_logs_file
            )
            if report_file:
                logger.info(f"Report dettagliato disponibile in: {report_file}")
        except Exception as e:
            logger.error(f"Errore durante la generazione del report: {str(e)}")
        
        return test_result


if __name__ == "__main__":
    logger.info("=== INIZIO TEST TRADING FLOW ===")
    result = run_test_scenario()
    logger.info(f"=== FINE TEST TRADING FLOW - {'SUCCESSO' if result else 'FALLIMENTO'} ===")
    sys.exit(0 if result else 1) 