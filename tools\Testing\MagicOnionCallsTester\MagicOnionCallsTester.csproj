﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Grpc.Net.Client" Version="2.70.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
    <PackageReference Include="OT.Common.Broker" Version="1.0.0-rc.32" />
    <PackageReference Include="OT.Common.Broker.Virtual" Version="1.0.0-rc.23" />
    <PackageReference Include="OT.Common.Cache" Version="1.0.0-rc.20" />
    <PackageReference Include="OT.Common.Customer" Version="1.0.0-rc.36" />
    <PackageReference Include="Spectre.Console.Cli" Version="0.50.0" />
  </ItemGroup>

</Project>
