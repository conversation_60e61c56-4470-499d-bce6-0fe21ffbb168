﻿using MagicOnionCallsTester.Cases;
using MagicOnionCallsTester.Models.Customer;
using Microsoft.Extensions.Logging;
using Spectre.Console;
using System.Text.Json;

namespace MagicOnionCallsTester
{
    public class CaseSelector
    {
        private readonly ILogger<CaseSelector> _logger;
        private readonly Dictionary<string, BrokerCustomerCredentials> _customers;

        public CaseSelector(
            ILogger<CaseSelector> logger
            )
        {
            _logger = logger;
            _customers = new Dictionary<string, BrokerCustomerCredentials>()
            {
                ["78711"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "78711",
                    CustomerId = 6358
                },
                ["581216"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "581216",
                    CustomerId = 8708,
                },
                ["464459"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "464459",
                    CustomerId = 1116,
                },
                ["1931612"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "01931612",
                    CustomerId = 12828,
                },
                ["ID6358"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "6358",
                    CustomerId = 6358
                },
                ["507935"] = new BrokerCustomerCredentials()
                {
                    OTAccessToken = "MANUALTEST",
                    Username = "507935",
                    CustomerId = 1000
                }
            };
        }

        #region Helpers

        #region Input
        private string PromptSelectCustomer()
        {
            string selectedCustomer = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                .Title("Select customer")
                .PageSize(10)
                .MoreChoicesText("[grey](up/down)[/]")
                .AddChoices(_customers.Select(i => i.Key))
                .UseConverter(i => $"{_customers[i].CustomerId}:{_customers[i].Username}")
                );

            return selectedCustomer;
        }

        private int[] PromptSelectCase((int, string)[] testCases)
        {
            List<int> selectedCases = AnsiConsole.Prompt(
                new MultiSelectionPrompt<int>()
                .Title("Select test cases")
                .NotRequired()
                .PageSize(10)
                .MoreChoicesText("[grey](up/down)[/]")
                .InstructionsText("[grey](Press [blue]<space>[/] to toggle, [green]<enter>[/] to accept)[/]")
                .AddChoices(testCases.Select(i => i.Item1))
                .UseConverter(i => testCases[i].Item2)
                );
            return selectedCases.ToArray();
        }

        private string PromptWithConfirm(string promptDesc, Func<string, bool> promptCheck)
        {
            bool done = false;
            bool error = false;

            string input = string.Empty;
            while (!done)
            {
                input = AnsiConsole.Prompt(
                    new TextPrompt<string>(promptDesc)
                    .PromptStyle("green")
                    .AllowEmpty()
                    .DefaultValue(string.Empty)
                    );

                if (promptCheck(input))
                {
                    error = false;
                }
                else
                {
                    error = true;
                    AnsiConsole.MarkupLine("[red]Invalid input[/]");
                }

                if (!error)
                {
                    done = AnsiConsole.Prompt(new SelectionPrompt<bool>()
                    .Title($"Do you want to use {(string.IsNullOrEmpty(input) ? "an empty" : "this")} input?")
                    .AddChoices([true, false])
                    .UseConverter(i => i ? "Yes" : "No")
                    );
                }
            }

            return input;
        }
        #endregion

        private bool CheckJsonValidity(string json)
        {
            try
            {
                using JsonDocument doc = JsonDocument.Parse(json);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }
        }
        #endregion

        public async Task CacheProviderTest()
        {
            (int, string)[] testCases = [
                (0, "DropDown"),
                ];
            var selectedCases = PromptSelectCase(testCases);

            CacheProviderCalls testCalls = new CacheProviderCalls(_logger);
            foreach (var testCase in selectedCases)
            {
                switch (testCase)
                {
                    case 0:
                        testCalls = testCalls.GetDropdownCombinationsTree();
                        break;
                }
            }

            Console.WriteLine("Press any key to start");
            await testCalls.Start();
        }

        public async Task CustomerProviderTest()
        {
            string selectedCustomer = PromptSelectCustomer();

            (int, string)[] testCases = [
                (0, "Generic"),
                (1, "SetCustomer"),
                (2, "SavePersonalList"),
                (3, "GetPersonalView"),
                (4, "SavePersonalView"),
                ];
            var selectedCases = PromptSelectCase(testCases);

            CustomerProviderCalls testCalls = new CustomerProviderCalls(_customers[selectedCustomer], _logger);
            foreach (var testCase in selectedCases)
            {
                switch (testCase)
                {
                    case 0:
                        testCalls = testCalls.Test_Generic();
                        break;
                    case 1:
                        string alias = PromptWithConfirm("Customer", (p) => true);
                        testCalls = testCalls.Test_SetCustomer(alias);
                        break;
                    case 2:
                        testCalls = testCalls.Test_SavePersonalList(string.Empty);
                        break;
                    case 3:
                        testCalls = testCalls.Test_GetPersonalView();
                        break;
                    case 4:
                        testCalls = testCalls.Test_SavePersonalView(string.Empty);
                        break;
                }
            }

            Console.WriteLine("Press any key to start");
            await testCalls.Start();
        }

        public async Task VirtualBrokerTest()
        {
            string selectedCustomer = PromptSelectCustomer();

            (int, string)[] testCases = [
                (0, "Login"),
                (1, "Generic"),
                ];
            int[] selectedCases = PromptSelectCase(testCases);

            VirtualBrokerCalls testCalls = new VirtualBrokerCalls(_customers[selectedCustomer], _logger);
            foreach (var testCase in selectedCases)
            {
                switch (testCase)
                {
                    case 0:
                        testCalls = testCalls.Login();
                        break;
                    case 1:
                        //testCalls = testCalls.TestGeneric();
                        break;
                }
            }

            Console.WriteLine("Press any key to start");
            await testCalls.Start();
        }

        public async Task XtradingBrokerTest()
        {
            string selectedCustomer = PromptSelectCustomer();

            (int, string)[] testCases = [
                (0, "Login"),
                (1, "Generic"),
                (2, "Portfolio"),
                (3, "OrderStatus"),
                (4, "OrderDetail")
                ];
            int[] selectedCases = PromptSelectCase(testCases);

            XTradingBrokerCalls testCalls = new XTradingBrokerCalls(_customers[selectedCustomer], _logger);
            foreach (var testCase in selectedCases)
            {
                switch (testCase)
                {
                    case 0:
                        testCalls = testCalls.Login();
                        break;
                    case 1:
                        testCalls = testCalls.TestGeneric();
                        break;
                    case 2:
                        string jsonPortfolio = PromptWithConfirm("Portfolio filter json", (p) => string.IsNullOrWhiteSpace(p) || CheckJsonValidity(p));
                        testCalls = testCalls.GetPortfolio(jsonPortfolio);
                        break;
                    case 3:
                        string jsonOrder = PromptWithConfirm("Order filter json", (p) => string.IsNullOrWhiteSpace(p) || CheckJsonValidity(p));
                        testCalls = testCalls.GetOrderStatus(jsonOrder);
                        break;
                    case 4:
                        string jsonDetail = PromptWithConfirm("Order detail json", (p) => string.IsNullOrWhiteSpace(p) || CheckJsonValidity(p));
                        testCalls = testCalls.GetOrderDetail(jsonDetail);
                        break;
                }
            }

            Console.WriteLine("Press any key to start");
            await testCalls.Start();
        }
    }
}
