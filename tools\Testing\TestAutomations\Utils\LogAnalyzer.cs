using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using TestAutomations.Models;
using TestAutomations.Clients;

namespace TestAutomations.Utils
{
    public static class LogAnalyzer
    {
        
        public static string AnalyzeTestSteps(List<TestStepResult> steps)
        {
            if (steps == null || steps.Count == 0)
                return "Nessuno step di test disponibile.";
            
            var successCount = steps.Count(s => s.Success);
            var failureCount = steps.Count - successCount;
            
            var sb = new StringBuilder();
            sb.AppendLine($"Test completato con {successCount} successi e {failureCount} fallimenti.");
            
            if (failureCount > 0)
            {
                sb.AppendLine("\nStep falliti:");
                foreach (var failedStep in steps.Where(s => !s.Success))
                {
                    sb.AppendLine($"- {failedStep.StepName}: {failedStep.Message}");
                }
            }

            if (successCount > 0)
            {
                sb.AppendLine("\nStep completati con successo:");
                foreach (var successStep in steps.Where(s => s.Success))
                {
                    sb.AppendLine($"- {successStep.StepName}{(string.IsNullOrEmpty(successStep.Message) ? "" : $": {successStep.Message}")}");
                }
            }
            
            return sb.ToString();
        }

        public static string AnalyzeRequestLogPatterns(List<RequestLog> logs)
        {
            if (logs == null || logs.Count == 0)
                return "Nessun pattern rilevato nei log di richiesta.";
            
            var sb = new StringBuilder();
            
            // Analisi errori per endpoint
            var errorEndpoints = logs
                .Where(l => l.StatusCode >= 400)
                .GroupBy(l => l.Endpoint)
                .Select(g => new { Endpoint = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count);
            
            if (errorEndpoints.Any())
            {
                sb.AppendLine("Endpoint con errori più frequenti:");
                foreach (var endpoint in errorEndpoints)
                {
                    sb.AppendLine($"- {endpoint.Endpoint}: {endpoint.Count} errori");
                }
            }
            
            // Analisi tempi di risposta
            var avgResponseTimes = logs
                .GroupBy(l => l.Endpoint)
                .Select(g => new 
                { 
                    Endpoint = g.Key, 
                    AvgTime = g.Average(l => l.ResponseTime?.TotalMilliseconds),
                    MaxTime = g.Max(l => l.ResponseTime?.TotalMilliseconds)
                })
                .OrderByDescending(x => x.AvgTime);
            
            sb.AppendLine("\nTempi di risposta per endpoint:");
            foreach (var timing in avgResponseTimes)
            {
                sb.AppendLine($"- {timing.Endpoint}:");
                sb.AppendLine($"  Media: {timing.AvgTime:F0}ms");
                sb.AppendLine($"  Max: {timing.MaxTime:F0}ms");
            }
            
            // Analisi pattern di errori ricorrenti
            var commonErrors = logs
                .Where(l => !string.IsNullOrEmpty(l.Error))
                .GroupBy(l => l.Error)
                .Select(g => new { Error = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(5);
            
            if (commonErrors.Any())
            {
                sb.AppendLine("\nErrori più comuni:");
                foreach (var error in commonErrors)
                {
                    sb.AppendLine($"- {error.Error} (occorrenze: {error.Count})");
                }
            }
            
            return sb.ToString();
        }

        public static string SummarizeRequestLogs(List<RequestLog> logs)
        {
            if (logs == null || logs.Count == 0)
                return "Nessun log di richiesta disponibile.";

            var sb = new StringBuilder();
            foreach (var log in logs.TakeLast(10)) // Limita a 10 log più recenti
            {
                sb.AppendLine($"[{log.Timestamp:HH:mm:ss}] {log.Endpoint} - Status: {log.StatusCode} - Tempo: {log.ResponseTime?.TotalMilliseconds:F0}ms");
                if (!string.IsNullOrEmpty(log.Error))
                    sb.AppendLine($"  Errore: {log.Error}");
                if (!string.IsNullOrEmpty(log.ResponseContent))
                    sb.AppendLine($"  Risposta: {log.ResponseContent.Truncate(200)}");
            }
            return sb.ToString();
        }

        public static string SummarizeKubernetesLogs(string k8sLogs)
        {
            if (string.IsNullOrWhiteSpace(k8sLogs))
                return "Nessun log Kubernetes disponibile.";
            return k8sLogs.Length > 2000 ? k8sLogs.Substring(0, 2000) + "..." : k8sLogs;
        }

        public static string Truncate(this string value, int maxLength)
        {
            if (string.IsNullOrEmpty(value)) return value;
            return value.Length <= maxLength ? value : value.Substring(0, maxLength) + "...";
        }
    }
} 