using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using System.Text;

namespace TestAutomations.Utils
{
    public static class KubernetesLogFetcher
    {
        private static readonly string LogDir = "logs";
        private static bool _isLoggedIn = false;

        /// <summary>
        /// Recupera i log più recenti da un deployment Kubernetes
        /// </summary>
        /// <param name="deployment">Nome del deployment (default: cacheprovider)</param>
        /// <param name="ns">Namespace (default: ot)</param>
        /// <param name="tailLines">Numero di righe da recuperare (default: 1000, -1 per tutte)</param>
        /// <param name="sinceSeconds">Recupera log degli ultimi N secondi (default: null)</param>
        /// <returns>Contenuto dei log o messaggio di errore</returns>
        public static async Task<string> FetchLatestLogsAsync(
            string deployment = "cacheprovider",
            string ns = "ot",
            int tailLines = 1000,
            int? sinceSeconds = null)
        {
            try
            {
                // Assicurati che il login ad Azure sia stato effettuato
                if (!_isLoggedIn)
                {
                    var loginResult = await LoginToAzureAsync();
                    if (!loginResult.Success)
                    {
                        return $"Errore durante il login ad Azure: {loginResult.Message}";
                    }
                    _isLoggedIn = true;
                }

                // Costruisci il comando kubectl con i parametri
                var kubectlArgs = $"-n {ns} logs deployments/{deployment}";

                // Aggiungi opzioni per limitare l'output se necessario
                if (tailLines > 0)
                {
                    kubectlArgs += $" --tail={tailLines}";
                }

                if (sinceSeconds.HasValue)
                {
                    kubectlArgs += $" --since={sinceSeconds.Value}s";
                }

                // Esegui il comando kubectl
                var psi = new ProcessStartInfo
                {
                    FileName = "kubectl",
                    Arguments = kubectlArgs,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var proc = Process.Start(psi);
                if (proc == null)
                {
                    return "Impossibile avviare il processo kubectl";
                }

                string output = await proc.StandardOutput.ReadToEndAsync();
                string error = await proc.StandardError.ReadToEndAsync();
                await proc.WaitForExitAsync();

                if (proc.ExitCode != 0)
                {
                    // Se l'errore è relativo all'autenticazione, prova a rifare il login
                    if (error.Contains("Unauthorized") || error.Contains("authentication") || error.Contains("token"))
                    {
                        _isLoggedIn = false;
                        var reloginResult = await LoginToAzureAsync();
                        if (reloginResult.Success)
                        {
                            // Riprova l'operazione dopo il nuovo login
                            return await FetchLatestLogsAsync(deployment, ns, tailLines, sinceSeconds);
                        }
                        else
                        {
                            return $"Errore di autenticazione kubectl: {error}";
                        }
                    }
                    return $"Errore kubectl: {error}";
                }

                // Salva su file
                Directory.CreateDirectory(LogDir);
                var file = Path.Combine(LogDir, $"k8s_logs_{deployment}_{DateTime.Now:yyyyMMdd_HHmmss}.log");
                await File.WriteAllTextAsync(file, output);

                return output;
            }
            catch (Exception ex)
            {
                return $"Errore esecuzione kubectl: {ex.Message}";
            }
        }

        /// <summary>
        /// Effettua il login ad Azure utilizzando le credenziali configurate
        /// </summary>
        /// <returns>Risultato dell'operazione di login</returns>
        public static async Task<(bool Success, string Message)> LoginToAzureAsync()
        {
            try
            {
                // Carica le credenziali da secrets.json o variabili d'ambiente
                var azureConfig = LoadAzureConfiguration();

                if (string.IsNullOrEmpty(azureConfig.TenantId))
                {
                    return (false, "Azure Tenant ID non configurato");
                }

                // Invece di usare az login direttamente, esegui un comando batch che lo avvia
                // Questo risolve i problemi di interazione con la finestra di login
                string batchCommand = "az login";

                // Aggiungi parametri se disponibili
                if (!string.IsNullOrEmpty(azureConfig.ClientId) && !string.IsNullOrEmpty(azureConfig.ClientSecret))
                {
                    // Login con Service Principal
                    batchCommand = $"az login --service-principal -t {azureConfig.TenantId} -u {azureConfig.ClientId} -p {azureConfig.ClientSecret}";
                }
                else if (!string.IsNullOrEmpty(azureConfig.Username) && !string.IsNullOrEmpty(azureConfig.Password))
                {
                    // Login con username e password
                    batchCommand = $"az login -t {azureConfig.TenantId} -u {azureConfig.Username} -p {azureConfig.Password}";
                }

                // Crea un file batch temporaneo
                string tempBatchFile = Path.Combine(Path.GetTempPath(), $"az_login_{Guid.NewGuid()}.bat");
                File.WriteAllText(tempBatchFile, batchCommand);

                // Esegui il file batch
                var psi = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c \"{tempBatchFile}\"",
                    UseShellExecute = true,
                    CreateNoWindow = false
                };

                try
                {
                    using var proc = Process.Start(psi);
                    if (proc == null)
                    {
                        return (false, "Impossibile avviare il processo az login");
                    }

                    // Attendiamo che il processo termini
                    proc.WaitForExit(60000); // Attendi fino a 60 secondi

                    // Elimina il file batch temporaneo
                    try { File.Delete(tempBatchFile); } catch { /* Ignora errori */ }

                    // Verifica se il login è riuscito eseguendo un comando di verifica
                    var verifyResult = await VerifyAzureLoginAsync();
                    if (verifyResult.Success)
                    {
                        // Ottieni le credenziali Kubernetes dopo il login ad Azure
                        return await GetKubernetesCredentialsAsync(azureConfig.ClusterName, azureConfig.ResourceGroup);
                    }
                    return verifyResult;
                }
                catch (Exception ex)
                {
                    // Elimina il file batch temporaneo in caso di errore
                    try { File.Delete(tempBatchFile); } catch { /* Ignora errori */ }

                    return (false, $"Errore durante l'esecuzione di az login: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Errore durante il login ad Azure: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifica se il login ad Azure è riuscito
        /// </summary>
        /// <returns>Risultato dell'operazione</returns>
        private static async Task<(bool Success, string Message)> VerifyAzureLoginAsync()
        {
            try
            {
                var psi = new ProcessStartInfo
                {
                    FileName = "az",
                    Arguments = "account show",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var proc = Process.Start(psi);
                if (proc == null)
                {
                    return (false, "Impossibile verificare lo stato del login ad Azure");
                }

                string output = await proc.StandardOutput.ReadToEndAsync();
                string error = await proc.StandardError.ReadToEndAsync();
                await proc.WaitForExitAsync();

                if (proc.ExitCode != 0)
                {
                    return (false, $"Verifica del login ad Azure fallita: {error}");
                }

                // Verifica se l'output contiene informazioni sull'account
                if (output.Contains("\"name\":") || output.Contains("\"id\":"))
                {
                    return (true, "Login ad Azure verificato con successo");
                }
                else
                {
                    return (false, "Login ad Azure non riuscito: nessun account attivo");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Errore durante la verifica del login ad Azure: {ex.Message}");
            }
        }

        /// <summary>
        /// Ottiene le credenziali Kubernetes dal cluster AKS
        /// </summary>
        /// <param name="clusterName">Nome del cluster AKS</param>
        /// <param name="resourceGroup">Gruppo di risorse</param>
        /// <returns>Risultato dell'operazione</returns>
        private static async Task<(bool Success, string Message)> GetKubernetesCredentialsAsync(string clusterName, string resourceGroup)
        {
            if (string.IsNullOrEmpty(clusterName) || string.IsNullOrEmpty(resourceGroup))
            {
                return (true, "Cluster AKS o Resource Group non specificati, si assume che kubectl sia già configurato");
            }

            try
            {
                var psi = new ProcessStartInfo
                {
                    FileName = "az",
                    Arguments = $"aks get-credentials --resource-group {resourceGroup} --name {clusterName} --overwrite-existing",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var proc = Process.Start(psi);
                if (proc == null)
                {
                    return (false, "Impossibile avviare il processo az aks get-credentials");
                }

                string output = await proc.StandardOutput.ReadToEndAsync();
                string error = await proc.StandardError.ReadToEndAsync();
                await proc.WaitForExitAsync();

                if (proc.ExitCode != 0)
                {
                    return (false, $"Errore durante il recupero delle credenziali Kubernetes: {error}");
                }

                return (true, "Credenziali Kubernetes ottenute con successo");
            }
            catch (Exception ex)
            {
                return (false, $"Errore durante il recupero delle credenziali Kubernetes: {ex.Message}");
            }
        }

        /// <summary>
        /// Carica la configurazione Azure da secrets.json o variabili d'ambiente
        /// </summary>
        /// <returns>Configurazione Azure</returns>
        private static AzureConfiguration LoadAzureConfiguration()
        {
            // Prova a caricare da Program.Configuration se disponibile
            var config = Program.Configuration;

            // Crea un oggetto di configurazione
            var azureConfig = new AzureConfiguration
            {
                TenantId = GetConfigValue(config, "AZURE_TENANT_ID"),
                ClientId = GetConfigValue(config, "AZURE_CLIENT_ID"),
                ClientSecret = GetConfigValue(config, "AZURE_CLIENT_SECRET"),
                Username = GetConfigValue(config, "AZURE_USERNAME"),
                Password = GetConfigValue(config, "AZURE_PASSWORD"),
                ClusterName = GetConfigValue(config, "AZURE_AKS_CLUSTER_NAME"),
                ResourceGroup = GetConfigValue(config, "AZURE_RESOURCE_GROUP")
            };

            return azureConfig;
        }

        /// <summary>
        /// Ottiene un valore di configurazione da IConfiguration o variabili d'ambiente
        /// </summary>
        private static string GetConfigValue(IConfiguration config, string key)
        {
            // Prova prima da IConfiguration
            if (config != null)
            {
                var value = config[key];
                if (!string.IsNullOrEmpty(value))
                {
                    return value;
                }
            }

            // Fallback su variabili d'ambiente
            return Environment.GetEnvironmentVariable(key);
        }

        /// <summary>
        /// Classe per la configurazione Azure
        /// </summary>
        private class AzureConfiguration
        {
            public string TenantId { get; set; }
            public string ClientId { get; set; }
            public string ClientSecret { get; set; }
            public string Username { get; set; }
            public string Password { get; set; }
            public string ClusterName { get; set; }
            public string ResourceGroup { get; set; }
        }
    }
}
