using System;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace TestAutomations.Utils
{
    public class Logger
    {
        private static Logger _instance;
        private static readonly object _lock = new object();
        
        private readonly string _logFilePath;
        private readonly bool _consoleOutput;
        private readonly StringBuilder _logBuffer = new StringBuilder();
        private readonly Timer _flushTimer;
        
        // Colori per la console
        private const ConsoleColor INFO_COLOR = ConsoleColor.White;
        private const ConsoleColor WARNING_COLOR = ConsoleColor.Yellow;
        private const ConsoleColor ERROR_COLOR = ConsoleColor.Red;
        private const ConsoleColor SUCCESS_COLOR = ConsoleColor.Green;
        
        private Logger(string logFilePath = "logs/test_automation.log", bool consoleOutput = true)
        {
            _logFilePath = logFilePath;
            _consoleOutput = consoleOutput;
            
            // Crea la directory dei log se non esiste
            string logDir = Path.GetDirectoryName(_logFilePath);
            if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }
            
            // Inizializza il timer per il flush automatico del buffer ogni 5 secondi
            _flushTimer = new Timer(FlushBuffer, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
            
            // Scrivi intestazione nel file di log
            AppendToLog($"=== INIZIO SESSIONE DI TEST {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
        }
        
        public static Logger GetInstance(string logFilePath = "logs/test_automation.log", bool consoleOutput = true)
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new Logger(logFilePath, consoleOutput);
                    }
                }
            }
            return _instance;
        }
        
        public void Info(string message)
        {
            LogMessage("INFO", message, INFO_COLOR);
        }
        
        public void Warning(string message)
        {
            LogMessage("WARN", message, WARNING_COLOR);
        }
        
        public void Error(string message)
        {
            LogMessage("ERROR", message, ERROR_COLOR);
        }
        
        public void Success(string message)
        {
            LogMessage("SUCCESS", message, SUCCESS_COLOR);
        }
        
        private void LogMessage(string level, string message, ConsoleColor color)
        {
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            string formattedMessage = $"[{timestamp}] [{level}] {message}";
            
            // Output su console se abilitato
            if (_consoleOutput)
            {
                lock (_lock)
                {
                    ConsoleColor originalColor = Console.ForegroundColor;
                    Console.ForegroundColor = color;
                    Console.WriteLine(formattedMessage);
                    Console.ForegroundColor = originalColor;
                }
            }
            
            // Aggiungi al buffer per il file di log
            AppendToLog(formattedMessage);
        }
        
        private void AppendToLog(string message)
        {
            lock (_logBuffer)
            {
                _logBuffer.AppendLine(message);
                
                // Flush immediato se il buffer supera una certa dimensione
                if (_logBuffer.Length > 4096)
                {
                    FlushBuffer();
                }
            }
        }
        
        private void FlushBuffer(object state = null)
        {
            string textToWrite;
            
            lock (_logBuffer)
            {
                if (_logBuffer.Length == 0)
                    return;
                
                textToWrite = _logBuffer.ToString();
                _logBuffer.Clear();
            }
            
            try
            {
                File.AppendAllText(_logFilePath, textToWrite);
            }
            catch (Exception ex)
            {
                // Se non possiamo scrivere sul file, almeno mostriamo l'errore in console
                if (_consoleOutput)
                {
                    ConsoleColor originalColor = Console.ForegroundColor;
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"[ERROR] Impossibile scrivere sul file di log: {ex.Message}");
                    Console.ForegroundColor = originalColor;
                }
            }
        }
        
        public void Dispose()
        {
            // Assicurati che tutti i log siano scritti prima di chiudere
            FlushBuffer();
            _flushTimer?.Dispose();
            
            // Scrivi footer nel file di log
            string footer = $"=== FINE SESSIONE DI TEST {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===";
            File.AppendAllText(_logFilePath, footer + Environment.NewLine);
        }
    }
} 
