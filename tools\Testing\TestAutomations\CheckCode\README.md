# Analisi Statica e Report Commit - TestAutomations

Questa cartella contiene strumenti per l'analisi statica del codice, la generazione di report sui commit Git e la valutazione dell'impatto delle modifiche sulle API documentate.

## Strumenti principali

### 1. Report Generazione Commit Git (`getcommits.bat`)

Script batch per Windows che genera un report dettagliato dei commit Git effettuati negli ultimi N giorni. Utile per analisi di impatto, revisioni e test pre-rilascio.

- Esecuzione: `TestAutomations/CheckCode/getcommits.bat <numero_giorni> [nome_file_report]`
- Output: file `.txt` con dettagli commit, autore, data, messaggio e statistiche file modificati

### 2. Analisi Impatto Commit con LLM (`analyze_impact.py`)

Script Python che analizza i dettagli dei commit Git e valuta il potenziale impatto sulle API documentate in file Markdown, utilizzando modelli OpenAI o Google Gemini.

- Esecuzione: `python TestAutomations/CheckCode/analyze_impact.py <report_commit> <directory_api_docs> [opzioni]`
- Opzioni: output file, modello LLM, ecc.
- Output: report di analisi impatto, con dettagli su possibili rotture di compatibilità o conflitti

### 3. Altri script e report

- `impact_report.md`: Esempio di report generato
- `structure.txt`: Struttura del codice analizzata
- Script PowerShell e batch per estrazione commit

## Prerequisiti

- Git installato e configurato nel PATH
- Python 3.7+ con librerie: `openai`, `python-dotenv` (per analisi LLM)
- API Key OpenAI o Gemini nel file `.env` (root progetto)

## Utilizzo tipico

1. Genera il report dei commit:
   ```
   TestAutomations/CheckCode/getcommits.bat 7
   ```
2. Analizza l'impatto dei commit sulle API:
   ```
   python TestAutomations/CheckCode/analyze_impact.py report_commit_ultimi_7_giorni.txt TestAutomations/API/ -o impact_report.md --model gpt-4o
   ```

## Note

- Gli script sono pensati per essere eseguiti dalla root del repository
- I report generati sono utili per QA, revisione codice e analisi pre-rilascio
- Per dettagli su parametri e output, consulta i commenti nei singoli script

# Report Generazione Commit Git (`getcommits.bat`)

Questo script batch per Windows (`getcommits.bat`) è progettato per generare un report dettagliato dei commit Git effettuati negli ultimi N giorni specificati dall'utente. Il report include informazioni cruciali per ogni commit, come l'hash, l'autore, la data, il messaggio completo del commit e le statistiche dei file modificati.

Lo scopo principale è fornire una panoramica chiara delle modifiche recenti al codice, utile per analisi di impatto, revisioni e test pre-rilascio.

## Prerequisiti

1.  **Git installato**: Git deve essere installato sul sistema e la sua directory eseguibile deve essere inclusa nella variabile d'ambiente PATH.
2.  **Esecuzione dalla Root del Repository**: Lo script deve essere eseguito dalla directory principale (root) di un repository Git valido.

## Utilizzo

Aprire un terminale (Prompt dei comandi, PowerShell, Git Bash, ecc.) e navigare alla directory root del proprio repository Git.

Il comando per eseguire lo script è:

```batch
TestAutomations\CheckCode\getcommits.bat <numero_giorni> [nome_file_report]
```

**Parametri:**

*   `<numero_giorni>` (obbligatorio): Specifica il numero di giorni nel passato per cui recuperare i commit. Ad esempio, `7` per recuperare i commit dell'ultima settimana.
*   `[nome_file_report]` (opzionale): Specifica il nome del file in cui salvare il report. Se omesso, il report verrà salvato in un file chiamato `report_commit_ultimi_N_giorni.txt` (dove `N` è il numero di giorni specificato) nella directory corrente (la root del repository).

### Esempi

1.  **Generare un report degli ultimi 7 giorni con nome file di default:**

    ```batch
    TestAutomations\CheckCode\getcommits.bat 7
    ```

    Questo creerà un file chiamato `report_commit_ultimi_7_giorni.txt` nella directory corrente.

2.  **Generare un report degli ultimi 30 giorni specificando un nome file:**

    ```batch
    TestAutomations\CheckCode\getcommits.bat 30 report_commit_mensile.txt
    ```

    Questo creerà un file chiamato `report_commit_mensile.txt` nella directory corrente.

## Output del Report

Lo script genera un file di testo (`.txt`) che contiene:

*   Un'intestazione con la data e l'ora di generazione del report e il percorso del repository.
*   Per ogni commit trovato nel periodo specificato:
    *   **Commit**: Hash completo del commit.
    *   **Autore**: Nome e email dell'autore del commit.
    *   **Data**: Data e ora del commit (formato ISO).
    *   **Messaggio**: Il messaggio completo del commit (soggetto e corpo).
    *   **Modifiche**: Statistiche dei file modificati in quel commit (file aggiunti, eliminati, modificati, con il numero di righe cambiate per ogni file), come prodotto da `git log --stat`.

Questo report può essere utilizzato per:
*   Analizzare rapidamente le modifiche recenti.
*   Identificare i commit che potrebbero avere un impatto significativo.
*   Supportare le fasi di testing e quality assurance.
*   Fornire input per script di analisi più avanzati (come quello che si prevede di creare in Python con LLM). 

---

# Analisi Impatto Commit con LLM (`analyze_impact.py`)

Questo script Python (`analyze_impact.py`) è progettato per analizzare i dettagli dei commit Git (generati da `getcommits.bat`) e valutarne il potenziale impatto sulle API documentate in file Markdown, utilizzando un Large Language Model (LLM) tramite l'API di OpenAI.

Lo scopo è automatizzare parte del processo di revisione, identificando potenziali conflitti, rotture di compatibilità o effetti collaterali imprevisti che le modifiche al codice potrebbero avere sulle API esistenti, prima delle fasi di test pre-rilascio.

## Prerequisiti

1.  **Python**: Python 3.7 o superiore installato.
2.  **Librerie Python**: È necessario installare le seguenti librerie:
    ```bash
    pip install openai python-dotenv
    ```
3.  **API Key OpenAI**: Un file chiamato `.env` deve essere presente nella directory principale del progetto (es. `/c:/development/OT/.env`). Questo file deve contenere la tua API key di OpenAI:
    ```
    OPENAI_API_KEY="LA_TUA_CHIAVE_OPENAI_QUI"
    ```
4.  **Report dei Commit**: Un file di testo generato dallo script `getcommits.bat`, contenente i dettagli dei commit da analizzare.
5.  **Documentazione API**: Una directory contenente i file di documentazione delle API in formato Markdown (`.md`). Lo script leggerà tutti i file `.md` presenti in questa directory.

## Utilizzo

Aprire un terminale e navigare alla directory root del proprio repository Git (la stessa da cui si esegue `getcommits.bat`).

Il comando per eseguire lo script è:

```bash
python TestAutomations/CheckCode/analyze_impact.py <percorso_report_commit> <percorso_directory_api_docs> [opzioni]
```

**Argomenti Posizionali:**

*   `<percorso_report_commit>` (obbligatorio): Percorso del file di report dei commit (es. `report_commit_ultimi_7_giorni.txt`).
*   `<percorso_directory_api_docs>` (obbligatorio): Percorso della directory contenente i file `.md` della documentazione API (es. `TestAutomations/API/`).

**Opzioni:**

*   `-o <file_output>, --output_file <file_output>` (opzionale):
    Percorso del file in cui salvare il report di analisi generato dall'LLM. Se omesso, il report completo verrà stampato sulla console.
    Esempio: `-o analisi_impatto_report.txt`

*   `--model <nome_modello>` (opzionale):
    Specifica il modello OpenAI da utilizzare per l'analisi (es. `gpt-3.5-turbo`, `gpt-4`, `gpt-4o`).
    Il default è `gpt-3.5-turbo`.
    Lo script contiene una mappa interna (`MODEL_CONTEXT_LIMITS`) con i limiti di contesto approssimativi per alcuni modelli noti. Se si specifica un modello non presente nella mappa, verrà utilizzato un limite di contesto di fallback (16384 token) e verrà mostrato un avviso.
    Esempio: `--model gpt-4o`

### Esempio Completo

```bash
# Assumendo che sei nella root del progetto /c:/development/OT/
# e che commit_report.txt e la cartella TestAutomations/API/ esistano

python TestAutomations/CheckCode/analyze_impact.py commit_report.txt TestAutomations/API/ -o report_analisi_impatto.txt --model gpt-4o
```

## Funzionamento (In Breve)

1.  **Caricamento Dati**: 
    *   Legge tutti i file `.md` dalla directory `api_docs_dir` fornita, memorizzando il contenuto di ciascun file.
    *   Analizza il file di report dei commit per estrarre i dettagli di ciascun commit (hash, autore, data, messaggio, modifiche ai file).
2.  **Analisi con LLM (per ogni commit e per ogni documento API)**:
    *   Per ogni commit estratto dal report:
        *   Per ogni documento API (`.md`) caricato:
            *   **Stima dei Token**: Calcola una stima dei token necessari per inviare il contenuto del documento API corrente più i dettagli del commit al modello LLM selezionato. Questa stima include un buffer per il prompt stesso e la risposta attesa.
            *   **Controllo Limite Contesto**: Se i token stimati superano il limite di contesto del modello LLM (meno il buffer), l'analisi per quella specifica combinazione commit-documento API viene **saltata** per prevenire errori e costi eccessivi. Un messaggio di "SKIPPED" con i dettagli dei token verrà incluso nel report finale.
            *   **Chiamata LLM**: Se la stima dei token è entro i limiti, invia i dettagli del commit e il contenuto del documento API corrente all'LLM.
            *   Il prompt chiede all'LLM di identificare potenziali impatti negativi (rotture di compatibilità, conflitti, ecc.) che il commit potrebbe avere sulle funzionalità descritte *in quello specifico documento API*.
3.  **Generazione Report**: 
    *   Assembla tutte le risposte dell'LLM (o i messaggi di skip) in un unico report testuale.
    *   Il report include statistiche sull'esecuzione (quante analisi tentate, saltate, eseguite).

## Output del Report di Analisi

Lo script genera un file di testo (o stampa a console) che contiene:

*   Un'intestazione con la data/ora di generazione, i percorsi dei file di input, il modello LLM utilizzato e il limite di token stimato per il payload (documento + commit).
*   **Statistiche dell'Analisi**: Numero di analisi totali tentate, saltate per limiti di token, ed effettivamente inviate all'LLM.
*   Per ogni **commit** analizzato:
    *   Dettagli del commit: Hash, Autore, Data, Messaggio completo, Statistiche delle modifiche ai file.
    *   Una riga di separazione (`---`).
    *   Per ogni **documento API** rispetto al quale il commit è stato analizzato:
        *   Il nome del documento API.
        *   L'analisi testuale fornita dall'LLM riguardante l'impatto del commit su quel documento specifico.
        *   Oppure, un messaggio **"SKIPPED"** se l'analisi per quella combinazione è stata saltata a causa del superamento dei limiti di token, con dettagli sulla stima dei token e il limite del modello.
        *   Una riga di separazione (`---`) dopo l'analisi di ogni documento per quel commit.
    *   Una riga di separazione (`==================================================`) tra un commit e l'altro.

## Considerazioni Importanti

*   **Costi API OpenAI**: L'utilizzo di questo script comporta chiamate all'API di OpenAI, che possono avere costi associati in base al modello utilizzato e al numero di token processati. Il controllo dei token integrato aiuta a mitigare costi imprevisti dovuti a input troppo grandi.
*   **Qualità dell'Analisi**: L'accuratezza e l'utilità dell'analisi dipendono fortemente dalla qualità e chiarezza della documentazione API fornita, dalla specificità dei messaggi di commit, dalla complessità delle modifiche e dalle capacità del modello LLM scelto. I risultati dovrebbero essere considerati come un supporto alla revisione manuale, non un sostituto completo.
*   **Limiti di Contesto**: Anche se lo script tenta di gestire i limiti di token, documenti API estremamente lunghi potrebbero comunque essere saltati. In tali casi, potrebbe essere necessario suddividere i documenti API più grandi in file `.md` più piccoli e specifici.
*   **File Processati**: Lo script processa solo file con estensione `.md` nella directory specificata per la documentazione API. Altri formati (come `.json`, `.yaml`) non vengono letti a meno che lo script non venga modificato.

# Analisi impatto commit multi-repo con LLM

## Modelli supportati

Puoi usare sia modelli OpenAI (gpt-3.5-turbo, gpt-4o, gpt-4, ecc.) sia modelli Gemini di Google:

- gemini-2.0-pro
- gemini-2.0-flash
- gemini-2.0-flash-spark
- gemini-2.0-flash-001
- gemini-2.5-pro-preview-05-06
- gemini-2.5-flash-preview-04-17
- gemini-2.5-pro
- gemini-2.5-flash

Per usare Gemini, basta specificare il modello con `--model` e avere nel `.env`:

```
GEMINI_API_KEY=la_tua_chiave_gemini
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/
```

Esempio:

```
python analyze_impact.py ... --model gemini-2.5-pro-preview-05-06
```

Lo script seleziona automaticamente l'API corretta (OpenAI o Gemini) in base al nome del modello.

## Limiti di contesto

- GPT-4o: 128.000 token
- Gemini 2.x: fino a 1M token (1048576)

**Attenzione:** il costo in token può essere elevato se la documentazione API è lunga o ci sono molti commit. Lo script ora accorpa i commit per servizio e invia solo le parti rilevanti del `.md` per ottimizzare i costi.

## Variabili d'ambiente richieste

- Per OpenAI: `OPENAI_API_KEY`
- Per Gemini: `GEMINI_API_KEY` e (opzionale) `GEMINI_BASE_URL`

## Esempio di uso

```
python analyze_impact.py reports/getcommits_report.txt API/ -o impact_report.md --model gemini-2.0-flash 