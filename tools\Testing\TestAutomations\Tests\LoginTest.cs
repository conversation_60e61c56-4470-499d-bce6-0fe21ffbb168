using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class LoginTest : TestBase
    {
        private readonly string _username;
        private readonly string _password;
        private OTTradingClient _client;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        public LoginTest(string name, bool isDummy = false) : base(name, isDummy)
        {
            // O<PERSON>eni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                Logger.Info($"Avvio test login - Modalità: {(IsDummy ? "Dummy" : "Normale")}");

                // Esegui il login in base alla modalità
                bool loginSuccess;
                if (IsDummy)
                {
                    loginSuccess = await ExecuteDummyLoginAsync();
                }
                else
                {
                    loginSuccess = await ExecuteRealLoginAsync();
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("Login", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("Login", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("Login", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("Login", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> ExecuteDummyLoginAsync()
        {
            try
            {
                Logger.Info($"Esecuzione dummy login come {_username}...");

                // Utilizziamo direttamente il metodo LoginAsync del client con il parametro isDummy impostato a true
                bool success = await _client.LoginAsync(_username, _password, isDummy: true);

                if (!success)
                {
                    Logger.Error("Dummy login fallito");
                    return false;
                }

                Logger.Info("Dummy login completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante il dummy login: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante il dummy login: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> ExecuteRealLoginAsync()
        {
            try
            {
                Logger.Info($"Esecuzione login normale come {_username}...");

                // Utilizziamo il metodo LoginAsync del client con il parametro isDummy impostato a false
                bool success = await _client.LoginAsync(_username, _password, isDummy: false);

                if (!success)
                {
                    Logger.Error("Login normale fallito");
                    return false;
                }

                Logger.Info("Login normale completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante il login normale: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante il login normale: {ex.Message}");
                return false;
            }
        }
        // Metodo pubblico per accedere al client autenticato
        // Questo è fondamentale per l'uso come prerequisito
        public OTTradingClient GetAuthenticatedClient()
        {
            return _client;
        }

        // Restituisce i risultati dei test step
        public List<TestStepResult> GetTestSteps()
        {
            return _testSteps;
        }
    }
}