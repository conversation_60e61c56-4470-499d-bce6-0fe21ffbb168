{"openapi": "3.0.4", "info": {"title": "OTWebCore API", "description": "OT", "version": "V1"}, "paths": {"/api/Account/LoginApi": {"post": {"tags": ["Account"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResult"}}}}}}}, "/api/Account/DummyLoginApi": {"post": {"tags": ["Account"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiLoginModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiLoginResultApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiLoginResultApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiLoginResultApiResult"}}}}}}}, "/api/Account/RegisterApi": {"post": {"tags": ["Account"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiRegisterModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiRegisterModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApiRegisterModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiRegisterResultApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiRegisterResultApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiRegisterResultApiResult"}}}}}}}, "/api/Account/LogoutApi": {"get": {"tags": ["Account"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResult"}}}}}}}, "/api/Account/LoggedBrokers": {"get": {"tags": ["Account"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoggedBrokersResultApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoggedBrokersResultApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoggedBrokersResultApiResult"}}}}}}}, "/api/Account/BrokerProperties": {"get": {"tags": ["Account"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BrokerPropertiesResultApiResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BrokerPropertiesResultApiResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrokerPropertiesResultApiResult"}}}}}}}, "/api/Advice/GetAdvice": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdviceModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdviceModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AdviceModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetAdviceFull": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdviceFullModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdviceFullModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AdviceFullModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetMessages": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MessageModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MessageModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetAlerts": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllertModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AllertModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AllertModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetShortLevaInfo": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShortLevaInfoModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShortLevaInfoModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShortLevaInfoModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/MessageReadReceipts": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagesModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MessagesModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MessagesModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetPriipsKidInfoes": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriipsKidInfoModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PriipsKidInfoModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PriipsKidInfoModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/CheckPriipsCash": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckPriipsCashModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckPriipsCashModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckPriipsCashModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/CheckPriipsDer": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckPriipsDerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckPriipsDerModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckPriipsDerModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/ChangeLplActivation": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeLplActivationModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeLplActivationModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeLplActivationModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/ChangeLplClosePriority": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeLplClosePriorityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeLplClosePriorityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeLplClosePriorityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetDerIpoInfoes": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DerIpoInfoesModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DerIpoInfoesModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DerIpoInfoesModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/ChangeIpoEnabling": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpoEnablingModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IpoEnablingModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IpoEnablingModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetMarketAbusePdf": {"get": {"tags": ["Advice"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/Advice/GetNotices": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoticesRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NoticesRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NoticesRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetLeverageEnabledTitles": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnabledTitlesRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnabledTitlesRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EnabledTitlesRequestModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetDerivativesExchangeAndFactors": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExchangeRateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExchangeRateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExchangeRateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetIpoEnabledInstruments": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IpoEnabledInstrumentsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IpoEnabledInstrumentsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IpoEnabledInstrumentsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetBailInInstruments": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BailInRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BailInRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BailInRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/EmirData": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmirDataModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmirDataModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmirDataModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetLendingFees": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LendingFeesModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LendingFeesModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LendingFeesModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/SearchProduct": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchProductModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchProductModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchProductModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/LendingSearch": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LendingSearchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LendingSearchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LendingSearchModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Advice/GetCapitalGainPortalLink": {"get": {"tags": ["Advice"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Advice/TobinTax": {"post": {"tags": ["Advice"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TobinTaxModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TobinTaxModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TobinTaxModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/GetStockAlarms": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/GetStockAlarmsAsFlowData": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/SendPushMessage": {"post": {"tags": ["Alarm"], "parameters": [{"name": "customerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sentDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "notificationText", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockKey"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockKey"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockKey"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/GetNewsPushNotificationsConfiguration": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/SetNewsPushNotifications": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/AddDevice": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/AddHUAWEIDevice": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/RemoveDevice": {"delete": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/RemoveHUAWEIDevice": {"delete": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/CreateThreshold": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/UpdateThreshold": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/DeleteThreshold": {"delete": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Alarm/GetStrategyAlarms": {"post": {"tags": ["Alarm"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyAlarmModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StrategyAlarmModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StrategyAlarmModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/SetLiquidity": {"post": {"tags": ["Balance"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetLiquidityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SetLiquidityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SetLiquidityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/GetBalance": {"post": {"tags": ["Balance"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/GetBalanceDetail": {"post": {"tags": ["Balance"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceDetailModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BalanceDetailModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BalanceDetailModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/RecalculateBalance": {"post": {"tags": ["Balance"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BalanceModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/AccountLiquidityDetail": {"get": {"tags": ["Balance"], "parameters": [{"name": "paramAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "accountType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "v<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Balance/TitoliDestinati": {"get": {"tags": ["Balance"], "parameters": [{"name": "bondAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "cashAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "bankId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Balance/MarginiShortMultiday": {"post": {"tags": ["Balance"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/XTMarginiShortMultidayModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/XTMarginiShortMultidayModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/XTMarginiShortMultidayModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Balance/ReservedAmount": {"get": {"tags": ["Balance"], "parameters": [{"name": "cashAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Book/GetBook": {"post": {"tags": ["Book"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BookModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BookModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetCandles": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetCandles2": {"post": {"tags": ["Chart"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "password", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChartModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/StartConsolidateAll": {"post": {"tags": ["Chart"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "password", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/StartConsolidateTicks": {"post": {"tags": ["Chart"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "password", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/StartConsolidateMinutes": {"post": {"tags": ["Chart"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "password", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/StartConsolidateDays": {"post": {"tags": ["Chart"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "password", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsolidateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetTickFile": {"get": {"tags": ["Chart"], "parameters": [{"name": "stockFileName", "in": "query", "schema": {"type": "string"}}, {"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Chart/AddTickFile": {"post": {"tags": ["Chart"], "parameters": [{"name": "stockFileName", "in": "query", "schema": {"type": "string"}}, {"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetTickFileList": {"get": {"tags": ["Chart"], "parameters": [{"name": "stockFolder", "in": "query", "schema": {"type": "string"}}, {"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}, {"name": "command", "in": "query", "schema": {"type": "string", "default": "select"}}, {"name": "lastCreationDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastModificationDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "regexPattern", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Chart/UploadTickFile": {"post": {"tags": ["Chart"], "parameters": [{"name": "stockPath", "in": "query", "schema": {"type": "string"}}, {"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/ImportModels": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetModels": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/SetDefaultModel": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/SaveModel": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/DuplicateModel": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/DeleteModel": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/ImportStudies": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetStudies": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/SaveStudy": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/DuplicateStudy": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/DeleteStudy": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GraphStudy"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetAllEODFidaCandles": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetAllEODFidaCandlesJSON": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetEODFidaCandles": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Chart/GetEODCandles": {"post": {"tags": ["Chart"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FidaModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Commissions/CommissionSummary": {"get": {"tags": ["Commissions"], "parameters": [{"name": "bondAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Commissions/CommissionSearch": {"post": {"tags": ["Commissions"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCommissionsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchCommissionsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchCommissionsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Configuration/EditTools": {"post": {"tags": ["Configuration"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DerivativesConfigurations"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DerivativesConfigurations"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DerivativesConfigurations"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Configuration/OpenToolDetail": {"get": {"tags": ["Configuration"], "parameters": [{"name": "toolId", "in": "query", "schema": {"type": "string"}}, {"name": "bondAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/GetDocument": {"post": {"tags": ["Document"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DocumentModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Document/GetDocument2": {"get": {"tags": ["Document"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "documentRequestType", "in": "query", "schema": {"$ref": "#/components/schemas/DocumentRequestType"}}, {"name": "documentId", "in": "query", "schema": {"type": "string"}}, {"name": "brokerDocumentType", "in": "query", "schema": {"$ref": "#/components/schemas/DocumentTypeSella"}}, {"name": "idSubDocument", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/GetSample": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/GetSamples": {"get": {"tags": ["Document"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/RemoveSamples": {"delete": {"tags": ["Document"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/GetInformativeNote": {"get": {"tags": ["Document"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "documentType", "in": "query", "schema": {"$ref": "#/components/schemas/DocumentTypeSella"}}, {"name": "documentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Document/SearchInformativeNote": {"post": {"tags": ["Document"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchInformativeNoteModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchInformativeNoteModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchInformativeNoteModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/Login": {"post": {"tags": ["ExternalCalls"], "parameters": [{"name": "alias", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/Login2": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginParams"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginParams"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginParams"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/PlatformLogout": {"post": {"tags": ["ExternalCalls"], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/BrokerLogin": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SellaTraderLogin": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/DummyLogin": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/BrokerLogout": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/IsMarketHoliday": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SearchStockByBasket": {"post": {"tags": ["ExternalCalls"], "parameters": [{"name": "basketCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SearchStocks": {"post": {"tags": ["ExternalCalls"], "parameters": [{"name": "stockCode", "in": "query", "schema": {"type": "string"}}, {"name": "marketsCodes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SearchStocks2": {"post": {"tags": ["ExternalCalls"], "parameters": [{"name": "stockCode", "in": "query", "schema": {"type": "string"}}, {"name": "marketsCodes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "marketsGroups", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SetCustomerProfile": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerInterfaceSettings"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerInterfaceSettings"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerInterfaceSettings"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/TradingViewFull": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/TradingViewFullTest": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TvMobileModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/saveJsonConfig": {"post": {"tags": ["ExternalCalls"], "parameters": [{"name": "json", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/ExternalCalls/SaveCustomerFeedback": {"post": {"tags": ["ExternalCalls"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerFeedbackModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerFeedbackModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerFeedbackModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/getJsonConfig": {"get": {"tags": ["ExternalCalls"], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/TradingView": {"get": {"tags": ["ExternalCalls"], "parameters": [{"name": "market", "in": "query", "schema": {"type": "string"}}, {"name": "stock", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "useNewVersion", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "savedchart", "in": "query", "schema": {"type": "string"}}, {"name": "resolution", "in": "query", "schema": {"type": "string"}}, {"name": "theme", "in": "query", "schema": {"type": "string"}}, {"name": "auth<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/SimpleWidget": {"get": {"tags": ["ExternalCalls"], "parameters": [{"name": "market", "in": "query", "schema": {"type": "string"}}, {"name": "stock", "in": "query", "schema": {"type": "string"}}, {"name": "auth<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/GetCustomerProfile": {"get": {"tags": ["ExternalCalls"], "parameters": [{"name": "customerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/GetBalanceAccount": {"get": {"tags": ["ExternalCalls"], "parameters": [{"name": "cashAccountId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalCalls/GetDistributionTable": {"get": {"tags": ["ExternalCalls"], "parameters": [{"name": "customerId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ExternalVirtualPortfolio/ExportFile": {"post": {"tags": ["ExternalVirtualPortfolio"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Filter/GetCurrencies": {"post": {"tags": ["Filter"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Filter/GetStockFilterParams": {"post": {"tags": ["Filter"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Filter/GetDropdowns": {"get": {"tags": ["Filter"], "parameters": [{"name": "language", "in": "query", "schema": {"$ref": "#/components/schemas/CustomerLanguage"}}, {"name": "section", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Filter/GetDropdownCombinations": {"get": {"tags": ["Filter"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Filter/GetDropdownCombinationsTree": {"get": {"tags": ["Filter"], "parameters": [{"name": "language", "in": "query", "schema": {"$ref": "#/components/schemas/CustomerLanguage"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Layout/SaveLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/DuplicateLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DuplicateLayoutModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DuplicateLayoutModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DuplicateLayoutModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetNewestLayoutByName": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetLayouts": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetDefaultLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/SetDefaultLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/DeleteLayout": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LayoutSelectionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetMenu": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/SetMenu": {"post": {"tags": ["Layout"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MenuModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Layout/GetDropdownSelection": {"get": {"tags": ["Layout"], "parameters": [{"name": "menuIds", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DropdownSelectionModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DropdownSelectionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DropdownSelectionModel"}}}}}}}, "/api/List/GetPersonalListNew": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/List/GetPersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["List"], "parameters": [{"name": "logos", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/List/AddToPersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/RemoveFromPersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/CreatePersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/SavePersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/DeletePersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/GetPersonalListBroker": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/MergeBrokerPersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MergePersonalListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MergePersonalListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MergePersonalListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/List/ImportBrokerPersonalList": {"post": {"tags": ["List"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalListBrokerModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Management/Command": {"post": {"tags": ["Management"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Management/ResetCustomer": {"post": {"tags": ["Management"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ManagementModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Management/MigrateCustomer": {"post": {"tags": ["Management"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerMigrationModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerMigrationModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerMigrationModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Management/ManagementLogin": {"post": {"tags": ["Management"], "parameters": [{"name": "username", "in": "header", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManagementLoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ManagementLoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ManagementLoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Metrics/counter": {"post": {"tags": ["Metrics"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Metrics/gauge": {"post": {"tags": ["Metrics"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MetricsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/News/TestHotNews": {"get": {"tags": ["News"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/News/GetNews": {"post": {"tags": ["News"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/News/GetPlainNews": {"post": {"tags": ["News"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewsDetailModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/News/GetNewsList": {"post": {"tags": ["News"], "parameters": [{"name": "convertHtml", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/News/GetNewsListPaged": {"post": {"tags": ["News"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "convertHtml", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewsListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/News/GetNewsAdvancedSearchValues": {"post": {"tags": ["News"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/News/MarkNewsAsRead": {"post": {"tags": ["News"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewsMarkAsReadModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NewsMarkAsReadModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NewsMarkAsReadModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/InsertOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InsertOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InsertOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/ConfirmInsertOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/UpdateOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/ConfirmUpdateOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/DeleteOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/ConfirmDeleteOrder": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetPortfolio2": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetPortfolio3": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetPortfolioAsPersonalList": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PortfolioModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PersonalList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PersonalList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalList"}}}}}}}, "/api/Order/GetTradingInfo": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradingInfoModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradingInfoModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradingInfoModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetOrderStatus": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetOrderStatus2": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderStatusModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetProfitLoss": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetProfitLoss2": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProfitLossModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/ProfitLossFilter": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetAccounts": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetOrderDetail": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDetailModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderDetailModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/DeleteStrategy": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteStrategyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteStrategyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteStrategyModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetStrategyStatus": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyStatusModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StrategyStatusModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StrategyStatusModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/GetShortLiquidityAmountRequired": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShortLiquidityAmountRequiredModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShortLiquidityAmountRequiredModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShortLiquidityAmountRequiredModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/NormalizePosition": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/TransformSiToSm": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NormalizePositionModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/OptionsCalculator": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OptionsCalculatorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OptionsCalculatorModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OptionsCalculatorModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/MarginSimulator": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarginSimulatorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MarginSimulatorModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MarginSimulatorModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Order/FormSellBuyFields": {"get": {"tags": ["Order"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Order/ElaboraConsiglio": {"post": {"tags": ["Order"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElaboraConsiglioWebModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ElaboraConsiglioWebModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ElaboraConsiglioWebModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetPersonalViewsNew": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/SavePersonalViewNew": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetPersonalViewNew": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetPersonalViews": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PViewsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetPersonalView": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PViewModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/SavePersonalView": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/DeletePersonalView": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PersonalViewModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetAllColumns": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "platformType", "in": "query", "schema": {"$ref": "#/components/schemas/PlatformType"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PersonalView/GetColumns": {"post": {"tags": ["Personal<PERSON>iew"], "parameters": [{"name": "infoSection", "in": "query", "schema": {"$ref": "#/components/schemas/InfoSection"}}, {"name": "platformType", "in": "query", "schema": {"$ref": "#/components/schemas/PlatformType"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Security/Login": {"post": {"tags": ["Security"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Security/Logout": {"post": {"tags": ["Security"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Security/GetAccessToken": {"get": {"tags": ["Security"], "parameters": [{"name": "brokerName", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Security/VersionCheck": {"post": {"tags": ["Security"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Security/PrestartChecks": {"post": {"tags": ["Security"], "parameters": [{"name": "version", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Security/UrlCheck": {"post": {"tags": ["Security"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UrlCheckRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UrlCheckRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UrlCheckRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Security/TokenCheck": {"post": {"tags": ["Security"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Services/Manuals": {"get": {"tags": ["Services"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Services/Webinars": {"get": {"tags": ["Services"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetDefaultQuantities": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetDefaultQuantity": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/EditDefaultQuantity": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/DeleteDefaultQuantity": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DefaultQuantityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetCustomerProperties": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetCustomerProperty": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/EditCustomerProperty": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/DeleteCustomerProperty": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerPropertyModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetAbilInfoesList": {"get": {"tags": ["Settings"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "subjectId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetAbilInfoesDetails": {"get": {"tags": ["Settings"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "serviceId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/DisableService": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisableServiceModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DisableServiceModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DisableServiceModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetCostiRischiStatus": {"get": {"tags": ["Settings"], "parameters": [{"name": "broker", "in": "query", "schema": {"$ref": "#/components/schemas/BrokerName"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SetManLevaPriips": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ManLevaPriipsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ManLevaPriipsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ManLevaPriipsModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetProfStatement": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetProfStatementModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetProfStatementModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetProfStatementModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SaveProfStatement": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveProfStatementModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveProfStatementModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveProfStatementModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetDestinationLiquidity": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SetDestinationLiquidity": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SetDestinationLiquidityModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetEseguitiContoTrader": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEseguitiContoTraderModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetEseguitiContoTraderModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetEseguitiContoTraderModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SetDefaultCashAcct": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DefaultCashAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DefaultCashAccountModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DefaultCashAccountModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SetBondAcctAlias": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BondAccountAliasModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BondAccountAliasModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BondAccountAliasModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/ApplicationConfiguration": {"get": {"tags": ["Settings"], "parameters": [{"name": "platform", "in": "query", "schema": {"$ref": "#/components/schemas/PlatformType"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Settings/SaveApplicationConfiguration": {"post": {"tags": ["Settings"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveApplicationConfigurationModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveApplicationConfigurationModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveApplicationConfigurationModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Settings/GetShortLeverageUserInfo": {"get": {"tags": ["Settings"], "parameters": [{"name": "bondAcctId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockList2": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockList3": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockListModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockListByFilter2": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockListFilterModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListFilterModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockListFilterModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetVolatilityChartData": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VolatilityChartDataModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VolatilityChartDataModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VolatilityChartDataModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStock": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStock2": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockUserInfo": {"get": {"tags": ["StockList"], "parameters": [{"name": "marketCode", "in": "query", "schema": {"type": "string"}}, {"name": "stockCode", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStocks": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockForDetail2": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StocksModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockRisk": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetStockForDetail": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetMarkets": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetBaskets": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetMarketInformation": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetMarketGroups": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetFastSearchInstruments": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetFastSearchInstrumentsGrid": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetFastSearchInstrumentsGridPaged": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetFastSearchPagedWeb": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockSearchModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetDerContextFilteredData": {"get": {"tags": ["StockList"], "parameters": [{"name": "market", "in": "query", "schema": {"type": "string"}}, {"name": "subType", "in": "query", "schema": {"type": "string"}}, {"name": "underlying", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetDerSellaTraderContextDataSeparator": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbSellaTraderContextData": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbSellaTraderContextDataSeparator": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCertSellaTraderContextDataSeparator": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetDerContextData": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbContextData": {"get": {"tags": ["StockList"], "parameters": [{"name": "grouped", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbContextFilteredData": {"get": {"tags": ["StockList"], "parameters": [{"name": "market", "in": "query", "schema": {"type": "string"}}, {"name": "stockTypeString", "in": "query", "schema": {"type": "string"}}, {"name": "currency", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCwContextData": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCwContextFilteredData": {"get": {"tags": ["StockList"], "parameters": [{"name": "issuer", "in": "query", "schema": {"type": "string"}}, {"name": "subType", "in": "query", "schema": {"type": "string"}}, {"name": "underlying", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCertContextDataTree": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCertContextDataTreeMobile": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetDerContextDataTree": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetDerContextDataTreeMobile": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbContextDataTree": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetObbContextDataTreeMobile": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCwContextDataTree": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetCwContextDataTreeMobile": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetBondIssuersDescription": {"get": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/StockList/IsTradable": {"post": {"tags": ["StockList"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/StockList/GetLogos": {"get": {"tags": ["StockList"], "parameters": [{"name": "stockKeys", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TimeAndSale/ExportTicksByBasket": {"post": {"tags": ["TimeAndSale"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDataByBasketModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportDataByBasketModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportDataByBasketModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TimeAndSale/GetTicks": {"post": {"tags": ["TimeAndSale"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeAndSaleModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TimeAndSaleModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TimeAndSaleModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TimeAndSale/GetTickRanges": {"get": {"tags": ["TimeAndSale"], "parameters": [{"name": "marketCode", "in": "query", "schema": {"type": "string"}}, {"name": "stockCode", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/config": {"get": {"tags": ["TradingView"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TvConfiguration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TvConfiguration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TvConfiguration"}}}}}}}, "/api/TradingView/symbols": {"get": {"tags": ["TradingView"], "parameters": [{"name": "symbol", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TvSymbolInfo"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TvSymbolInfo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TvSymbolInfo"}}}}}}}, "/api/TradingView/search": {"get": {"tags": ["TradingView"], "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "exchange", "in": "query", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TvSymbolSearch"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TvSymbolSearch"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TvSymbolSearch"}}}}}}}}, "/api/TradingView/marks": {"get": {"tags": ["TradingView"], "parameters": [{"name": "from", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "to", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "symbol", "in": "query", "schema": {"type": "string"}}, {"name": "resolution", "in": "query", "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/tvhistory": {"get": {"tags": ["TradingView"], "parameters": [{"name": "From", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "FromDT", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "To", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ToDT", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Symbol", "in": "query", "schema": {"type": "string"}}, {"name": "MarketCode", "in": "query", "schema": {"type": "string"}}, {"name": "StockCode", "in": "query", "schema": {"type": "string"}}, {"name": "TimeFrameType", "in": "query", "schema": {"$ref": "#/components/schemas/TimeFrameType"}}, {"name": "Resolution", "in": "query", "schema": {"type": "string"}}, {"name": "OTResolution", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OTResolutionNew", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "TimeFrameTypeNew", "in": "query", "schema": {"$ref": "#/components/schemas/TimeFrameTypeNEW"}}, {"name": "CountBack", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UseNewVersion", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/{charts_storage_api_version}/loadchartbysymbol": {"get": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "symbol", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/{charts_storage_api_version}/loadfirstuserchart": {"get": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/{charts_storage_api_version}/charts": {"delete": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "chart", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "chart", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/{charts_storage_api_version}/study_templates": {"get": {"tags": ["TradingView"], "parameters": [{"name": "client", "in": "query", "schema": {"type": "string"}}, {"name": "user", "in": "query", "schema": {"type": "string"}}, {"name": "charts_storage_api_version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/GetChartFromDb": {"get": {"tags": ["TradingView"], "parameters": [{"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/CustomerConfigurationType"}}, {"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradingView/SaveChartOnDb": {"post": {"tags": ["TradingView"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradingViewSavedObject"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradingViewSavedObject"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradingViewSavedObject"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/InternetBanking": {"get": {"tags": ["User"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WebHookHandler/WebhookReceiver": {"post": {"tags": ["WebHookHandler"], "parameters": [{"name": "X-api-version", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrafanaWebHookDataModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GrafanaWebHookDataModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GrafanaWebHookDataModel"}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AccountFilter": {"type": "object", "properties": {"_brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_bondAcctId": {"type": "integer", "format": "int32", "nullable": true}, "_dossierId": {"type": "integer", "format": "int32", "nullable": true}, "_cashAcctId": {"type": "integer", "format": "int32", "nullable": true}, "_customerCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AdvancedObbSearchObject": {"type": "object", "properties": {"_type": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_subtype": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_typeObb": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_subtypeObb": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_market": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_countryIssuer": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_dalPerCed": {"type": "number", "format": "double", "nullable": true}, "_toPerCed": {"type": "number", "format": "double", "nullable": true}, "_selCedFrom": {"type": "integer", "format": "int32", "nullable": true}, "_selCedTo": {"type": "integer", "format": "int32", "nullable": true}, "_cedPeriod": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_cedTipo": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_txtDalRel": {"type": "number", "format": "double", "nullable": true}, "_txtAlRel": {"type": "number", "format": "double", "nullable": true}, "_txtDalRen": {"type": "number", "format": "double", "nullable": true}, "_txtAlRen": {"type": "number", "format": "double", "nullable": true}, "_classeRischio": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_classeRischioCredito": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_currency": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_issuerDescription": {"type": "array", "items": {"type": "string"}, "nullable": true}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "AdviceFilter": {"type": "object", "properties": {"_newsMinutes": {"type": "integer", "format": "int32", "nullable": true}, "_orderLimit": {"type": "integer", "format": "int32", "nullable": true}, "_orderBy": {"$ref": "#/components/schemas/OrderBy"}, "_types": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true}, "_page": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "AdviceFullModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/AdviceFilter"}, "par": {"$ref": "#/components/schemas/AdviceParameters"}}, "additionalProperties": false}, "AdviceModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/AdviceFilter"}}, "additionalProperties": false}, "AdviceParameters": {"type": "object", "properties": {"_broker": {"$ref": "#/components/schemas/BrokerName"}, "_accessToken": {"type": "string", "nullable": true}, "_customerId": {"type": "integer", "format": "int32"}, "_clientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_accountFilter": {"$ref": "#/components/schemas/AccountFilter"}, "_stockFilter": {"$ref": "#/components/schemas/StockFilter"}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_messageType": {"type": "string", "nullable": true}, "_orderCode": {"type": "string", "nullable": true}, "_serviceId": {"type": "integer", "format": "int32", "nullable": true}, "_productId": {"type": "integer", "format": "int32", "nullable": true}, "_quickFilter": {"$ref": "#/components/schemas/AdviceFilter"}}, "additionalProperties": false}, "AlarmFilterModel": {"type": "object", "properties": {"stock_type": {"type": "string", "nullable": true}, "StockTypes": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItem"}, "nullable": true}, "stock_sub_type": {"type": "string", "nullable": true}, "StockSubTypes": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItem"}, "nullable": true}, "stock_type_detail": {"type": "string", "nullable": true}, "StockTypeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItem"}, "nullable": true}, "instrument_search_type": {"$ref": "#/components/schemas/IntrumentDescription"}, "InstrumentSearchTypes": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItem"}, "nullable": true}, "search_text": {"type": "string", "nullable": true}, "status_code": {"$ref": "#/components/schemas/AllarmStatus"}, "StatusCodes": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItem"}, "nullable": true}, "insert_start_date": {"type": "string", "format": "date-time", "nullable": true}, "insert_end_date": {"type": "string", "format": "date-time", "nullable": true}, "start_date": {"type": "string", "format": "date-time", "nullable": true}, "end_date": {"type": "string", "format": "date-time", "nullable": true}, "paging": {"$ref": "#/components/schemas/PagingSelector"}, "page_number": {"type": "integer", "format": "int32", "writeOnly": true}, "page_size": {"type": "integer", "format": "int32", "writeOnly": true}}, "additionalProperties": false}, "AlarmModel": {"type": "object", "properties": {"stockKey": {"$ref": "#/components/schemas/StockKey"}, "StockDescription": {"type": "string", "nullable": true}, "customerId": {"type": "integer", "format": "int32", "nullable": true}, "token": {"type": "string", "nullable": true}, "hotNewsEnabled": {"type": "boolean", "nullable": true}, "deviceId": {"type": "string", "nullable": true}, "rule": {"type": "integer", "format": "int32", "nullable": true}, "ruleId": {"type": "integer", "format": "int32", "nullable": true}, "field": {"type": "string", "nullable": true}, "thresholdValue": {"type": "number", "format": "double", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "sendDate": {"type": "string", "format": "date-time", "nullable": true}, "filter": {"$ref": "#/components/schemas/AlarmFilterModel"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AlertServiceID": {"enum": [10, 20, 40, 50, 60, 70, 80, 90, 100, 110, 120], "type": "integer", "format": "int32"}, "AllarmStatus": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "AllertModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "ServiceId": {"$ref": "#/components/schemas/AlertServiceID"}}, "additionalProperties": false}, "ApiLoginModel": {"type": "object", "properties": {"Username": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "Chars": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "Char1": {"type": "string"}, "Char2": {"type": "string"}, "Birthday": {"type": "string", "format": "date-time"}, "Token": {"type": "string", "nullable": true}, "Step": {"$ref": "#/components/schemas/ApiLoginStep"}, "AuthId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiLoginResult": {"type": "object", "properties": {"NextStep": {"$ref": "#/components/schemas/ApiLoginStep"}, "Chars": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "AuthId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiLoginResultApiResult": {"type": "object", "properties": {"Data": {"$ref": "#/components/schemas/ApiLoginResult"}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ApiLoginStep": {"enum": [0, 1, 2, 3, 4, 5, 90, 100], "type": "integer", "format": "int32"}, "ApiRegisterModel": {"required": ["<PERSON><PERSON>", "ConfirmPassword", "Email", "Password"], "type": "object", "properties": {"Email": {"minLength": 1, "type": "string"}, "Alias": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string"}, "ConfirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ApiRegisterResult": {"type": "object", "additionalProperties": false}, "ApiRegisterResultApiResult": {"type": "object", "properties": {"Data": {"$ref": "#/components/schemas/ApiRegisterResult"}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "AuthParams": {"type": "object", "properties": {"_accessToken": {"type": "string", "nullable": true}, "_user": {"type": "string", "nullable": true}, "_password": {"type": "string", "nullable": true}, "_birthDay": {"type": "string", "format": "date-time", "nullable": true}, "BirthDayTicks": {"type": "integer", "format": "int64", "nullable": true}, "_token": {"type": "string", "nullable": true}, "_otp": {"type": "string", "nullable": true}, "_chars": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_cnctr_access_token": {"type": "string", "nullable": true}, "_next_step": {"$ref": "#/components/schemas/NextStep"}, "_clientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_crossAuthToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BailInRequest": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "BalanceDetailModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "par": {"$ref": "#/components/schemas/BalanceDetailParameters"}}, "additionalProperties": false}, "BalanceDetailParameters": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "AccessToken": {"type": "string", "nullable": true}, "CashAccountId": {"type": "integer", "format": "int32"}, "CustomerId": {"type": "integer", "format": "int32"}, "GamingId": {"type": "integer", "format": "int32", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "Currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BalanceModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "par": {"$ref": "#/components/schemas/BalanceParam"}}, "additionalProperties": false}, "BalanceParam": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "AccessToken": {"type": "string", "nullable": true}, "CashAccountId": {"type": "integer", "format": "int32"}, "CustomerId": {"type": "integer", "format": "int32"}, "GamingId": {"type": "integer", "format": "int32", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "BatchTypes": {"enum": [0, 1], "type": "integer", "format": "int32"}, "BondAccountAliasModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32"}, "Alias": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BookModel": {"type": "object", "properties": {"stock": {"$ref": "#/components/schemas/StockKey"}}, "additionalProperties": false}, "BooleanApiResult": {"type": "object", "properties": {"Data": {"type": "boolean", "readOnly": true}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "BrokerAccountProperties": {"type": "object", "properties": {"BrokerCustomer": {"type": "string", "nullable": true}, "BondAcctId": {"type": "integer", "format": "int32"}, "CashAcctId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32", "nullable": true}, "CustomerCode": {"type": "string", "nullable": true}, "BankId": {"type": "integer", "format": "int32", "nullable": true}, "Key": {"type": "string", "nullable": true, "readOnly": true}, "KeyDispo": {"type": "string", "nullable": true, "readOnly": true}, "Tag": {"nullable": true, "readOnly": true}, "BondAccountCode": {"type": "string", "nullable": true}, "CashAccountCode": {"type": "string", "nullable": true}, "AliasDossier": {"type": "string", "nullable": true}, "AccountHolder": {"type": "string", "nullable": true}, "CanTrade": {"type": "boolean"}, "CashAccountCurrency": {"type": "string", "nullable": true}, "IsDefaultDossier": {"type": "boolean"}, "LeverageIntradayEnabled": {"type": "boolean"}, "LeverageMultidayEnabled": {"type": "boolean"}, "LeveragePlusEnabled": {"type": "boolean"}, "LeveragePlusActive": {"type": "integer", "format": "int32"}, "MarginAccountId": {"type": "integer", "format": "int64"}, "MarginAccountShorts": {"type": "string", "nullable": true}, "MarginAccountShortPluss": {"type": "string", "nullable": true}, "MarginAccountShortMinuss": {"type": "string", "nullable": true}, "MarginAccountShortTranss": {"type": "string", "nullable": true}, "MarginIPOenabled": {"type": "boolean"}, "MarginIPOAdjustment": {"type": "number", "format": "double"}, "MarginIPOactive": {"type": "boolean"}, "DerivativesEnabled": {"type": "integer", "format": "int32"}, "ShortMultidayEnabled": {"type": "boolean"}, "EmirLEIActive": {"type": "boolean"}, "IsDefaultBondAccount": {"type": "boolean"}, "OperativeService": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BrokerBaseProperties": {"type": "object", "properties": {"BrokerCustomer": {"type": "string", "nullable": true}, "BondAcctId": {"type": "integer", "format": "int32"}, "CashAcctId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32", "nullable": true}, "CustomerCode": {"type": "string", "nullable": true}, "BankId": {"type": "integer", "format": "int32", "nullable": true}, "Key": {"type": "string", "nullable": true, "readOnly": true}, "KeyDispo": {"type": "string", "nullable": true, "readOnly": true}, "Tag": {"nullable": true, "readOnly": true}}, "additionalProperties": false}, "BrokerName": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "BrokerPropertiesResult": {"type": "object", "properties": {"BrokerAccountProperties": {"$ref": "#/components/schemas/SpecificBrokerProperties"}}, "additionalProperties": false}, "BrokerPropertiesResultApiResult": {"type": "object", "properties": {"Data": {"$ref": "#/components/schemas/BrokerPropertiesResult"}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CertXSearchObject": {"type": "object", "properties": {"IssuerDescription": {"type": "string", "nullable": true}, "OfficialSegment": {"type": "string", "nullable": true}, "UnderlyingType": {"type": "string", "nullable": true}, "MicroCategory": {"type": "string", "nullable": true}, "UnderlyingISINCode": {"type": "string", "nullable": true}, "ExpirationYear": {"type": "string", "nullable": true}, "CurrencySign": {"type": "string", "nullable": true}, "BullBear": {"type": "string", "nullable": true}, "UnderlyingTypeDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangeLplActivationModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "NewValue": {"$ref": "#/components/schemas/LplValues"}, "Dossiers": {"type": "array", "items": {"$ref": "#/components/schemas/LplDossier"}, "nullable": true}}, "additionalProperties": false}, "ChangeLplClosePriorityModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "NewValue": {"$ref": "#/components/schemas/LplValues"}}, "additionalProperties": false}, "ChannelType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "type": "integer", "format": "int32"}, "ChartModel": {"type": "object", "properties": {"stock": {"$ref": "#/components/schemas/StockKey"}, "timeFrame": {"$ref": "#/components/schemas/TimeFrame"}, "maxCandles": {"type": "integer", "format": "int32", "nullable": true}, "cacheId": {"type": "string", "nullable": true}, "firstID": {"type": "string", "format": "date-time", "nullable": true}, "PriceInfo": {"type": "boolean"}}, "additionalProperties": false}, "CheckPriipsCashModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "MarketCode": {"type": "string", "nullable": true}, "PriipsKid": {"type": "array", "items": {"$ref": "#/components/schemas/PriipsKidCash"}, "nullable": true}}, "additionalProperties": false}, "CheckPriipsDerModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "MarketCode": {"type": "string", "nullable": true}, "PriipsKid": {"type": "array", "items": {"$ref": "#/components/schemas/PriipsKidDer"}, "nullable": true}}, "additionalProperties": false}, "ClientInfo": {"type": "object", "properties": {"ChannelType": {"$ref": "#/components/schemas/ChannelType"}, "DeviceType": {"$ref": "#/components/schemas/DeviceType"}, "BankId": {"type": "integer", "format": "int32", "nullable": true}, "IbCode": {"type": "string", "nullable": true}, "IpAddress": {"type": "string", "nullable": true}, "Version": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Col": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Caption": {"type": "string", "nullable": true}, "CaptionWeb": {"type": "string", "nullable": true}, "Precision": {"type": "integer", "format": "int32", "nullable": true}, "PositiveNegative": {"type": "boolean"}, "Blink": {"type": "boolean"}, "Type": {"$ref": "#/components/schemas/ColType"}, "Alignment": {"$ref": "#/components/schemas/ColAlignment"}, "Origin": {"$ref": "#/components/schemas/ColOrigin"}, "Section": {"$ref": "#/components/schemas/InfoSection"}, "PlatformType": {"$ref": "#/components/schemas/PlatformType"}, "_setterType": {"type": "string", "nullable": true, "writeOnly": true}, "_setterAlignment": {"type": "string", "nullable": true, "writeOnly": true}, "_setterOrigin": {"type": "string", "nullable": true, "writeOnly": true}, "_setterStringPlatformType": {"type": "string", "nullable": true, "writeOnly": true}, "_setterIntPlatformType": {"type": "integer", "format": "int32", "writeOnly": true}}, "additionalProperties": false}, "ColAlignment": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "ColOrigin": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": "integer", "format": "int32"}, "ColType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 70, 80, 90, 91], "type": "integer", "format": "int32"}, "ConfirmOrderModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/ConfirmOrderParameters"}}, "additionalProperties": false}, "ConfirmOrderParameters": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "CustomerID": {"type": "integer", "format": "int32"}, "OrderID": {"type": "string", "nullable": true}, "AccessToken": {"type": "string", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "ConsolidateRequest": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "TrimEnabled": {"type": "boolean"}}, "additionalProperties": false}, "Criteria": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "CustomerConfigurationType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "CustomerFeedbackModel": {"type": "object", "properties": {"body": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CustomerInformationType": {"enum": [0], "type": "integer", "format": "int32"}, "CustomerInterfaceSettings": {"type": "object", "properties": {"customerId": {"type": "integer", "format": "int32"}, "nickname": {"type": "string", "nullable": true}, "theme": {"$ref": "#/components/schemas/InterfaceTheme"}, "tradingType": {"$ref": "#/components/schemas/TradingType"}, "blink": {"type": "boolean"}, "notifications": {"$ref": "#/components/schemas/Notifications"}, "operationalSettings": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/OperationalSettings"}, "nullable": true}, "report": {"$ref": "#/components/schemas/Report"}, "hasMultisession": {"type": "boolean"}, "otDefaultDossierKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CustomerLanguage": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "CustomerMigrationModel": {"type": "object", "properties": {"IgnoreErrors": {"type": "boolean"}, "Customers": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CustomerProperty": {"type": "object", "properties": {"_customerID": {"type": "integer", "format": "int32"}, "_property": {"type": "string", "nullable": true}, "_stringValue": {"type": "string", "nullable": true}, "_intValue": {"type": "integer", "format": "int32", "nullable": true}, "_dateTimeValue": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CustomerPropertyModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/CustomerProperty"}}, "additionalProperties": false}, "CwSearchObject": {"type": "object", "properties": {"_issuerCode": {"type": "string", "nullable": true}, "_underlyingDescription": {"type": "string", "nullable": true}, "_stockSubType": {"type": "string", "nullable": true}, "_strikeFrom": {"type": "number", "format": "double", "nullable": true}, "_strikeTo": {"type": "number", "format": "double", "nullable": true}, "_expiryMonth": {"type": "integer", "format": "int32", "nullable": true}, "_expiryYear": {"type": "integer", "format": "int32", "nullable": true}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "DefaultCashAccountModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "CashAccountId": {"type": "integer", "format": "int32"}, "IsOperativeCash": {"type": "boolean"}, "Currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DefaultQuantity": {"type": "object", "properties": {"_customerID": {"type": "integer", "format": "int32"}, "_marketCode": {"type": "string", "nullable": true}, "_stockCode": {"type": "string", "nullable": true}, "_description": {"type": "string", "nullable": true}, "_defaultQty": {"type": "number", "format": "double"}}, "additionalProperties": false}, "DefaultQuantityModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/DefaultQuantity"}}, "additionalProperties": false}, "DeleteOrderModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/DeleteOrderParameters"}}, "additionalProperties": false}, "DeleteOrderParameters": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "OrderID": {"type": "string", "nullable": true}, "CustomerID": {"type": "integer", "format": "int32"}, "AccessToken": {"type": "string", "nullable": true}, "OrderBatchType": {"$ref": "#/components/schemas/BatchTypes"}, "OrderTypology": {"$ref": "#/components/schemas/OrderTypology"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "DeleteStrategyModel": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "StrategyId": {"type": "string", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "DerIpoInfoesModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "CashAccountId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DerivateSearchObject": {"type": "object", "properties": {"_marketCode": {"type": "string", "nullable": true}, "_stockType": {"type": "string", "nullable": true}, "_underlyingDescription": {"type": "string", "nullable": true}, "_strikeFrom": {"type": "number", "format": "double", "nullable": true}, "_strikeTo": {"type": "number", "format": "double", "nullable": true}, "_optionType": {"type": "string", "nullable": true}, "_contractSize": {"type": "number", "format": "double", "nullable": true}, "_expiryDay": {"type": "integer", "format": "int32", "nullable": true}, "_expiryMonth": {"type": "integer", "format": "int32", "nullable": true}, "_expiryYear": {"type": "integer", "format": "int32", "nullable": true}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "DerivativesConfigurations": {"type": "object", "properties": {"BondAccountId": {"type": "integer", "format": "int32"}, "CashAccountId": {"type": "integer", "format": "int32"}, "MarginAccountId": {"type": "integer", "format": "int64"}, "CashAccountCode": {"type": "string", "nullable": true}, "CashAccountCurrency": {"type": "string", "nullable": true}, "CashAccountCodeUSD": {"type": "string", "nullable": true}, "BondAccountCode": {"type": "string", "nullable": true}, "BankId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32", "nullable": true}, "NamedInstrumentsDossier": {"type": "string", "nullable": true}, "BearerInstrumentsDossier": {"type": "string", "nullable": true}, "DerivativesOperationsStatus": {"type": "integer", "format": "int32"}, "DerivativesCode": {"type": "string", "nullable": true}, "IncreaseWarrantyMargin": {"type": "number", "format": "double", "nullable": true}, "DerivativesIntradayIPOMargination": {"type": "boolean"}, "DerivativesIntradayIPOMarginationEnabled": {"type": "boolean"}, "PercentageMaximum": {"type": "string", "nullable": true}, "DeliberateMaximum": {"type": "string", "nullable": true}, "OperationalCeiling": {"type": "string", "nullable": true}, "EnabledTools": {"type": "array", "items": {"$ref": "#/components/schemas/EnabledTool"}, "nullable": true}}, "additionalProperties": false}, "DeviceType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "type": "integer", "format": "int32"}, "Direction": {"enum": [0, 1], "type": "integer", "format": "int32"}, "DisableServiceModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "AbilitationId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DocumentModel": {"type": "object", "properties": {"_documentType": {"$ref": "#/components/schemas/DocumentRequestType"}, "_idDocument": {"type": "string", "nullable": true}, "_idSubDocument": {"type": "string", "nullable": true}, "_brokerDocumentType": {"$ref": "#/components/schemas/DocumentTypeSella"}, "_directUrl": {"type": "string", "nullable": true}, "_brokerName": {"$ref": "#/components/schemas/BrokerName"}}, "additionalProperties": false}, "DocumentRequestType": {"enum": [0, 1, 20, 99], "type": "integer", "format": "int32"}, "DocumentTypeSella": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21], "type": "integer", "format": "int32"}, "DropdownItemModel": {"type": "object", "properties": {"Description": {"type": "string", "nullable": true}, "Value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DropdownSelectionModel": {"type": "object", "properties": {"Items": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/DropdownItemModel"}, "nullable": true}, "nullable": true}}, "additionalProperties": false}, "DuplicateLayoutModel": {"type": "object", "properties": {"newLayout": {"$ref": "#/components/schemas/LayoutFull"}, "originalLayoutId": {"type": "integer", "format": "int32"}, "originalPlatformType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EffectiveTradingReport": {"type": "object", "properties": {"informationNote": {"$ref": "#/components/schemas/InformationNote"}, "customerInformationType": {"$ref": "#/components/schemas/CustomerInformationType"}, "insertEditDelete": {"type": "boolean"}, "reservedOrdersActivation": {"type": "boolean"}, "marketOrdersExecutionRefusalCancelation": {"type": "boolean"}}, "additionalProperties": false}, "ElaboraConsiglioWebModel": {"type": "object", "properties": {"broker": {"$ref": "#/components/schemas/BrokerName"}, "idConsiglio": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EmirDataModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "OrderId": {"type": "string", "nullable": true}, "BondAccountId": {"type": "integer", "format": "int32"}, "CustomerCode": {"type": "string", "nullable": true}, "StartDate": {"type": "string", "format": "date-time", "nullable": true}, "EndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "EnabledTitlesRequestModel": {"type": "object", "properties": {"ServiceId": {"type": "integer", "format": "int32"}, "Market": {"type": "string", "nullable": true}, "SubjectId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EnabledTool": {"type": "object", "properties": {"Id": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "IsActive": {"type": "boolean"}, "Disable": {"type": "boolean"}, "Details": {"$ref": "#/components/schemas/ToolDetails"}}, "additionalProperties": false}, "EndOfDayTimeFrameType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "EvaluationMode": {"enum": [0, 1], "type": "integer", "format": "int32"}, "ExchangeRateRequest": {"type": "object", "properties": {"Date": {"type": "string", "format": "date-time"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "ExportDataByBasketModel": {"type": "object", "properties": {"brokerName": {"$ref": "#/components/schemas/BrokerName"}, "filetype": {"$ref": "#/components/schemas/ExportFileType"}, "basketcode": {"type": "string", "nullable": true}, "dataType": {"$ref": "#/components/schemas/ExportDataType"}, "date": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ExportDataType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "ExportFileType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "FidaModel": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time", "nullable": true}, "to": {"type": "string", "format": "date-time", "nullable": true}, "marketCode": {"type": "string", "nullable": true}, "stockCode": {"type": "string", "nullable": true}, "fidaCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GamingReport": {"type": "object", "properties": {"insertEditDelete": {"type": "boolean"}, "reservedOrdersActivation": {"type": "boolean"}, "marketOrdersExecutionRefusalCancelation": {"type": "boolean"}}, "additionalProperties": false}, "GetEseguitiContoTraderModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "CashAccountId": {"type": "integer", "format": "int32"}, "Trimester": {"type": "integer", "format": "int32"}, "Year": {"type": "integer", "format": "int32"}, "Bank": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetProfStatementModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "Service": {"$ref": "#/components/schemas/ProfStatementType"}}, "additionalProperties": false}, "GlobalSearchObject": {"type": "object", "properties": {"_description": {"type": "string", "nullable": true}, "_isin": {"type": "string", "nullable": true}, "_marketCode": {"type": "string", "nullable": true}, "_stockCode": {"type": "string", "nullable": true}, "_criteria": {"$ref": "#/components/schemas/Criteria"}, "_afterhour": {"type": "string", "nullable": true}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "GrafanaAlertModel": {"type": "object", "properties": {"Annotations": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}, "Labels": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}, "DashboardURL": {"type": "string", "nullable": true}, "EndsAt": {"type": "string", "format": "date-time", "nullable": true}, "Fingerprint": {"type": "string", "nullable": true}, "GeneratorURL": {"type": "string", "nullable": true}, "PanelURL": {"type": "string", "nullable": true}, "SilenceURL": {"type": "string", "nullable": true}, "StartsAt": {"type": "string", "format": "date-time", "nullable": true}, "Status": {"type": "string", "nullable": true}, "ValueString": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GrafanaWebHookDataModel": {"type": "object", "properties": {"Alerts": {"type": "array", "items": {"$ref": "#/components/schemas/GrafanaAlertModel"}, "nullable": true}, "CommonAnnotations": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}, "CommonLabels": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}, "ExternalURL": {"type": "string", "nullable": true}, "GroupKey": {"type": "string", "nullable": true}, "GroupLabels": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "OrgId": {"type": "integer", "format": "int32"}, "Receiver": {"type": "string", "nullable": true}, "State": {"type": "string", "nullable": true}, "Status": {"type": "string", "nullable": true}, "Title": {"type": "string", "nullable": true}, "TruncatedAlerts": {"type": "integer", "format": "int32"}, "Version": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GraphModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "idToDuplicate": {"type": "integer", "format": "int32", "nullable": true}, "subjectID": {"type": "integer", "format": "int32"}, "layoutType": {"type": "string", "nullable": true}, "layoutID": {"type": "integer", "format": "int32"}, "layoutDescription": {"type": "string", "nullable": true}, "layoutDefinition": {"type": "string", "nullable": true}, "isDefault": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GraphStudy": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "marketCode": {"type": "string", "nullable": true}, "stockCode": {"type": "string", "nullable": true}, "idToDuplicate": {"type": "integer", "format": "int32", "nullable": true}, "subjectID": {"type": "integer", "format": "int32"}, "layoutType": {"type": "string", "nullable": true}, "workspaceType": {"type": "string", "nullable": true}, "layoutID": {"type": "integer", "format": "int32"}, "layoutDescription": {"type": "string", "nullable": true}, "layoutDefinition": {"type": "string", "nullable": true}, "isDefault": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "IPOMargination": {"type": "object", "properties": {"isActive": {"type": "boolean"}, "ipoMarginationCombo": {"$ref": "#/components/schemas/LeverageActivationCombo"}}, "additionalProperties": false}, "InfoAbil": {"enum": [0, 1, 2, 4, 8, 16], "type": "integer", "format": "int32"}, "InfoPermission": {"type": "object", "properties": {"IsDefault": {"type": "boolean"}, "PlatformList": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "InfoGroups": {"type": "array", "items": {"type": "string"}, "nullable": true}, "InfoAbil": {"$ref": "#/components/schemas/InfoAbil"}, "MaxBookLevel": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "InfoSection": {"enum": [1, 2, 4, 8, 16, -1], "type": "integer", "format": "int32"}, "InformationNote": {"enum": [0], "type": "integer", "format": "int32"}, "InsertOrderModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/InsertOrderParameters"}, "OrderSource": {"$ref": "#/components/schemas/OrderSource"}, "PositionId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "InsertOrderParameters": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "CustomerID": {"type": "integer", "format": "int32"}, "OrderID": {"type": "string", "nullable": true}, "MarketCode": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "OrderType": {"$ref": "#/components/schemas/OrderType"}, "Price": {"type": "number", "format": "double", "nullable": true}, "Quantity": {"type": "number", "format": "double"}, "ValidityDate": {"type": "string", "format": "date-time", "nullable": true}, "ValidityDateTicks": {"type": "integer", "format": "int64", "nullable": true}, "EvaluationMode": {"$ref": "#/components/schemas/EvaluationMode"}, "OrderFast": {"type": "boolean"}, "AccessToken": {"type": "string", "nullable": true}, "BrokerProperties": {"$ref": "#/components/schemas/BrokerBaseProperties"}, "Phase": {"$ref": "#/components/schemas/OrderPhase"}, "ManualOrder": {"type": "boolean"}, "BestExecution": {"type": "boolean"}, "BatchOrder": {"type": "boolean"}, "ParkingOrder": {"type": "boolean"}, "LeverageOrder": {"type": "boolean"}, "StopPrice": {"type": "number", "format": "double", "nullable": true}, "IcebergOrderQty": {"type": "integer", "format": "int32", "nullable": true}, "OrderParameter": {"$ref": "#/components/schemas/OrderParameter"}, "GamingId": {"type": "integer", "format": "int32", "nullable": true}, "PriceType": {"$ref": "#/components/schemas/PriceTypes"}, "StrategyEvaluationMode": {"$ref": "#/components/schemas/StrategyEvaluationModes"}, "StrategyConditionType": {"$ref": "#/components/schemas/StrategyConditionTypes"}, "TakeOrderLimitPrice": {"type": "number", "format": "double", "nullable": true}, "TakeOrderStopPrice": {"type": "number", "format": "double", "nullable": true}, "StopOrderLimitPrice": {"type": "number", "format": "double", "nullable": true}, "StopOrderStopPrice": {"type": "number", "format": "double", "nullable": true}, "TakeStopOrderActive": {"type": "boolean"}, "MainOrderTick": {"type": "number", "format": "double", "nullable": true}, "TakeOrderTick": {"type": "number", "format": "double", "nullable": true}, "StopOrderTick": {"type": "number", "format": "double", "nullable": true}, "IsMargin": {"type": "boolean", "nullable": true}, "StockType": {"type": "string", "nullable": true}, "OperationPurpose": {"type": "integer", "format": "int32", "nullable": true}, "OrderPhase": {"type": "integer", "format": "int32", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "InstrumentType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "InterfaceTheme": {"enum": [0, 1], "type": "integer", "format": "int32"}, "IntraDayLeverage": {"type": "object", "properties": {"isActive": {"type": "boolean"}, "intradayLeverageCombo": {"$ref": "#/components/schemas/LeverageCombo"}}, "additionalProperties": false}, "IntrumentDescription": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "IpoEnabledInstrumentsRequest": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "IpoEnablingModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountId": {"type": "integer", "format": "int32"}, "CashAccountId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32"}, "MarginAcctId": {"type": "integer", "format": "int32"}, "CustomerCode": {"type": "string", "nullable": true}, "NewValue": {"$ref": "#/components/schemas/IpoValues"}}, "additionalProperties": false}, "IpoValues": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "ItemParameters": {"type": "object", "properties": {"_action": {"type": "string", "nullable": true}, "_code": {"type": "string", "nullable": true}, "_viewName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ItemType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "LayoutFull": {"type": "object", "properties": {"_layoutID": {"type": "integer", "format": "int32"}, "_customerID": {"type": "integer", "format": "int32"}, "_platformType": {"type": "integer", "format": "int32"}, "_name": {"type": "string", "nullable": true}, "_description": {"type": "string", "nullable": true}, "_isDefault": {"type": "boolean"}, "_creationDate": {"type": "string", "format": "date-time"}, "_lastUpdateDate": {"type": "string", "format": "date-time"}, "_lastUseDate": {"type": "string", "format": "date-time"}, "_json": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LayoutModel": {"type": "object", "properties": {"layout": {"$ref": "#/components/schemas/LayoutFull"}, "layoutID": {"type": "integer", "format": "int32"}, "platformType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LayoutSelectionModel": {"type": "object", "properties": {"layoutID": {"type": "integer", "format": "int32"}, "platformType": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LendingFeesModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAcctId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32"}, "InstrumentType": {"type": "string", "nullable": true}, "InstrumentText": {"type": "string", "nullable": true}, "LendingOpenDateFrom": {"type": "string", "format": "date-time", "nullable": true}, "LendingOpenDateTo": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LendingSearchModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAcctId": {"type": "integer", "format": "int32"}, "BondAcctCode": {"type": "string", "nullable": true}, "CashAcctId": {"type": "string", "nullable": true}, "CashAcctCode": {"type": "string", "nullable": true}, "DossierId": {"type": "string", "nullable": true}, "InstrumentType": {"type": "string", "nullable": true}, "InstrumentText": {"type": "string", "nullable": true}, "LendingOpenDateFrom": {"type": "string", "format": "date-time", "nullable": true}, "LendingOpenDateTo": {"type": "string", "format": "date-time", "nullable": true}, "LendingCloseDateFrom": {"type": "string", "format": "date-time", "nullable": true}, "LendingCloseDateTo": {"type": "string", "format": "date-time", "nullable": true}, "LendingNumber": {"type": "string", "nullable": true}, "BranchCode": {"type": "string", "nullable": true}, "MarketCode": {"type": "string", "nullable": true}, "ProductParam": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "ServiceType": {"type": "string", "nullable": true}, "ActiveLenders": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LeverageActivationCombo": {"enum": [0, 1], "type": "integer", "format": "int32"}, "LeverageCombo": {"enum": [0, 1], "type": "integer", "format": "int32"}, "LeveragePlus": {"type": "object", "properties": {"isActive": {"type": "boolean"}, "activationCombo": {"$ref": "#/components/schemas/LeverageActivationCombo"}, "officeClosureCombo": {"$ref": "#/components/schemas/LeverageCombo"}}, "additionalProperties": false}, "LiquidityParam": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "AccessToken": {"type": "string", "nullable": true}, "CashAccountId": {"type": "integer", "format": "int32"}, "CustomerId": {"type": "integer", "format": "int32"}, "GamingId": {"type": "integer", "format": "int32", "nullable": true}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "LiquidityValue": {"type": "number", "format": "double", "nullable": true}, "AddLiquidityValue": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "LiquidityWays": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "LoggedBrokersResult": {"type": "object", "properties": {"Brokers": {"type": "array", "items": {"$ref": "#/components/schemas/BrokerName"}, "nullable": true}, "DefaultBroker": {"$ref": "#/components/schemas/BrokerName"}}, "additionalProperties": false}, "LoggedBrokersResultApiResult": {"type": "object", "properties": {"Data": {"$ref": "#/components/schemas/LoggedBrokersResult"}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "LoginModel": {"type": "object", "properties": {"brokerName": {"$ref": "#/components/schemas/BrokerName"}, "authParams": {"$ref": "#/components/schemas/AuthParams"}}, "additionalProperties": false}, "LoginParams": {"type": "object", "properties": {"_alias": {"type": "string", "nullable": true}, "_password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LplDossier": {"type": "object", "properties": {"DossierId": {"type": "integer", "format": "int32"}, "CashAccountIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "LplValues": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "ManLevaPriipsModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "SubjectId": {"type": "integer", "format": "int32"}, "InformativaCostiSkipValue": {"type": "boolean"}}, "additionalProperties": false}, "ManagementCommand": {"type": "object", "properties": {"Parameters": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}}, "additionalProperties": false}, "ManagementLoginModel": {"type": "object", "properties": {"Code": {"type": "string", "nullable": true}, "Brokers": {"type": "array", "items": {"$ref": "#/components/schemas/BrokerName"}, "nullable": true}}, "additionalProperties": false}, "ManagementModel": {"type": "object", "properties": {"Type": {"type": "integer", "format": "int32"}, "Command": {"$ref": "#/components/schemas/ManagementCommand"}}, "additionalProperties": false}, "MarginSimulatorModel": {"type": "object", "properties": {"_broker": {"$ref": "#/components/schemas/BrokerName"}, "_macroChannelGroup": {"type": "integer", "format": "int32"}, "_cashLiquidity": {"type": "number", "format": "double"}, "_bondLiquidity": {"type": "number", "format": "double"}, "_customerCode": {"type": "string", "nullable": true}, "_isIpo": {"type": "boolean"}, "_derMarginIPOAdjustment": {"type": "number", "format": "double"}, "_selectedProducts": {"type": "array", "items": {"$ref": "#/components/schemas/MarginSimulatorProduct"}, "nullable": true}}, "additionalProperties": false}, "MarginSimulatorProduct": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "Quantity": {"type": "number", "format": "double"}, "Price": {"type": "number", "format": "double"}, "PositionType": {"type": "string", "nullable": true}, "Leva": {"type": "number", "format": "double"}, "InsBuy": {"type": "number", "format": "double"}, "InsSell": {"type": "number", "format": "double"}, "MedianPriceInsBuy": {"type": "number", "format": "double"}, "MedianPriceInsSell": {"type": "number", "format": "double"}, "CoveredQty": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Menu": {"type": "object", "properties": {"_menuName": {"type": "string", "nullable": true}, "_items": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}, "nullable": true}}, "additionalProperties": false}, "MenuItem": {"type": "object", "properties": {"_name": {"type": "string", "nullable": true}, "_items": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}, "nullable": true}, "_parameters": {"$ref": "#/components/schemas/ItemParameters"}, "_type": {"$ref": "#/components/schemas/ItemType"}}, "additionalProperties": false}, "MenuModel": {"type": "object", "properties": {"menu": {"$ref": "#/components/schemas/Menu"}, "menuName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MergePersonalListModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "Channel": {"type": "integer", "format": "int32"}, "PersonalListId": {"type": "string", "nullable": true}, "BrokerListIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "MessageModel": {"type": "object", "properties": {"broker": {"$ref": "#/components/schemas/BrokerName"}}, "additionalProperties": false}, "MessagesModel": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "MetricsModel": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Value": {"type": "number", "format": "double"}, "Timestamp": {"type": "string", "format": "date-time"}, "Labels": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "NewsDetailModel": {"type": "object", "properties": {"newsId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "NewsListFilter": {"type": "object", "properties": {"FromDateTicks": {"type": "integer", "format": "int64", "nullable": true}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "ToDateTicks": {"type": "integer", "format": "int64", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_category": {"type": "integer", "format": "int32", "nullable": true}, "_keyword": {"type": "array", "items": {"type": "string"}, "nullable": true}, "_elementCount": {"type": "integer", "format": "int32", "nullable": true}, "_subject": {"type": "string", "nullable": true}, "_market": {"type": "string", "nullable": true}, "_geograficRegion": {"type": "string", "nullable": true}, "_industrialSector": {"type": "string", "nullable": true}, "_governmentPolitic": {"type": "string", "nullable": true}, "_startTime": {"$ref": "#/components/schemas/NewsTimeSpanDTO"}, "StartTime": {"type": "string", "format": "date-span"}, "_endTime": {"$ref": "#/components/schemas/NewsTimeSpanDTO"}, "EndTime": {"type": "string", "format": "date-span"}, "_stockKey": {"$ref": "#/components/schemas/StockKey"}, "_paging": {"$ref": "#/components/schemas/PagingSelector"}, "_pageNumber": {"type": "integer", "format": "int32", "writeOnly": true}, "_pageSize": {"type": "integer", "format": "int32", "writeOnly": true}}, "additionalProperties": false}, "NewsListModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/NewsListFilter"}}, "additionalProperties": false}, "NewsMarkAsReadModel": {"type": "object", "properties": {"NewsId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "NewsTimeSpanDTO": {"type": "object", "properties": {"_days": {"type": "integer", "format": "int32"}, "_hours": {"type": "integer", "format": "int32"}, "_minutes": {"type": "integer", "format": "int32"}, "_seconds": {"type": "integer", "format": "int32"}, "_milliseconds": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "NextStep": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 99], "type": "integer", "format": "int32"}, "NormalizationType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "NormalizePositionModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "PortfolioId": {"type": "integer", "format": "int32"}, "QtyToNormalize": {"type": "integer", "format": "int32"}, "NormalizationType": {"$ref": "#/components/schemas/NormalizationType"}}, "additionalProperties": false}, "NoticesRequest": {"type": "object", "properties": {"ServiceId": {"type": "integer", "format": "int32"}, "SubjectId": {"type": "integer", "format": "int32"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "Notifications": {"type": "object", "properties": {"effectiveTrading": {"type": "boolean"}, "virtualTrading": {"type": "boolean"}, "gaming": {"type": "boolean"}}, "additionalProperties": false}, "ObbSearchObject": {"type": "object", "properties": {"_marketCode": {"type": "string", "nullable": true}, "_stockType": {"type": "string", "nullable": true}, "_currency": {"type": "string", "nullable": true}, "_dateFrom": {"type": "string", "nullable": true}, "_dateTo": {"type": "string", "nullable": true}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ObjectApiResult": {"type": "object", "properties": {"Data": {"nullable": true, "readOnly": true}, "Error": {"type": "string", "nullable": true, "readOnly": true}, "IsSuccess": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "OperationalSettings": {"type": "object", "properties": {"intradayLeverage": {"$ref": "#/components/schemas/IntraDayLeverage"}, "leveragePlus": {"$ref": "#/components/schemas/LeveragePlus"}, "ipoMargination": {"$ref": "#/components/schemas/IPOMargination"}}, "additionalProperties": false}, "OptionsCalculatorModel": {"type": "object", "properties": {"_broker": {"$ref": "#/components/schemas/BrokerName"}, "_marketCode": {"type": "string", "nullable": true}, "_stockCode": {"type": "string", "nullable": true}, "_customerCode": {"type": "string", "nullable": true}, "_priceLimit": {"type": "number", "format": "double"}, "_accountCurrencyCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderBy": {"enum": [0, 1], "type": "integer", "format": "int32"}, "OrderDetailModel": {"type": "object", "properties": {"orderCode": {"type": "string", "nullable": true}, "brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_clientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "OrderParameter": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "OrderPhase": {"enum": [0, 10, 20, 30, 40, 70, 80, 134, 999], "type": "integer", "format": "int32"}, "OrderShowType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "OrderSource": {"enum": [0, 1, 2, 3, 99], "type": "integer", "format": "int32"}, "OrderStatus": {"enum": [0, 1, 2, 4, 8, 16, 32, 64, 128, 512, 1024, 2048], "type": "integer", "format": "int32"}, "OrderStatusFilter": {"type": "object", "properties": {"_brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_refreshCache": {"type": "boolean"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_accountFilter": {"$ref": "#/components/schemas/AccountFilter"}, "_stockFilter": {"$ref": "#/components/schemas/StockFilter"}, "_showBuyOrders": {"type": "boolean"}, "_showSellOrders": {"type": "boolean"}, "_showLeverageOrders": {"type": "boolean"}, "_showSpecialOrders": {"type": "boolean"}, "_onlyMyOrders": {"type": "boolean"}, "_orderId": {"type": "string", "nullable": true}, "_orderStatus": {"$ref": "#/components/schemas/OrderStatus"}, "_orderTipology": {"$ref": "#/components/schemas/OrderTypology"}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_onlyOrdersWithExe": {"type": "boolean"}, "_gamingId": {"type": "integer", "format": "int32", "nullable": true}, "_orderShowType": {"$ref": "#/components/schemas/OrderShowType"}, "StrategyId": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "OrderStatusModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/OrderStatusFilter"}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "OrderType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "OrderTypology": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "PViewModel": {"type": "object", "properties": {"platformType": {"type": "integer", "format": "int32", "nullable": true}, "viewId": {"type": "string", "nullable": true}, "viewName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PViewsModel": {"type": "object", "properties": {"platformType": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PagingSelector": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PermissionType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "PersonalList": {"type": "object", "properties": {"_customerId": {"type": "integer", "format": "int32", "nullable": true}, "_id": {"type": "string", "nullable": true}, "_name": {"type": "string", "nullable": true}, "_viewTemplateName": {"type": "string", "nullable": true}, "_viewId": {"type": "string", "nullable": true}, "_details": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalListEntry"}, "nullable": true}, "_tickerAddicted": {"type": "integer", "format": "int32", "nullable": true}, "_systemView": {"type": "integer", "format": "int32"}, "_portfolioView": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PersonalListBrokerModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "Channel": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PersonalListDetailModel": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "Order": {"type": "integer", "format": "int32"}, "GroupName": {"type": "string", "nullable": true}, "GroupFormat": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PersonalListEntry": {"type": "object", "properties": {"_marketCode": {"type": "string", "nullable": true}, "_stockCode": {"type": "string", "nullable": true}, "_order": {"type": "integer", "format": "int32"}, "_groupName": {"type": "string", "nullable": true}, "_groupFormat": {"type": "string", "nullable": true}, "logo_dark": {"type": "string", "nullable": true}, "logo_light": {"type": "string", "nullable": true}, "_stockDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PersonalListModel": {"type": "object", "properties": {"personalList": {"$ref": "#/components/schemas/PersonalList"}, "basketCode": {"type": "string", "nullable": true}, "extraFilter": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PersonalView": {"type": "object", "properties": {"_viewId": {"type": "string", "nullable": true}, "_customerId": {"type": "integer", "format": "int32", "nullable": true}, "_name": {"type": "string", "nullable": true}, "_description": {"type": "string", "nullable": true}, "_templateName": {"type": "string", "nullable": true}, "_platformType": {"type": "integer", "format": "int32"}, "_columns": {"type": "array", "items": {"$ref": "#/components/schemas/Col"}, "nullable": true}, "_category": {"$ref": "#/components/schemas/ViewCategory"}, "_isTemplate": {"type": "boolean", "readOnly": true}, "_setterOrigin": {"type": "string", "nullable": true, "writeOnly": true}, "CachePrefix": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PersonalViewModel": {"type": "object", "properties": {"personalView": {"$ref": "#/components/schemas/PersonalView"}}, "additionalProperties": false}, "PlatformType": {"enum": [0, 1, 2, 4, 8, 16, -1], "type": "integer", "format": "int32"}, "PortfolioFilter": {"type": "object", "properties": {"_brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_refreshCache": {"type": "boolean"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_accountFilter": {"$ref": "#/components/schemas/AccountFilter"}, "_stockFilter": {"$ref": "#/components/schemas/StockFilter"}, "_onlyShortMultiDay": {"type": "boolean"}, "_onlyIpoDerivative": {"type": "boolean"}, "_currency": {"type": "string", "nullable": true}, "_includeAfterHour": {"type": "boolean"}, "_valorizationMode": {"type": "integer", "format": "int32"}, "_gamingId": {"type": "integer", "format": "int32", "nullable": true}, "_resetPortfolio": {"type": "boolean"}, "PositionId": {"type": "integer", "format": "int32", "nullable": true}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_portfolioId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PortfolioModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/PortfolioFilter"}, "file_type": {"$ref": "#/components/schemas/ExportFileType"}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "PriceTypes": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "PriipsKidCash": {"type": "object", "properties": {"ProductId": {"type": "integer", "format": "int32"}, "ProductCompanyCode": {"type": "string", "nullable": true}, "Isin": {"type": "string", "nullable": true}, "FileName": {"type": "string", "nullable": true}, "FileVersion": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PriipsKidDer": {"type": "object", "properties": {"FileVersion": {"type": "string", "nullable": true}, "ProductMacroFamilyGroup": {"type": "string", "nullable": true}, "ProductFamilyGroup": {"type": "string", "nullable": true}, "ProductFamilyCode": {"type": "string", "nullable": true}, "ProductSubFamilyCode": {"type": "string", "nullable": true}, "ProductClassCode": {"type": "string", "nullable": true}, "OperationType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PriipsKidInfoModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "MarketCode": {"type": "string", "nullable": true}, "StockType": {"type": "string", "nullable": true}, "Isin": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "StockDescription": {"type": "string", "nullable": true}, "DerFamilyCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProfStatementType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "ProfitLossFilter": {"type": "object", "properties": {"_brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_refreshCache": {"type": "boolean"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_accountFilter": {"$ref": "#/components/schemas/AccountFilter"}, "_stockFilter": {"$ref": "#/components/schemas/StockFilter"}, "_currency": {"type": "string", "nullable": true}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_closingOrderId": {"type": "string", "nullable": true}, "_valorizationMode": {"type": "integer", "format": "int32", "nullable": true}, "_profitLossType": {"type": "integer", "format": "int32", "nullable": true}, "_gamingId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ProfitLossModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/ProfitLossFilter"}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "Report": {"type": "object", "properties": {"effectiveTradingReport": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/EffectiveTradingReport"}, "nullable": true}, "virtualTradingReport": {"$ref": "#/components/schemas/VirtualTradingReport"}, "gamingReport": {"$ref": "#/components/schemas/GamingReport"}}, "additionalProperties": false}, "SaveApplicationConfigurationModel": {"type": "object", "properties": {"Platform": {"$ref": "#/components/schemas/PlatformType"}, "Settings": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SaveProfStatementModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "Service": {"$ref": "#/components/schemas/ProfStatementType"}, "Professional": {"type": "boolean"}}, "additionalProperties": false}, "SearchCommissionsModel": {"type": "object", "properties": {"BondAccountId": {"type": "integer", "format": "int32", "nullable": true}, "StartTime": {"type": "string", "format": "date-time"}, "EndTime": {"type": "string", "format": "date-time"}, "Market": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchFilter": {"type": "object", "properties": {"_searchFilterType": {"$ref": "#/components/schemas/SearchFilterType"}, "_globalSearchObject": {"$ref": "#/components/schemas/GlobalSearchObject"}, "_cwSearchObject": {"$ref": "#/components/schemas/CwSearchObject"}, "_obbSearchObject": {"$ref": "#/components/schemas/ObbSearchObject"}, "_derivateSearchObject": {"$ref": "#/components/schemas/DerivateSearchObject"}, "_advancedObbSearchObject": {"$ref": "#/components/schemas/AdvancedObbSearchObject"}, "_certXSearchObject": {"$ref": "#/components/schemas/CertXSearchObject"}, "IsValidFilter": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "SearchFilterType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "SearchInformativeNoteModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAcctId": {"type": "integer", "format": "int32", "nullable": true}, "DossierId": {"type": "integer", "format": "int32", "nullable": true}, "CashAcctId": {"type": "integer", "format": "int32", "nullable": true}, "CustomerCode": {"type": "string", "nullable": true}, "DocumentType": {"type": "string", "nullable": true}, "Market": {"type": "string", "nullable": true}, "SelectedInstrumentDescription": {"type": "string", "nullable": true}, "InstrumentDescription": {"type": "string", "nullable": true}, "OrderCode": {"type": "string", "nullable": true}, "StartTime": {"type": "string", "format": "date-time"}, "EndTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SearchProductModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "InstrumentType": {"type": "string", "nullable": true}, "InstrumentText": {"type": "string", "nullable": true}, "Market": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "SelectListGroup": {"type": "object", "properties": {"Disabled": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SelectListItem": {"type": "object", "properties": {"Disabled": {"type": "boolean"}, "Group": {"$ref": "#/components/schemas/SelectListGroup"}, "Selected": {"type": "boolean"}, "Text": {"type": "string", "nullable": true}, "Value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SetDestinationLiquidityModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "CashAccountId": {"type": "integer", "format": "int32"}, "Amount": {"type": "number", "format": "double"}, "Type": {"$ref": "#/components/schemas/LiquidityWays"}}, "additionalProperties": false}, "SetLiquidityModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/LiquidityParam"}}, "additionalProperties": false}, "ShortLevaInfoModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "ShortServiceId": {"$ref": "#/components/schemas/ShortLevaServiceId"}, "MarketCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShortLevaServiceId": {"enum": [1, 2, 4, 10], "type": "integer", "format": "int32"}, "ShortLiquidityAmountRequiredModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "PortfolioId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SpecificBrokerProperties": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "Accounts": {"type": "array", "items": {"$ref": "#/components/schemas/BrokerAccountProperties"}, "nullable": true}, "TradePermissions": {"$ref": "#/components/schemas/TradePermission"}, "InfoPermissions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/InfoPermission"}, "nullable": true}}, "additionalProperties": false}, "StockFilter": {"type": "object", "properties": {"_stockTypeGroup": {"type": "string", "nullable": true}, "_stockType": {"type": "string", "nullable": true}, "_stockSubType": {"type": "string", "nullable": true}, "_stockTypeDetail": {"type": "string", "nullable": true}, "_marketCode": {"type": "string", "nullable": true}, "_searchType": {"$ref": "#/components/schemas/SearchType"}, "_searchText": {"type": "string", "nullable": true}, "_stockCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockKey": {"type": "object", "properties": {"MarketCode": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "IsValid": {"type": "boolean", "readOnly": true}, "Key": {"type": "string", "nullable": true, "readOnly": true}, "Tag": {"nullable": true, "readOnly": true}}, "additionalProperties": false}, "StockListFilterModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "filter": {"$ref": "#/components/schemas/SearchFilter"}}, "additionalProperties": false}, "StockListModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "type": {"$ref": "#/components/schemas/StockListType"}, "code": {"type": "string", "nullable": true}, "filter": {"$ref": "#/components/schemas/SearchFilter"}, "stocks": {"type": "array", "items": {"$ref": "#/components/schemas/StockKey"}, "nullable": true}, "broker": {"$ref": "#/components/schemas/BrokerName"}, "extra_filters": {"type": "string", "nullable": true}, "sorting": {"$ref": "#/components/schemas/WebFlowDataSortModel"}, "page": {"type": "integer", "format": "int32", "writeOnly": true}, "pageSize": {"type": "integer", "format": "int32", "writeOnly": true}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "StockListType": {"enum": [0, 1, 3, 4], "type": "integer", "format": "int32"}, "StockModel": {"type": "object", "properties": {"marketCode": {"type": "string", "nullable": true}, "stockCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StockSearchModel": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "marketCode": {"type": "string", "nullable": true}, "marketGroups": {"type": "array", "items": {"type": "string"}, "nullable": true}, "page": {"type": "integer", "format": "int32", "writeOnly": true}, "pageSize": {"type": "integer", "format": "int32", "writeOnly": true}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "StocksModel": {"type": "object", "properties": {"cols": {"type": "array", "items": {"type": "string"}, "nullable": true}, "stocks": {"type": "array", "items": {"$ref": "#/components/schemas/StockKey"}, "nullable": true}}, "additionalProperties": false}, "StrategyAlarmModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "FromDate": {"type": "string", "format": "date-time"}, "ToDate": {"type": "string", "format": "date-time"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "StrategyConditionTypes": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "StrategyEvaluationModes": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StrategyStateCode": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "StrategyStatusFilter": {"type": "object", "properties": {"_brokerName": {"$ref": "#/components/schemas/BrokerName"}, "_refreshCache": {"type": "boolean"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}, "_accountFilter": {"$ref": "#/components/schemas/AccountFilter"}, "_stockFilter": {"$ref": "#/components/schemas/StockFilter"}, "_strategyStatus": {"$ref": "#/components/schemas/StrategyStateCode"}, "_fromDate": {"type": "string", "format": "date-time", "nullable": true}, "_toDate": {"type": "string", "format": "date-time", "nullable": true}, "_strategyId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StrategyStatusModel": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "filter": {"$ref": "#/components/schemas/StrategyStatusFilter"}, "paging": {"$ref": "#/components/schemas/PagingSelector"}}, "additionalProperties": false}, "TimeAndSaleFilter": {"type": "object", "properties": {"_filterMode": {"$ref": "#/components/schemas/TimeAndSaleFilterMode"}, "_ticks": {"type": "integer", "format": "int32", "nullable": true}, "_minutes": {"type": "integer", "format": "int32", "nullable": true}, "_startTime": {"$ref": "#/components/schemas/TimeSpanDTO"}, "StartTime": {"type": "string", "format": "date-span"}, "_endTime": {"$ref": "#/components/schemas/TimeSpanDTO"}, "EndTime": {"type": "string", "format": "date-span"}, "Date": {"type": "string", "format": "date-time", "nullable": true}, "EndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TimeAndSaleFilterMode": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "TimeAndSaleModel": {"type": "object", "properties": {"filetype": {"$ref": "#/components/schemas/ExportFileType"}, "stock": {"$ref": "#/components/schemas/StockKey"}, "basketcode": {"type": "string", "nullable": true}, "filter": {"$ref": "#/components/schemas/TimeAndSaleFilter"}}, "additionalProperties": false}, "TimeFrame": {"type": "object", "properties": {"_timeFrameType": {"$ref": "#/components/schemas/TimeFrameType"}, "_startDate": {"type": "string", "format": "date-time"}, "_endDate": {"type": "string", "format": "date-time", "nullable": true}, "_startTime": {"$ref": "#/components/schemas/TimeSpanDTO"}, "_endTime": {"$ref": "#/components/schemas/TimeSpanDTO"}, "_timeFrameEOD": {"$ref": "#/components/schemas/EndOfDayTimeFrameType"}, "_timeFrameValue": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "TimeFrameType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "TimeFrameTypeNEW": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "TimeSpanDTO": {"type": "object", "properties": {"_days": {"type": "integer", "format": "int32"}, "_hours": {"type": "integer", "format": "int32"}, "_minutes": {"type": "integer", "format": "int32"}, "_seconds": {"type": "integer", "format": "int32"}, "_milliseconds": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TobinTaxModel": {"type": "object", "properties": {"Broker": {"$ref": "#/components/schemas/BrokerName"}, "BondAccountCode": {"type": "string", "nullable": true}, "ShowOperations": {"type": "boolean"}, "FromDate": {"type": "string", "format": "date-time"}, "ToDate": {"type": "string", "format": "date-time"}, "InstrumentType": {"$ref": "#/components/schemas/InstrumentType"}}, "additionalProperties": false}, "ToolDetails": {"type": "object", "additionalProperties": false}, "TradePermission": {"type": "object", "properties": {"PermissionType": {"$ref": "#/components/schemas/PermissionType"}, "TradeGroups": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TradingInfoModel": {"type": "object", "properties": {"broker": {"$ref": "#/components/schemas/BrokerName"}, "marketCode": {"type": "string", "nullable": true}, "stockCode": {"type": "string", "nullable": true}, "stock": {"$ref": "#/components/schemas/StockKey"}, "gamingId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TradingType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "TradingViewSavedObject": {"type": "object", "properties": {"savetype": {"$ref": "#/components/schemas/CustomerConfigurationType"}, "json": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TvConfiguration": {"type": "object", "properties": {"supported_resolutions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "supports_group_request": {"type": "boolean"}, "supports_marks": {"type": "boolean"}, "supports_search": {"type": "boolean"}, "supports_timescale_marks": {"type": "boolean"}}, "additionalProperties": false}, "TvMobileModel": {"type": "object", "properties": {"market": {"type": "string", "nullable": true}, "stock": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "theme": {"type": "string", "nullable": true}, "resolution": {"type": "string", "nullable": true}, "saveType": {"type": "integer", "format": "int32"}, "useNewVersion": {"type": "boolean"}, "savedchart": {"type": "string", "nullable": true}, "authcookie": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TvSymbolInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "ticker": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "session": {"type": "string", "nullable": true}, "holidays": {"type": "string", "nullable": true}, "corrections": {"type": "string", "nullable": true}, "exchange-traded": {"type": "string", "nullable": true}, "exchange-listed": {"type": "string", "nullable": true}, "timezone": {"type": "string", "nullable": true}, "format": {"type": "string", "nullable": true}, "minmov": {"type": "number", "format": "double", "nullable": true}, "pricescale": {"type": "number", "format": "double", "nullable": true}, "minmov2": {"type": "number", "format": "double", "nullable": true}, "fractional": {"type": "boolean", "nullable": true}, "pointvalue": {"type": "number", "format": "double", "nullable": true}, "has_intraday": {"type": "boolean", "nullable": true}, "supported_resolutions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "intraday_multipliers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "has_daily": {"type": "boolean", "nullable": true}, "has_seconds": {"type": "boolean", "nullable": true}, "seconds_multipliers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "has_weekly_and_monthly": {"type": "boolean", "nullable": true}, "has_empty_bars": {"type": "boolean", "nullable": true}, "force_session_rebuild": {"type": "boolean", "nullable": true}, "has_no_volume": {"type": "boolean", "nullable": true}, "volume_precision": {"type": "integer", "format": "int32", "nullable": true}, "data_status": {"type": "string", "nullable": true}, "expired": {"type": "boolean", "nullable": true}, "expiration_date": {"type": "string", "nullable": true}, "sector": {"type": "string", "nullable": true}, "industry": {"type": "string", "nullable": true}, "original_currency_code": {"type": "string", "nullable": true}, "currency_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TvSymbolSearch": {"type": "object", "properties": {"Symbol": {"type": "string", "nullable": true}, "full_name": {"type": "string", "nullable": true}, "Ticker": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Exchange": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Type": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "type": "integer", "format": "int32"}, "UpdateOrderModel": {"type": "object", "properties": {"par": {"$ref": "#/components/schemas/UpdateOrderParameters"}}, "additionalProperties": false}, "UpdateOrderParameters": {"type": "object", "properties": {"BrokerName": {"$ref": "#/components/schemas/BrokerName"}, "OrderID": {"type": "string", "nullable": true}, "Price": {"type": "number", "format": "double"}, "Quantity": {"type": "number", "format": "double"}, "CustomerID": {"type": "integer", "format": "int32"}, "AccessToken": {"type": "string", "nullable": true}, "IsFast": {"type": "boolean"}, "StopPrice": {"type": "number", "format": "double", "nullable": true}, "GamingId": {"type": "integer", "format": "int32"}, "Parked": {"type": "boolean"}, "DeleteAfterInsert": {"type": "boolean", "nullable": true}, "ValidityDays": {"type": "integer", "format": "int32", "nullable": true}, "OrderTypology": {"$ref": "#/components/schemas/OrderTypology"}, "ClientInfo": {"$ref": "#/components/schemas/ClientInfo"}}, "additionalProperties": false}, "UpdatePersonalListModel": {"type": "object", "properties": {"ListId": {"type": "string", "nullable": true}, "Details": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalListDetailModel"}, "nullable": true}}, "additionalProperties": false}, "UrlCheckRequest": {"type": "object", "properties": {"Username": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ViewCategory": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "VirtualTradingReport": {"type": "object", "properties": {"insertEditDelete": {"type": "boolean"}, "reservedOrdersActivation": {"type": "boolean"}, "marketOrdersExecutionRefusalCancelation": {"type": "boolean"}}, "additionalProperties": false}, "VolatilityChartDataModel": {"type": "object", "properties": {"underlyingMarketCode": {"type": "string", "nullable": true}, "underlyingStockCode": {"type": "string", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "WebFlowDataSortModel": {"type": "object", "properties": {"columnName": {"type": "string", "nullable": true}, "direction": {"$ref": "#/components/schemas/Direction"}}, "additionalProperties": false}, "XTMarginiShortMultidayItemModel": {"type": "object", "properties": {"BankId": {"type": "integer", "format": "int32"}, "BondAcct13": {"type": "string", "nullable": true}, "BondAcctId": {"type": "integer", "format": "int32"}, "CashAcctId": {"type": "integer", "format": "int32"}, "DossierId": {"type": "integer", "format": "int32"}, "CustomerCode": {"type": "string", "nullable": true}, "ProductId": {"type": "integer", "format": "int32"}, "StartQty": {"type": "number", "format": "double"}, "NewQty": {"type": "number", "format": "double"}, "CloseQty": {"type": "number", "format": "double"}, "SmdQty": {"type": "number", "format": "double"}, "LastCollateralPerc": {"type": "number", "format": "double"}, "NewCollateralPerc": {"type": "number", "format": "double"}, "LastRefPrice": {"type": "number", "format": "double"}, "NewRefPrice": {"type": "number", "format": "double"}, "CountervalueSmd": {"type": "number", "format": "double"}, "MarginValueSmd": {"type": "number", "format": "double"}, "TotalSmdWarranty": {"type": "number", "format": "double"}, "CollateralAmount": {"type": "number", "format": "double"}, "ValueDate": {"type": "string", "format": "date-time", "nullable": true}, "RecordDate": {"type": "string", "format": "date-time", "nullable": true}, "CollateralProcessed": {"type": "boolean"}, "PriceOpen": {"type": "number", "format": "double"}, "BondAmount": {"type": "number", "format": "double"}, "AccountCurrencyCode": {"type": "string", "nullable": true}, "MarketCode": {"type": "string", "nullable": true}, "StockCode": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Isin": {"type": "string", "nullable": true}, "PortfolioType": {"type": "string", "nullable": true}, "LmdCalcPrice": {"type": "number", "format": "double", "nullable": true}, "LmdStopOrderCode": {"type": "string", "nullable": true}, "LmdBookedQty": {"type": "number", "format": "double"}, "PercStopLossLmd": {"type": "number", "format": "double", "nullable": true}, "NewC9BalanceAmount": {"type": "number", "format": "double"}, "OpenDate": {"type": "string", "format": "date-time", "nullable": true}, "CollateralLmd": {"type": "number", "format": "double"}}, "additionalProperties": false}, "XTMarginiShortMultidayModel": {"type": "object", "properties": {"BondAccountId": {"type": "integer", "format": "int32"}, "CashAccountId": {"type": "integer", "format": "int32"}, "BondAcctCodeParam": {"type": "string", "nullable": true}, "CashAcctCodeParam": {"type": "string", "nullable": true}, "MktParam": {"type": "string", "nullable": true}, "ProductSearch": {"type": "integer", "format": "int32"}, "ProductTextParam": {"type": "string", "nullable": true}, "DateParam": {"type": "string", "format": "date-time"}, "PagingSelector": {"$ref": "#/components/schemas/PagingSelector"}, "Items": {"type": "array", "items": {"$ref": "#/components/schemas/XTMarginiShortMultidayItemModel"}, "nullable": true}}, "additionalProperties": false}}}}