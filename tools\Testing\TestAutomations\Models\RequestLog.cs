using System;

namespace TestAutomations.Models
{
    public class RequestLog
    {
        // Timestamp della richiesta
        public DateTime Timestamp { get; set; }

        // Metodo HTTP (GET, POST, etc.)
        public string Method { get; set; }

        // Endpoint chiamato
        public string Endpoint { get; set; }

        // Corpo della richiesta (JSON serializzato)
        public string RequestBody { get; set; }

        // Payload in formato compatto (per il log)
        public string Payload { get; set; }

        // Stato HTTP della risposta
        public int StatusCode { get; set; }

        // Corpo della risposta (JSON serializzato)
        public string ResponseBody { get; set; }

        // Contenuto della risposta compatto (per il log)
        public string ResponseContent { get; set; }

        // Messaggio di errore (se presente)
        public string Error { get; set; }

        // Messaggio informativo (se presente)
        public string InfoMessage { get; set; }

        // Tempo impiegato per la risposta
        public TimeSpan? ResponseTime { get; set; }

        // Costruttore di default
        public RequestLog()
        {
            Timestamp = DateTime.Now;
        }

        // Rappresentazione a stringa per il log
        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] {Method} {Endpoint} -> {StatusCode} " +
                   $"{(ResponseTime.HasValue ? $"({ResponseTime.Value.TotalMilliseconds:F0} ms)" : "")} " +
                   $"{(string.IsNullOrEmpty(Error) ? "" : $"ERROR: {Error}")}";
        }
    }
}