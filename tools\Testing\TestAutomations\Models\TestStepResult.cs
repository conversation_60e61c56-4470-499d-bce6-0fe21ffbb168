using System;

namespace TestAutomations.Models
{
    public class TestStepResult
    {
        // Nome del passo
        public string StepName { get; }
        
        // Esito del passo (true = successo)
        public bool Success { get; }
        
        // Messaggio di dettaglio (opzionale)
        public string Message { get; }
        
        // Timestamp di esecuzione
        public DateTime Timestamp { get; }
        
        // Costruttore
        public TestStepResult(string stepName, bool success, string message = null)
        {
            StepName = stepName ?? throw new ArgumentNullException(nameof(stepName));
            Success = success;
            Message = message;
            Timestamp = DateTime.Now;
        }
        
        // Rappresentazione stringa
        public override string ToString()
        {
            return $"{Timestamp:yyyy-MM-dd HH:mm:ss} - {StepName}: {(Success ? "SUCCESS" : "FAILED")}{(string.IsNullOrEmpty(Message) ? "" : $" - {Message}")}";
        }
    }
} 