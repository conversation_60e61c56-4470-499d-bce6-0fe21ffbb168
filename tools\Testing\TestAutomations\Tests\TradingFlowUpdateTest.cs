using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class TradingFlowUpdateTest : TestBase
    {
        private readonly string[] _args;
        private OTTradingClient _client;
        private readonly string _username;
        private readonly string _password;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        // Parametri di configurazione per retry
        private const int MaxRetryAttempts = 3;
        private const int InitialRetryDelayMs = 1000;
        private const double RetryBackoffFactor = 1.5;

        public TradingFlowUpdateTest(string name, string[] args = null, bool isDummy = false) : base(name, isDummy)
        {
            _args = args ?? new string[0];

            // Ottieni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test di trading flow
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test di trading flow Update");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // Esegui il test scenario
                bool testResult = await ExecuteTradingFlowUpdate();

                if (!testResult)
                {
                    Logger.Error("Test di trading flow Update fallito");
                    return false;
                }

                Logger.Info("Test di trading flow Update completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("TradingFlowUpdate", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlowUpdate", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("TradingFlowUpdate", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlowUpdate", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        public async Task<bool> ExecuteTradingFlowUpdate()
        {
            try
            {
                // Esegui il test scenario
                bool result = await RunTestScenarioAsync(_args);

                // Aggiungiamo un log riepilogativo
                LogTestStep("Flusso Trading Update", result, result ? "Test completato con successo" : "Test fallito");

                return result;
            }
            catch (Exception ex)
            {
                LogTestStep("Flusso Trading Update", false, $"Errore durante l'esecuzione: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> RunTestScenarioAsync(string[] args)
        {
            // Parametri di test da env
            var marketCode = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "MTA";
            var stockCode = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "BGN";
            var orderType = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_ORDER_TYPE"), out var ot) ? ot : 0;
            var price = double.TryParse(Environment.GetEnvironmentVariable("OT_TEST_PRICE"), out var p) ? p : 55.9;
            var quantity = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_QUANTITY"), out var q) ? q : 1;
            var newQuantity = quantity + 1; // Incrementiamo la quantità per l'aggiornamento
            var newPrice = price * 0.99; // Riduciamo leggermente il prezzo per l'aggiornamento

            Logger.Info($"Parametri di test: Mercato={marketCode}, Titolo={stockCode}, Tipo={orderType}, Prezzo={price}, Quantità={quantity}");
            Logger.Info($"Parametri di aggiornamento: Nuova Quantità={newQuantity}, Nuovo Prezzo={newPrice}");

            string orderId = null;
            var testSteps = new List<TestStepResult>();

            try
            {
                // 1. Verifica saldo disponibile (solo per acquisti)
                if (orderType == 0) // 0=acquisto
                {
                    bool enoughBalance = await CheckAccountBalanceAsync(price, newQuantity); // Verifichiamo con la nuova quantità
                    if (!enoughBalance)
                    {
                        LogTestStep("VerificaSaldo", false, "Saldo insufficiente per l'acquisto");
                        testSteps.Add(new TestStepResult("VerificaSaldo", false, "Saldo insufficiente per l'acquisto"));
                        return false;
                    }
                    LogTestStep("VerificaSaldo", true, "Saldo sufficiente per l'acquisto");
                    testSteps.Add(new TestStepResult("VerificaSaldo", true, "Saldo sufficiente per l'acquisto"));
                }

                // 2. Inserimento ordine iniziale
                var insertResp = await InsertOrderWithRetryAsync((int)(BrokerName.Sella), marketCode, stockCode, orderType, price, quantity);
                orderId = TryExtractOrderId(insertResp);

                if (string.IsNullOrEmpty(orderId))
                {
                    LogTestStep("InserimentoOrdine", false, "Impossibile ottenere l'ID dell'ordine");
                    testSteps.Add(new TestStepResult("InserimentoOrdine", false, "Impossibile ottenere l'ID dell'ordine"));
                    return false;
                }

                LogTestStep("InserimentoOrdine", true, $"OrderID: {orderId}");
                testSteps.Add(new TestStepResult("InserimentoOrdine", true, $"OrderID: {orderId}"));

                // 2.5 confermare ordine
                var confirmResp = await ConfirmOrderWithRetryAsync(orderId, 0);
                bool confirmSuccess = JsonResponseHelper.IsConfirmationSuccessful(confirmResp);

                if (!confirmSuccess)
                {
                    LogTestStep("ConfermaOrdine", false, "Conferma ordine fallita");
                    testSteps.Add(new TestStepResult("ConfermaOrdine", false, "Conferma ordine fallita"));
                    return false;
                }

                LogTestStep("ConfermaOrdine", true, "Ordine confermato con successo");
                testSteps.Add(new TestStepResult("ConfermaOrdine", true, "Ordine confermato con successo"));

                // 3. Aggiornamento ordine
                var updateResp = await UpdateOrderWithRetryAsync((int)BrokerName.Sella, orderId, newQuantity, newPrice);
                bool updateSuccess = IsUpdateSuccessful(updateResp);

                if (!updateSuccess)
                {
                    LogTestStep("AggiornamentoOrdine", false, "Aggiornamento ordine fallito");
                    testSteps.Add(new TestStepResult("AggiornamentoOrdine", false, "Aggiornamento ordine fallito"));
                    return false;
                }

                LogTestStep("AggiornamentoOrdine", true, "Ordine aggiornato con successo");
                testSteps.Add(new TestStepResult("AggiornamentoOrdine", true, "Ordine aggiornato con successo"));

                // 3.5 confermare aggiornamento ordine
                var confirmUpdResp = await ConfirmOrderWithRetryAsync(orderId, 1);
                bool confirmUpdSuccess = JsonResponseHelper.IsConfirmationSuccessful(confirmResp);

                if (!confirmSuccess)
                {
                    LogTestStep("ConfermaOrdine", false, "Conferma ordine fallita");
                    testSteps.Add(new TestStepResult("ConfermaOrdine", false, "Conferma update ordine fallita"));
                    return false;
                }

                LogTestStep("ConfermaOrdine", true, "Ordine confermato con successo");
                testSteps.Add(new TestStepResult("ConfermaOrdine", true, "Ordine aggiornato con successo"));

                // 4. Verifica che l'ordine sia stato aggiornato correttamente
                var orderStatus = await GetOrderDetailsWithRetryAsync(orderId);
                bool verifyUpdate = VerifyOrderUpdate(orderStatus, newQuantity, newPrice);

                if (!verifyUpdate)
                {
                    LogTestStep("VerificaAggiornamento", false, "Verifica aggiornamento ordine fallita");
                    testSteps.Add(new TestStepResult("VerificaAggiornamento", false, "Verifica aggiornamento ordine fallita"));
                    return false;
                }

                LogTestStep("VerificaAggiornamento", true, "Verifica aggiornamento ordine completata con successo");
                testSteps.Add(new TestStepResult("VerificaAggiornamento", true, "Verifica aggiornamento ordine completata con successo"));

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante l'esecuzione del test scenario: {ex.Message}");
                return false;
            }
        }
        private async Task<bool> CheckAccountBalanceAsync(double price, int quantity)
        {
            try
            {
                // GetAccountBalanceAsync ora restituisce direttamente il valore di AVAILABLE_LIQUIDITY
                decimal availableBalance = await _client.GetAccountBalanceAsync();
                decimal requiredAmount = (decimal)(price * quantity);

                Logger.Info($"Saldo disponibile: {availableBalance}, Importo richiesto: {requiredAmount}");
                return availableBalance >= requiredAmount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante la verifica del saldo: {ex.Message}");
                // In caso di errore, assumiamo che il saldo sia sufficiente
                return true;
            }
        }

        private async Task<JsonElement> InsertOrderWithRetryAsync(int broker, string market, string stock, int orderType, double price, int quantity)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di inserimento ordine");
                    return await _client.InsertOrderAsync(broker, market, stock, orderType, price, quantity);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di inserimento ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile inserire l'ordine dopo tutti i tentativi");
        }

        private async Task<JsonElement> UpdateOrderWithRetryAsync(int broker, string orderId, int newQuantity, double newPrice)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di aggiornamento ordine {orderId}");
                    // Aggiungiamo il metodo UpdateOrderAsync al client
                    return await _client.UpdateOrderAsync(broker, orderId, newQuantity, newPrice);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di aggiornamento ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile aggiornare l'ordine dopo tutti i tentativi");
        }

        /// <summary>
        /// 0 Insert, 1 Update, 2 Delete
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="type">0 Insert, 1 Update, 2 Delete</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<JsonElement> ConfirmOrderWithRetryAsync(string orderId, int type)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di conferma ordine {orderId}");
                    return await _client.ConfirmOrderAsync((int)BrokerName.Sella, orderId, type);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di conferma ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile confermare l'ordine dopo tutti i tentativi");
        }

        private async Task<JsonElement> GetOrderDetailsWithRetryAsync(string orderId)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di ottenere dettagli ordine {orderId}");
                    return await _client.GetOrderDetailsAsync((int)BrokerName.Sella, orderId);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di ottenere dettagli ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile ottenere i dettagli dell'ordine dopo tutti i tentativi");
        }

        private string TryExtractOrderId(JsonElement response)
        {
            // Cerca l'ID dell'ordine in vari campi possibili
            if (response.TryGetProperty("OrderID", out var id1) && id1.ValueKind == JsonValueKind.String)
                return id1.GetString();

            if (response.TryGetProperty("orderId", out var id2) && id2.ValueKind == JsonValueKind.String)
                return id2.GetString();

            if (response.TryGetProperty("orderID", out var id3) && id3.ValueKind == JsonValueKind.String)
                return id3.GetString();

            if (response.TryGetProperty("order_id", out var id4) && id4.ValueKind == JsonValueKind.String)
                return id4.GetString();

            // Cerca in un oggetto data
            if (response.TryGetProperty("data", out var dataElem) && dataElem.ValueKind == JsonValueKind.Object)
            {
                if (dataElem.TryGetProperty("OrderID", out var dataId1) && dataId1.ValueKind == JsonValueKind.String)
                    return dataId1.GetString();

                if (dataElem.TryGetProperty("orderId", out var dataId2) && dataId2.ValueKind == JsonValueKind.String)
                    return dataId2.GetString();
            }

            return null;
        }

        private bool IsUpdateSuccessful(JsonElement response)
        {
            // Verifica se l'aggiornamento è andato a buon fine
            if (response.TryGetProperty("success", out var successElem) &&
                successElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("isSuccess", out var isSuccessElem) &&
                isSuccessElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("status", out var statusElem) &&
                statusElem.ValueKind == JsonValueKind.String &&
                statusElem.GetString().ToLower() == "ok")
                return true;

            if (response.TryGetProperty("resultStatus", out var resultStatusElem) &&
                resultStatusElem.ValueKind == JsonValueKind.String &&
                resultStatusElem.GetString().ToLower() == "ready")
                return true;

            return false;
        }

        private bool VerifyOrderUpdate(JsonElement orderDetails, int expectedQuantity, double expectedPrice)
        {
            try
            {
                // Verifica che la quantità sia stata aggiornata
                if (orderDetails.TryGetProperty("quantity", out var quantityElem) &&
                    quantityElem.ValueKind == JsonValueKind.Number)
                {
                    int actualQuantity = quantityElem.GetInt32();
                    if (actualQuantity != expectedQuantity)
                    {
                        Logger.Warning($"Quantità non aggiornata correttamente. Atteso: {expectedQuantity}, Attuale: {actualQuantity}");
                        return false;
                    }
                }
                else
                {
                    Logger.Warning("Impossibile trovare il campo 'quantity' nei dettagli dell'ordine");
                    return false;
                }

                // Verifica che il prezzo sia stato aggiornato
                if (orderDetails.TryGetProperty("price", out var priceElem) &&
                    priceElem.ValueKind == JsonValueKind.Number)
                {
                    double actualPrice = priceElem.GetDouble();
                    // Confronto con tolleranza per i numeri in virgola mobile
                    if (Math.Abs(actualPrice - expectedPrice) > 0.001)
                    {
                        Logger.Warning($"Prezzo non aggiornato correttamente. Atteso: {expectedPrice}, Attuale: {actualPrice}");
                        return false;
                    }
                }
                else
                {
                    Logger.Warning("Impossibile trovare il campo 'price' nei dettagli dell'ordine");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante la verifica dell'aggiornamento dell'ordine: {ex.Message}");
                return false;
            }
        }
    }
}
