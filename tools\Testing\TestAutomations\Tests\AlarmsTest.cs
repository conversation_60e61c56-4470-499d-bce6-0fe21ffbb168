using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class AlarmsTest : TestBase
    {
        private readonly string _username;
        private readonly string _password;
        private OTTradingClient _client;
        private readonly string _market;
        private readonly string _stock;
        private readonly double _price;
        private string _alarmId;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        public AlarmsTest(string name, bool isDummy = false) : base(name, isDummy)
        {
            // Ottieni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            _market = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "MTA";
            _stock = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "BGN";
            _price = double.TryParse(Environment.GetEnvironmentVariable("OT_TEST_PRICE"), out var p) ? p : 0;

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test degli allarmi");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // 2. Verifica allarmi esistenti
                var currentAlarms = await GetCurrentAlarmsAsync();
                LogTestStep("Verifica Allarmi Esistenti", true, $"Trovati {currentAlarms.Count} allarmi esistenti");
                _testSteps.Add(new TestStepResult("Verifica Allarmi Esistenti", true, $"Trovati {currentAlarms.Count} allarmi esistenti"));

                // 3. Crea nuovo allarme
                if (!await CreateAlarmAsync())
                {
                    LogTestStep("Creazione Allarme", false, "Impossibile creare l'allarme");
                    _testSteps.Add(new TestStepResult("Creazione Allarme", false, "Impossibile creare l'allarme"));
                    return false;
                }
                LogTestStep("Creazione Allarme", true, $"Allarme creato con ID: {_alarmId}");
                _testSteps.Add(new TestStepResult("Creazione Allarme", true, $"Allarme creato con ID: {_alarmId}"));

                // 4. Verifica esistenza allarme
                if (!await VerifyAlarmExistsAsync())
                {
                    LogTestStep("Verifica Esistenza Allarme", false, "Allarme non trovato");
                    _testSteps.Add(new TestStepResult("Verifica Esistenza Allarme", false, "Allarme non trovato"));
                    return false;
                }
                LogTestStep("Verifica Esistenza Allarme", true);
                _testSteps.Add(new TestStepResult("Verifica Esistenza Allarme", true));

                // 5. Modifica allarme
                if (!await ModifyAlarmAsync())
                {
                    LogTestStep("Modifica Allarme", false, "Impossibile modificare l'allarme");
                    _testSteps.Add(new TestStepResult("Modifica Allarme", false, "Impossibile modificare l'allarme"));
                    return false;
                }
                LogTestStep("Modifica Allarme", true);
                _testSteps.Add(new TestStepResult("Modifica Allarme", true));

                // 6. Elimina allarme
                if (!await DeleteAlarmAsync())
                {
                    LogTestStep("Eliminazione Allarme", false, "Impossibile eliminare l'allarme");
                    _testSteps.Add(new TestStepResult("Eliminazione Allarme", false, "Impossibile eliminare l'allarme"));
                    return false;
                }
                LogTestStep("Eliminazione Allarme", true);
                _testSteps.Add(new TestStepResult("Eliminazione Allarme", true));

                // 7. Verifica cancellazione
                if (await VerifyAlarmExistsAsync())
                {
                    LogTestStep("Verifica Cancellazione", false, "L'allarme esiste ancora dopo l'eliminazione");
                    _testSteps.Add(new TestStepResult("Verifica Cancellazione", false, "L'allarme esiste ancora dopo l'eliminazione"));
                    return false;
                }
                LogTestStep("Verifica Cancellazione", true);
                _testSteps.Add(new TestStepResult("Verifica Cancellazione", true));

                Logger.Info("Test degli allarmi completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("Alarms", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("Alarms", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("Alarms", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("Alarms", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        private async Task<List<JsonElement>> GetCurrentAlarmsAsync()
        {
            try
            {
                // Ottieni gli allarmi per titolo
                var request = new
                {
                    stockKey = new
                    {
                        MarketCode = _market,
                        StockCode = _stock
                    },
                    filter = new
                    {
                        // Filtri per gli allarmi se necessari
                    }
                };

                var response = await _client.SendRequestAsync("POST", "api/Alarm/GetStockAlarms", request);
                return JsonResponseHelper.ExtractArrayProperty(response, "rows");
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero Allarmi", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero Allarmi", false, $"Errore: {ex.Message}"));
                return new List<JsonElement>();
            }
        }

        private async Task<bool> CreateAlarmAsync()
        {
            try
            {
                // NOTA: Le API per creare allarmi non sono esposte pubblicamente nello swagger
                // Questo test simula la creazione di un allarme per scopi dimostrativi
                LogTestStep("Creazione Allarme", false, "API per creazione allarmi non disponibile nello swagger pubblico");
                _testSteps.Add(new TestStepResult("Creazione Allarme", false, "API per creazione allarmi non disponibile"));

                // Genera un ID fittizio per continuare il test
                _alarmId = $"ALM_{DateTime.Now.Ticks}";
                return false; // Restituisce false perché l'API non è disponibile
            }
            catch (Exception ex)
            {
                LogTestStep("Creazione Allarme", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Creazione Allarme", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> VerifyAlarmExistsAsync()
        {
            var alarms = await GetCurrentAlarmsAsync();
            foreach (var alarm in alarms)
            {
                // Cerca un allarme con lo stesso ID o altri parametri corrispondenti
                if (alarm.TryGetProperty("id", out var id) && id.GetString() == _alarmId)
                    return true;

                // Se non trova l'id, controlla altri campi
                if (alarm.TryGetProperty("stockCode", out var stockCode) &&
                    stockCode.GetString() == _stock &&
                    alarm.TryGetProperty("marketCode", out var marketCode) &&
                    marketCode.GetString() == _market)
                {
                    return true;
                }
            }

            return false;
        }

        private async Task<bool> ModifyAlarmAsync()
        {
            try
            {
                // NOTA: Le API per modificare allarmi non sono esposte pubblicamente nello swagger
                LogTestStep("Modifica Allarme", false, "API per modifica allarmi non disponibile nello swagger pubblico");
                _testSteps.Add(new TestStepResult("Modifica Allarme", false, "API per modifica allarmi non disponibile"));
                return false;
            }
            catch (Exception ex)
            {
                LogTestStep("Modifica Allarme", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Modifica Allarme", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        private async Task<bool> DeleteAlarmAsync()
        {
            try
            {
                // NOTA: Le API per eliminare allarmi non sono esposte pubblicamente nello swagger
                LogTestStep("Eliminazione Allarme", false, "API per eliminazione allarmi non disponibile nello swagger pubblico");
                _testSteps.Add(new TestStepResult("Eliminazione Allarme", false, "API per eliminazione allarmi non disponibile"));
                return false;
            }
            catch (Exception ex)
            {
                LogTestStep("Eliminazione Allarme", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Eliminazione Allarme", false, $"Errore: {ex.Message}"));
                return false;
            }
        }

        // Restituisce i risultati dei test step
        public List<TestStepResult> GetTestSteps()
        {
            return _testSteps;
        }
    }
}