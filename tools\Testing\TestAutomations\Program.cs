﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Configuration;
using TestAutomations.Tests;
using TestAutomations.Utils;
using TestAutomations.Common;
using TestAutomations.Config;

namespace TestAutomations
{
    class Program
    {
        // Dichiarazione della configuration come statica per accesso globale
        public static IConfiguration Configuration { get; private set; }

        // Dichiarazione di BrokerConfig come statica per accesso globale
        public static BrokerConfig BrokerConfig { get; private set; }

        static async Task Main(string[] args)
        {
            try
            {
                // Configurazione con path esplicito a secrets.json
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("secrets.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables();

                Configuration = builder.Build();

                // Inizializza BrokerConfig con la configurazione
                BrokerConfig = new BrokerConfig(Configuration);

                // Copia le impostazioni nelle variabili d'ambiente per retrocompatibilità
                foreach (var setting in Configuration.AsEnumerable())
                {
                    if (!string.IsNullOrEmpty(setting.Value))
                    {
                        Environment.SetEnvironmentVariable(setting.Key, setting.Value);
                    }
                }

                // Mostra l'help se richiesto
                if (args.Contains("--help") || args.Contains("-h"))
                {
                    ShowHelp();
                    return;
                }

                // Controlla se è richiesta solo l'analisi dei log
                bool analyzeLogsOnly = args.Contains("--analyze-logs") || args.Contains("-a");
                if (analyzeLogsOnly)
                {
                    await AnalyzeLogsAsync(args);
                    return;
                }

                var orchestrator = new TestOrchestrator();

                //
                //    Sequenza Consigliata di Esecuzione
                //
                //    1. TEST automatico per login
                //    2. TEST automatico per Operatività Sella
                //    3. TEST automatico per Operatività Virtual Broker
                //    4. TEST automatico per Aggiornamento Ordini
                //    5. TEST automatico per Cancellazione Ordini
                //    6. TEST automatico allarmi
                //    7. TEST automatico gestione Preferiti
                //    8. TEST automatico Portafoglio
                //    9. TEST automatico profitti e perdite
                //

                // Registrazione dei test
                orchestrator.RegisterTest(new LoginTest(name: "dummy", isDummy: true));
                orchestrator.RegisterTest(new LoginTest(name: "login", isDummy: false));
                //
                orchestrator.RegisterTest(new TradingFlowTest(name: "trading_flow_dummy", args, isDummy: true));
                orchestrator.RegisterTest(new TradingFlowVirtualTest(name: "trading_flow_virtual_dummy", args, isDummy: true));
                orchestrator.RegisterTest(new TradingFlowUpdateTest(name: "trading_flow_update_dummy", args, isDummy: true));
                orchestrator.RegisterTest(new TradingFlowDeleteTest(name: "trading_flow_delete_dummy", args, isDummy: true));
                //
                orchestrator.RegisterTest(new TradingFlowTest(name: "trading_flow", args, isDummy: false));
                orchestrator.RegisterTest(new TradingFlowVirtualTest(name: "trading_flow_virtual", args, isDummy: false));
                orchestrator.RegisterTest(new TradingFlowUpdateTest(name: "trading_flow_update", args, isDummy: false));
                orchestrator.RegisterTest(new TradingFlowDeleteTest(name: "trading_flow_delete", args, isDummy: false));
                //
                orchestrator.RegisterTest(new AlarmsTest(name: "alarm", isDummy: false));
                //

                //// Test Preferiti
                orchestrator.RegisterTest(new PreferitiTest(name: "preferiti", isDummy: true));
                orchestrator.RegisterTest(new PreferitiInsertTest(name: "preferiti_insert", isDummy: true));
                orchestrator.RegisterTest(new PreferitiModifyTest(name: "preferiti_modify", isDummy: true));
                orchestrator.RegisterTest(new PreferitiDeleteTest(name: "preferiti_delete", isDummy: true));
                //

                //// Test Portafoglio
                orchestrator.RegisterTest(new PortafoglioTest(name: "portafoglio", isDummy: true));
                orchestrator.RegisterTest(new PortafoglioNewPositionTest(name: "portafoglio_new_position", isDummy: true));
                orchestrator.RegisterTest(new PortafoglioClosePositionTest(name: "portafoglio_close_position", isDummy: true));
                orchestrator.RegisterTest(new PortafoglioUpdateTest(name: "portafoglio_update", isDummy: true));
                //

                //// Test ProfitLoss
                orchestrator.RegisterTest(new ProfitLossTest(name: "profit_and_loss", isDummy: true));
                orchestrator.RegisterTest(new ProfitLossLongPositionTest(name: "profit_loss_long", isDummy: true));
                orchestrator.RegisterTest(new ProfitLossShortPositionTest(name: "profit_loss_short", isDummy: true));

                // Verifica se è richiesta l'analisi dei log dopo i test
                bool analyzeLogsAfterTests = args.Contains("--with-analysis") || args.Contains("-w");

                if (args.Length > 0 && !args[0].StartsWith("-"))
                {
                    // Esegui test specifico
                    var testName = args[0];
                    Console.WriteLine($"Esecuzione test specifico: {testName}");
                    var success = await orchestrator.RunTestAsync(testName);
                    Console.WriteLine($"\nRisultato test {testName}: {(success ? "SUCCESS" : "FAILED")}");
                }
                else
                {
                    // Esegui tutti i test
                    Console.WriteLine("Esecuzione di tutti i test");
                    var succes_list = await orchestrator.RunAllTestsAsync();
                }

                // Mostra i risultati dei test
                Console.WriteLine("\n=== RIEPILOGO RISULTATI ===");
                Console.WriteLine($"{"Test",-20} | {"Risultato",-10}");
                Console.WriteLine(new string('-', 33));

                var results = orchestrator.GetTestResults();
                foreach (var result in results)
                {
                    string status = result.Value ? "SUCCESS" : "FAILED";
                    Console.WriteLine($"{result.Key,-20} | {status,-10}");
                }

                // Analizza i log se richiesto
                if (analyzeLogsAfterTests)
                {
                    Console.WriteLine("\n=== ANALISI DEI LOG ===");
                    await AnalyzeLogsAsync(args);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Errore durante l'esecuzione dei test: {ex.Message}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Analizza i log di Kubernetes utilizzando OpenAI
        /// </summary>
        private static async Task AnalyzeLogsAsync(string[] args)
        {
            try
            {
                Console.WriteLine("Analisi dei log in corso...");

                // Estrai i parametri per l'analisi
                string deployment = GetArgValue(args, "--deployment", "-d", "cacheprovider");
                string ns = GetArgValue(args, "--namespace", "-n", "ot");
                int tailLines = int.TryParse(GetArgValue(args, "--lines", "-l", "1000"), out int lines) ? lines : 1000;
                int? sinceSeconds = int.TryParse(GetArgValue(args, "--since", "-s", "3600"), out int seconds) ? seconds : 3600;

                // Verifica se è richiesta l'analisi di più deployment
                bool multiDeployment = args.Contains("--multi") || args.Contains("-m");

                if (multiDeployment)
                {
                    // Analizza più deployment
                    var deployments = new List<string>();

                    // Cerca i deployment specificati
                    string deploymentsArg = GetArgValue(args, "--deployments", null, null);
                    if (!string.IsNullOrEmpty(deploymentsArg))
                    {
                        deployments.AddRange(deploymentsArg.Split(','));
                    }
                    else
                    {
                        // Deployment predefiniti se non specificati
                        deployments.Add("cacheprovider");
                        deployments.Add("api-gateway");
                        deployments.Add("auth-service");
                    }

                    Console.WriteLine($"Analisi dei log dai deployment: {string.Join(", ", deployments)}");

                    // Effettua il login ad Azure se necessario
                    await EnsureAzureLoginAsync();

                    // Analizza i log
                    var analyzer = new OpenAILogAnalyzer();
                    var analysis = await analyzer.AnalyzeMultipleKubernetesLogsAsync(
                        deployments,
                        ns,
                        null,
                        tailLines,
                        sinceSeconds);

                    Console.WriteLine("\nRisultato dell'analisi:");
                    Console.WriteLine(analysis);
                }
                else
                {
                    // Analizza un singolo deployment
                    Console.WriteLine($"Analisi dei log dal deployment: {deployment}");

                    // Effettua il login ad Azure se necessario
                    await EnsureAzureLoginAsync();

                    // Analizza i log
                    var analyzer = new OpenAILogAnalyzer();
                    var analysis = await analyzer.AnalyzeKubernetesLogsAsync(
                        deployment,
                        ns,
                        null,
                        tailLines,
                        sinceSeconds);

                    Console.WriteLine("\nRisultato dell'analisi:");
                    Console.WriteLine(analysis);
                }

                Console.WriteLine("\nAnalisi completata. I risultati sono stati salvati nella directory 'logs'.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Errore durante l'analisi dei log: {ex.Message}");
            }
        }

        /// <summary>
        /// Effettua il login ad Azure se necessario
        /// </summary>
        private static async Task EnsureAzureLoginAsync()
        {
            // Verifica se è richiesto di saltare l'autenticazione Azure
            if (Environment.GetCommandLineArgs().Contains("--no-azure") ||
                Environment.GetCommandLineArgs().Contains("-na"))
            {
                Console.WriteLine("Autenticazione Azure saltata (modalità --no-azure).");
                return;
            }

            Console.WriteLine("Verifica dell'autenticazione Azure...");
            var loginResult = await KubernetesLogFetcher.LoginToAzureAsync();
            if (loginResult.Success)
            {
                Console.WriteLine("Login ad Azure completato con successo.");
            }
            else
            {
                Console.WriteLine($"Errore durante il login ad Azure: {loginResult.Message}");
                Console.WriteLine();
                Console.WriteLine("SUGGERIMENTI:");
                Console.WriteLine("1. Verifica che Azure CLI (az) sia installato e nel PATH");
                Console.WriteLine("2. Esegui 'az login' manualmente da terminale");
                Console.WriteLine("3. Configura le credenziali in secrets.json (AZURE_TENANT_ID, AZURE_CLIENT_ID, AZURE_CLIENT_SECRET)");
                Console.WriteLine("4. Usa l'opzione --no-azure per saltare l'autenticazione e utilizzare dati di esempio");
                Console.WriteLine();
                throw new Exception("Impossibile autenticarsi ad Azure.");
            }
        }

        /// <summary>
        /// Ottiene il valore di un argomento dalla riga di comando
        /// </summary>
        private static string GetArgValue(string[] args, string longName, string shortName, string defaultValue)
        {
            for (int i = 0; i < args.Length - 1; i++)
            {
                if (args[i] == longName || args[i] == shortName)
                {
                    return args[i + 1];
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// Mostra l'help dell'applicazione
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("TestAutomations - Strumenti di test e analisi per OT");
            Console.WriteLine("=======================================================");
            Console.WriteLine();
            Console.WriteLine("UTILIZZO:");
            Console.WriteLine("  dotnet run -- [opzioni] [nome_test]");
            Console.WriteLine();

            Console.WriteLine("MODALITÀ DI ESECUZIONE:");
            Console.WriteLine("  Senza parametri         Esegue tutti i test");
            Console.WriteLine("  [nome_test]             Esegue solo il test specificato");
            Console.WriteLine("  --analyze-logs, -a      Esegue solo l'analisi dei log (senza test)");
            Console.WriteLine("  --with-analysis, -w     Esegue i test e poi analizza i log");
            Console.WriteLine("  --help, -h              Mostra questo help");
            Console.WriteLine();

            Console.WriteLine("PARAMETRI PER L'ANALISI DEI LOG:");
            Console.WriteLine("  --deployment, -d <nome>  Nome del deployment (default: cacheprovider)");
            Console.WriteLine("  --namespace, -n <nome>   Namespace Kubernetes (default: ot)");
            Console.WriteLine("  --lines, -l <numero>     Numero di righe da recuperare (default: 1000)");
            Console.WriteLine("  --since, -s <secondi>    Recupera log degli ultimi N secondi (default: 3600)");
            Console.WriteLine("  --multi, -m              Analizza più deployment invece di uno solo");
            Console.WriteLine("  --deployments <lista>    Lista di deployment separati da virgola (usato con --multi)");
            Console.WriteLine("  --no-azure, -na          Salta l'autenticazione Azure (utile se az non è disponibile)");
            Console.WriteLine();

            Console.WriteLine("ESEMPI:");
            Console.WriteLine("  dotnet run -- login                     Esegue solo il test di login");
            Console.WriteLine("  dotnet run -- --with-analysis           Esegue tutti i test e analizza i log alla fine");
            Console.WriteLine("  dotnet run -- -a -d api-gateway         Analizza i log del deployment api-gateway");
            Console.WriteLine("  dotnet run -- -a -s 1800                Analizza i log degli ultimi 30 minuti");
            Console.WriteLine("  dotnet run -- -a -m                     Analizza i log di più deployment");
            Console.WriteLine("  dotnet run -- -a -m --deployments cacheprovider,api-gateway");
            Console.WriteLine("                                          Analizza i log dei deployment specificati");
            Console.WriteLine();

            Console.WriteLine("TEST DISPONIBILI:");
            Console.WriteLine("  login                    Test di login");
            Console.WriteLine("  trading_flow             Test del flusso di trading");
            Console.WriteLine("  trading_flow_virtual     Test del flusso di trading con broker virtuale");
            Console.WriteLine("  trading_flow_update      Test dell'aggiornamento degli ordini");
            Console.WriteLine("  trading_flow_delete      Test della cancellazione degli ordini");
            Console.WriteLine("  alarm                    Test degli allarmi");
            Console.WriteLine("  preferiti                Test dei preferiti");
            Console.WriteLine("  preferiti_insert         Test dell'inserimento di preferiti");
            Console.WriteLine("  preferiti_modify         Test della modifica di preferiti");
            Console.WriteLine("  preferiti_delete         Test dell'eliminazione di preferiti");
            Console.WriteLine("  portafoglio              Test del portafoglio");
            Console.WriteLine("  portafoglio_new_position Test della creazione di nuove posizioni");
            Console.WriteLine("  portafoglio_close_position Test della chiusura di posizioni");
            Console.WriteLine("  portafoglio_update       Test dell'aggiornamento del portafoglio");
            Console.WriteLine("  profit_and_loss          Test di profitti e perdite");
            Console.WriteLine("  profit_loss_long         Test di profitti e perdite per posizioni long");
            Console.WriteLine("  profit_loss_short        Test di profitti e perdite per posizioni short");
        }
    }
}