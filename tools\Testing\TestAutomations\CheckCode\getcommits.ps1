param(
    [Parameter(Mandatory=$true)]
    [int]$Days
)

$DebugLog = "getcommits_debug.log"
$ReportFile = "getcommits_report.txt"

if (Test-Path $DebugLog) { Remove-Item $DebugLog }
if (Test-Path $ReportFile) { Remove-Item $ReportFile }

$BaseDirs = @("Libraries", "Services")

"[DEBUG] Directory corrente: $PWD" | Tee-Object -FilePath $DebugLog -Append
"[DEBUG] Analisi ultimi $Days giorni" | Tee-Object -FilePath $DebugLog -Append

foreach ($base in $BaseDirs) {
    "[DEBUG] Directory base: $base" | Tee-Object -FilePath $DebugLog -Append
    if (Test-Path $base) {
        Get-ChildItem -Path $base -Directory | ForEach-Object {
            $repo = $_.FullName
            "[DEBUG] Sottodirectory trovata: $repo" | Tee-Object -FilePath $DebugLog -Append
            if (Test-Path (Join-Path $repo ".git")) {
                "[DEBUG] Repository Git trovata: $repo" | Tee-Object -FilePath $DebugLog -Append
                "========== $($_.Name) ($repo) ==========" | Add-Content $ReportFile
                Push-Location $repo
                try {
                    git log --since="$Days days ago" --pretty=format:"%h %an %ad %s" --date=short --stat 2>>"$PSScriptRoot\$DebugLog" | Add-Content "$PSScriptRoot\$ReportFile"
                } catch {
                    "[DEBUG] ERRORE git log in $repo" | Tee-Object -FilePath "$PSScriptRoot\$DebugLog" -Append
                }
                Pop-Location
                Add-Content $ReportFile ""
            } else {
                "[DEBUG] Nessun repository Git in: $repo" | Tee-Object -FilePath $DebugLog -Append
            }
        }
    } else {
        "[DEBUG] ATTENZIONE: La directory base '$base' non e' stata trovata. Saltata." | Tee-Object -FilePath $DebugLog -Append
    }
}

"[DEBUG] FINE CICLO BASE" | Tee-Object -FilePath $DebugLog -Append 