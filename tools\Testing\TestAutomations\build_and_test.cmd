@echo off
echo ========================
echo Lettura configurazione secrets
echo ========================

REM Verifica se esiste il file secrets.json
if not exist "%~dp0secrets.json" (
    echo File secrets.json non trovato!
    echo Creare il file secrets.json con le configurazioni necessarie
    pause
    exit /b 1
)

REM Leggi il file secrets.json e imposta le variabili d'ambiente
powershell -Command "$secrets = Get-Content '%~dp0secrets.json' | ConvertFrom-Json; foreach ($property in $secrets.PSObject.Properties) { [Environment]::SetEnvironmentVariable($property.Name, $property.Value, 'Process') }"

echo ========================
echo Compilazione TestAutomations
echo ========================
cd /d "%~dp0"
dotnet build .
if %ERRORLEVEL% NEQ 0 (
    echo Errore nella compilazione!
    pause
    exit /b 1
)
echo.
echo ========================
echo Compilazione completata con successo!
echo ========================
echo.

REM Mostra le opzioni disponibili
echo Seleziona un'opzione:
echo.
echo  1. Eseguire tutti i test
echo  2. Eseguire un test specifico
echo  3. Analizzare solo i log (senza test)
echo  4. Eseguire i test e poi analizzare i log
echo  5. Mostrare l'help completo
echo  6. Uscire
echo.
set /p option=Seleziona un'opzione (1-6)
echo.

REM Utilizziamo CHOICE per evitare problemi di interpretazione
if "%option%"=="1" goto option1
if "%option%"=="2" goto option2
if "%option%"=="3" goto option3
if "%option%"=="4" goto option4
if "%option%"=="5" goto option5
if "%option%"=="6" goto option6
goto invalid_option

:option1
echo Esecuzione di tutti i test...
dotnet run --project TestAutomations.csproj
goto end

:option2
echo.
echo TEST DISPONIBILI:
echo  login                    Test di login
echo  trading_flow             Test del flusso di trading
echo  trading_flow_virtual     Test del flusso di trading con broker virtuale
echo  trading_flow_update      Test dell'aggiornamento degli ordini
echo  trading_flow_delete      Test della cancellazione degli ordini
echo  alarm                    Test degli allarmi
echo  preferiti                Test dei preferiti
echo  preferiti_insert         Test dell'inserimento di preferiti
echo  preferiti_modify         Test della modifica di preferiti
echo  preferiti_delete         Test dell'eliminazione di preferiti
echo  portafoglio              Test del portafoglio
echo  portafoglio_new_position Test della creazione di nuove posizioni
echo  portafoglio_close_position Test della chiusura di posizioni
echo  portafoglio_update       Test dell'aggiornamento del portafoglio
echo  profit_and_loss          Test di profitti e perdite
echo  profit_loss_long         Test di profitti e perdite per posizioni long
echo  profit_loss_short        Test di profitti e perdite per posizioni short
echo.
set /p test_name=Inserisci il nome del test da eseguire
if not "%test_name%"=="" (
    echo Esecuzione del test: %test_name%
    dotnet run --project TestAutomations.csproj -- %test_name%
)
goto end

:option3
echo.
echo OPZIONI PER L'ANALISI DEI LOG:
echo  1. Analizzare un singolo deployment
echo  2. Analizzare più deployment
echo.
set /p log_option=Seleziona un'opzione (1-2)

if "%log_option%"=="1" goto log_option1
if "%log_option%"=="2" goto log_option2
goto end

:log_option1
set /p deployment=Deployment da analizzare (default: cacheprovider)
set /p namespace=Namespace Kubernetes (default: ot)
set /p lines=Numero di righe da recuperare (default: 1000)
set /p seconds=Recupera log degli ultimi N secondi (default: 3600)
set /p no_azure=Saltare l'autenticazione Azure? (s/n, default: n)

set cmd=dotnet run --project TestAutomations.csproj -- --analyze-logs
if not "%deployment%"=="" set cmd=%cmd% --deployment %deployment%
if not "%namespace%"=="" set cmd=%cmd% --namespace %namespace%
if not "%lines%"=="" set cmd=%cmd% --lines %lines%
if not "%seconds%"=="" set cmd=%cmd% --since %seconds%
if /i "%no_azure%"=="s" set cmd=%cmd% --no-azure

echo Esecuzione comando: %cmd%
%cmd%
goto end

:log_option2
set /p deployments=Lista di deployment separati da virgola (default: cacheprovider,api-gateway,auth-service)
set /p namespace=Namespace Kubernetes (default: ot)
set /p lines=Numero di righe da recuperare (default: 1000)
set /p seconds=Recupera log degli ultimi N secondi (default: 3600)
set /p no_azure=Saltare l'autenticazione Azure? (s/n, default: n)

set cmd=dotnet run --project TestAutomations.csproj -- --analyze-logs --multi
if not "%deployments%"=="" set cmd=%cmd% --deployments %deployments%
if not "%namespace%"=="" set cmd=%cmd% --namespace %namespace%
if not "%lines%"=="" set cmd=%cmd% --lines %lines%
if not "%seconds%"=="" set cmd=%cmd% --since %seconds%
if /i "%no_azure%"=="s" set cmd=%cmd% --no-azure

echo Esecuzione comando: %cmd%
%cmd%
goto end

:option4
echo.
set /p test_name=Inserisci il nome del test da eseguire (lascia vuoto per tutti i test)

set cmd=dotnet run --project TestAutomations.csproj -- --with-analysis
if not "%test_name%"=="" set cmd=%cmd% %test_name%

echo Esecuzione comando: %cmd%
%cmd%
goto end

:option5
echo Mostrando l'help completo...
dotnet run --project TestAutomations.csproj -- --help
goto end

:option6
echo Uscita...
goto end

:invalid_option
echo Opzione non valida. Riprova.
goto end

:end
pause