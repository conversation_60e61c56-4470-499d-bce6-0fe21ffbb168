using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using TestAutomations.Tests;
using TestAutomations.Utils;

namespace TestAutomations.Common
{
    public class TestOrchestrator
    {
        private readonly Dictionary<string, TestBase> _tests = new Dictionary<string, TestBase>();
        private readonly Dictionary<string, bool> _results = new Dictionary<string, bool>();
        private static readonly Logger Logger = Logger.GetInstance();

        public void RegisterTest(TestBase test)
        {
            if (test == null) throw new ArgumentNullException(nameof(test));
            _tests[test.Name] = test;
            Logger.Info($"Test '{test.Name}' registrato nell'orchestrator");
        }

        public async Task<Dictionary<string, bool>> RunAllTestsAsync()
        {
            foreach (var test in _tests.Values)
            {
                var result = await RunTestAsync(test);
                _results[test.Name] = result;
            }
            return _results;
        }

        public async Task<bool> RunTestAsync(string testName)
        {
            if (!_tests.TryGetValue(testName, out var test))
            {
                Logger.Error($"Test '{testName}' non trovato");
                return false;
            }

            return await RunTestAsync(test);
        }

        private async Task<bool> RunTestAsync(TestBase test)
        {
            Logger.Info($"Avvio test '{test.Name}' (Modalità login: {(test.IsDummy ? "Dummy" : "Normale")})...");
            
            try
            {
                var result = await test.ExecuteAsync();
                
                if (result)
                {
                    Logger.Info($"Test '{test.Name}' completato con successo");
                }
                else
                {
                    Logger.Error($"Test '{test.Name}' fallito");
                }
                
                _results[test.Name] = result;
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante l'esecuzione del test '{test.Name}': {ex.Message}");
                Logger.Error(ex.StackTrace);
                _results[test.Name] = false;
                return false;
            }
        }
        
        public Dictionary<string, bool> GetTestResults()
        {
            return new Dictionary<string, bool>(_results);
        }
    }
} 