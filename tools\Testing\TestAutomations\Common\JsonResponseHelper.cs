using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Linq;

namespace TestAutomations.Common
{
    /// <summary>
    /// Classe di utilità per gestire le risposte JSON dalle API OT
    /// </summary>
    public static class JsonResponseHelper
    {
        /// <summary>
        /// Estrae l'ID dell'ordine da una risposta JSON
        /// </summary>
        public static string TryExtractOrderId(JsonElement response)
        {
            return ExtractId(response, "OrderID", "orderId", "orderID", "order_id");
        }

        /// <summary>
        /// Verifica se la conferma di un ordine è andata a buon fine controllando solo se OrderID è valorizzato
        /// </summary>
        public static bool IsConfirmationSuccessful(JsonElement response)
        {
            // Verifica se la conferma è andata a buon fine controllando solo se OrderID è valorizzato
            if (response.TryGetProperty("OrderID", out var orderIdElem) &&
                orderIdElem.ValueKind == JsonValueKind.String &&
                !string.IsNullOrEmpty(orderIdElem.GetString()))
                return true;

            // Cerca anche in un oggetto data se presente
            if (response.TryGetProperty("data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Object)
            {
                if (dataElem.TryGetProperty("OrderID", out var dataOrderId) &&
                    dataOrderId.ValueKind == JsonValueKind.String &&
                    !string.IsNullOrEmpty(dataOrderId.GetString()))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Verifica se un ordine è stato completato (in qualsiasi stato finale)
        /// Basato sulla struttura definita nello swagger: Data[i].Status (OrderStatus enum)
        /// </summary>
        public static bool IsOrderCompleted(JsonElement? statusResponse)
        {
            if (statusResponse == null)
                return false;

            // Struttura API OT: Data[i].Status (come definito nello swagger)
            if (statusResponse.Value.TryGetProperty("Data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Array)
            {
                foreach (var orderData in dataElem.EnumerateArray())
                {
                    if (orderData.TryGetProperty("Tag", out var ordertags) && ordertags.TryGetProperty("Status", out var statusElem))
                    {
                        // Status è un intero secondo l'enum OrderStatus nello swagger
                        if (statusElem.ValueKind == JsonValueKind.Number)
                        {
                            int status = statusElem.GetInt32();
                            // Stati finali secondo OrderStatus enum: Accepted = 2, Rejected = 4, PartialExecuted = 32, Executed = 64, NotExecuted = 128
                            return status == 2 || status == 4 || status == 16 || status == 32 || status == 64 || status == 128;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Verifica se un ordine è stato eseguito (stato "executed")
        /// Basato sulla struttura definita nello swagger: Data[i].Status (OrderStatus enum)
        /// 0 = Non eseguito, 1 = Parzialmente eseguito, 2 = Eseguito
        /// </summary>
        public static int IsOrderExecuted(JsonElement? statusResponse)
        {
            if (statusResponse == null)
                return 0;

            // Struttura API OT: Data[i].Status (come definito nello swagger)
            if (statusResponse.Value.TryGetProperty("Data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Array)
            {
                foreach (var orderData in dataElem.EnumerateArray())
                {
                    if (orderData.TryGetProperty("Tag", out var orderTag) && orderTag.TryGetProperty("Status", out var statusElem))
                    {
                        // Status è un intero secondo l'enum OrderStatus nello swagger
                        if (statusElem.ValueKind == JsonValueKind.Number)
                        {
                            int status = statusElem.GetInt32();
                            // Executed = 64 secondo OrderStatus enum
                            // PartialExecuted = 32 ??
                            return status switch
                            {
                                32 => 1,
                                64 => 2,
                                _ => 0,
                            };
                        }
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// Estrae la quantità eseguita da una risposta JSON di stato dell'ordine
        /// </summary>
        /// <param name="statusResponse"></param>
        /// <returns></returns>
        public static int GetExecutedQuantity(JsonElement? statusResponse)
        {
            if (statusResponse == null)
                return 0;

            // Struttura API OT: Data[i].Status (come definito nello swagger)
            if (statusResponse.Value.TryGetProperty("Data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Array)
            {
                foreach (var orderData in dataElem.EnumerateArray())
                {
                    if (orderData.TryGetProperty("Values", out var orderTag) && orderTag.TryGetProperty("ORDER_EXECUTED_QUANTITY", out var statusElem))
                    {
                        // Status è un intero secondo l'enum OrderStatus nello swagger
                        if (statusElem.ValueKind == JsonValueKind.Number)
                        {
                            int quantity = statusElem.GetInt32();
                            return quantity;
                        }
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// Estrae gli errori da una risposta JSON
        /// </summary>
        public static string ExtractErrorsFromResponse(JsonElement response)
        {
            if (response.TryGetProperty("Errors", out var errorsElem) &&
                errorsElem.ValueKind == JsonValueKind.Array)
            {
                var errors = new List<string>();
                foreach (var error in errorsElem.EnumerateArray())
                {
                    errors.Add(error.ToString());
                }
                return string.Join("; ", errors);
            }
            return response.ToString();
        }

        /// <summary>
        /// Verifica se la cancellazione di un ordine è andata a buon fine
        /// </summary>
        public static bool IsDeleteSuccessful(JsonElement response)
        {
            // Verifica se la cancellazione è andata a buon fine
            if (response.TryGetProperty("success", out var successElem) &&
                successElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("isSuccess", out var isSuccessElem) &&
                isSuccessElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("status", out var statusElem) &&
                statusElem.ValueKind == JsonValueKind.String &&
                (statusElem.GetString().ToLower() == "ok" || statusElem.GetString().ToLower() == "deleted"))
                return true;

            if (response.TryGetProperty("resultStatus", out var resultStatusElem) &&
                resultStatusElem.ValueKind == JsonValueKind.String &&
                (resultStatusElem.GetString().ToLower() == "ready" || resultStatusElem.GetString().ToLower() == "deleted"))
                return true;

            return false;
        }

        /// <summary>
        /// Verifica se l'aggiornamento di un ordine è andato a buon fine
        /// </summary>
        public static bool IsUpdateSuccessful(JsonElement response)
        {
            // Verifica se l'aggiornamento è andato a buon fine
            if (response.TryGetProperty("success", out var successElem) &&
                successElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("isSuccess", out var isSuccessElem) &&
                isSuccessElem.ValueKind == JsonValueKind.True)
                return true;

            if (response.TryGetProperty("status", out var statusElem) &&
                statusElem.ValueKind == JsonValueKind.String &&
                statusElem.GetString().ToLower() == "ok")
                return true;

            if (response.TryGetProperty("resultStatus", out var resultStatusElem) &&
                resultStatusElem.ValueKind == JsonValueKind.String &&
                resultStatusElem.GetString().ToLower() == "ready")
                return true;

            return false;
        }

        /// <summary>
        /// Estrae un valore di stringa da una proprietà JSON
        /// </summary>
        public static string ExtractStringProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var prop) &&
                prop.ValueKind == JsonValueKind.String)
            {
                return prop.GetString();
            }
            return null;
        }

        /// <summary>
        /// Estrae un valore numerico da una proprietà JSON
        /// </summary>
        public static decimal? ExtractDecimalProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var prop) &&
                prop.ValueKind == JsonValueKind.Number)
            {
                return prop.GetDecimal();
            }
            return null;
        }

        /// <summary>
        /// Estrae un valore booleano da una proprietà JSON
        /// </summary>
        public static bool? ExtractBooleanProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var prop) &&
                (prop.ValueKind == JsonValueKind.True || prop.ValueKind == JsonValueKind.False))
            {
                return prop.GetBoolean();
            }
            return null;
        }

        /// <summary>
        /// Estrae un array JSON da una proprietà
        /// </summary>
        public static List<JsonElement> ExtractArrayProperty(JsonElement element, string propertyName)
        {
            var result = new List<JsonElement>();

            if (element.TryGetProperty(propertyName, out var arrayProp) &&
                arrayProp.ValueKind == JsonValueKind.Array)
            {
                foreach (var item in arrayProp.EnumerateArray())
                {
                    result.Add(item);
                }
            }

            return result;
        }

        /// <summary>
        /// Cerca un elemento in un array JSON in base a una proprietà e un valore
        /// </summary>
        public static JsonElement? FindElementInArray(JsonElement element, string arrayPropertyName,
                                                    string searchPropertyName, string searchValue)
        {
            if (element.TryGetProperty(arrayPropertyName, out var arrayProp) &&
                arrayProp.ValueKind == JsonValueKind.Array)
            {
                foreach (var item in arrayProp.EnumerateArray())
                {
                    if (item.TryGetProperty(searchPropertyName, out var prop) &&
                        prop.ValueKind == JsonValueKind.String &&
                        prop.GetString() == searchValue)
                    {
                        return item;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Verifica se un elemento esiste in un array JSON in base a una proprietà e un valore
        /// </summary>
        public static bool ExistsInArray(JsonElement element, string arrayPropertyName,
                                        string searchPropertyName, string searchValue)
        {
            return FindElementInArray(element, arrayPropertyName, searchPropertyName, searchValue) != null;
        }

        /// <summary>
        /// Estrae un ID generico da una risposta JSON cercando in vari campi possibili
        /// </summary>
        public static string ExtractId(JsonElement response, params string[] possibleIdFields)
        {
            foreach (var field in possibleIdFields)
            {
                if (response.TryGetProperty(field, out var idElem) &&
                    idElem.ValueKind == JsonValueKind.String)
                {
                    return idElem.GetString();
                }
            }

            // Cerca in un oggetto data
            if (response.TryGetProperty("data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Object)
            {
                foreach (var field in possibleIdFields)
                {
                    if (dataElem.TryGetProperty(field, out var idElem) &&
                        idElem.ValueKind == JsonValueKind.String)
                    {
                        return idElem.GetString();
                    }
                }
            }

            return null;
        }
    }
}
