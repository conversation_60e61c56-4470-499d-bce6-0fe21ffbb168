﻿using MagicOnionCallsTester.Helpers;
using MagicOnionCallsTester.Models.Customer;
using Microsoft.Extensions.Logging;
using OT.Common.Broker.Grpc;
using OT.Common.Broker.Grpc.V2;
using OT.Common.Broker.Models;
using OT.Common.Broker.Models.Auth;
using OT.Common.Broker.Models.Balance;
using OT.Common.Broker.Models.Orders;
using OT.Common.Broker.Models.Orders.Parameters;
using OT.Common.Broker.Models.Portfolio.Parameters;
using OT.Common.Broker.Models.Properties;
using OT.Common.Broker.Virtual.Grpc;
using OT.Common.Broker.Virtual.Models.VirtualPortfolio.Parameters;
using OT.Common.Models;
using OT.Common.Models.Stocks;
using System.Text.Json;

namespace MagicOnionCallsTester.Cases
{
    public class VirtualBrokerCalls : BaseCalls
    {
        private readonly MagicOnionClient<IBrokerAuthService> _authService;
        private readonly MagicOnionClient<IBrokerBalanceService> _balanceService;
        private readonly MagicOnionClient<IBrokerServiceV2> _brokerService;
        private readonly MagicOnionClient<IGamingService> _gamingService;
        private readonly MagicOnionClient<IVirtualPortfolioService> _vpService;

        private readonly BrokerCustomerCredentials _customerCredentials;
        private readonly ILogger _logger;

        public VirtualBrokerCalls(
            BrokerCustomerCredentials brokerCustomer,
            ILogger logger
            ) : base()
        {
            _customerCredentials = brokerCustomer;
            _logger = logger;

            _brokerService = new MagicOnionClient<IBrokerServiceV2>("localhost", 6002);
            _authService = new MagicOnionClient<IBrokerAuthService>("localhost", 6002);
            _balanceService = new MagicOnionClient<IBrokerBalanceService>("localhost", 6002);
            _gamingService = new MagicOnionClient<IGamingService>("localhost", 6002);
            _vpService = new MagicOnionClient<IVirtualPortfolioService>("localhost", 6002);
        }

        public VirtualBrokerCalls Test_Generic()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Generic] Starting");

                // NON COMMITTARE

                // ...
                //_logger.LogInformation("[Generic] Result: {r}", );

                _logger.LogInformation("[Generic] Done");
            });
            return this;
        }

        public VirtualBrokerCalls Login()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Login] Starting");
                await _authService.Client.Login(new AuthParams()
                {
                    OTAccessToken = _customerCredentials.OTAccessToken,
                    User = _customerCredentials.Username,
                    CustomerId = _customerCredentials.CustomerId,
                }).ResponseAsync;
                _logger.LogInformation("[Login] Done");
            });

            return this;
        }

        public VirtualBrokerCalls GetVirtualPortfolioList()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("Starting GetVirtualPortfolioList");
                await _vpService.Client.GetVirtualPortfolioList(new VirtualPortfolioParameters()
                {
                    Broker = BrokerName.VirtualBroker,
                    CustomerId = _customerCredentials.CustomerId,
                }).ResponseAsync;
                _logger.LogInformation("Done GetVirtualPortfolioList");
            });

            return this;
        }

        public VirtualBrokerCalls GetPortfolio(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[GetPortfolio] Starting");

                PortfolioFilter portfolioFilter;
                if (!string.IsNullOrEmpty(json))
                {
                    portfolioFilter = JsonSerializer.Deserialize<PortfolioFilter>(json)!;
                }
                else
                {
                    portfolioFilter = new PortfolioFilter()
                    {
                        // TODO
                    };
                }

                portfolioFilter.BrokerName = BrokerName.VirtualBroker;
                portfolioFilter.AccessToken = _customerCredentials.OTAccessToken;
                portfolioFilter.CustomerID = _customerCredentials.CustomerId;

                var result = await _brokerService.Client.GetPortfolio(portfolioFilter).ResponseAsync;


                _logger.LogInformation("[GetPortfolio] Done");
            });

            return this;
        }

        public VirtualBrokerCalls UpsertVirtualPortfolioPositions()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[UpsertVirtualPortfolioPositions] Starting");
                await _vpService.Client.UpsertVirtualPortfolioPositions(new VirtualPortfolioParameters()
                {
                    Broker = BrokerName.VirtualBroker,
                    Id = 1,
                    Type = PortfolioType.VirtualPortfolio,
                    CustomerId = _customerCredentials.CustomerId,
                    PortfolioDetails = new List<VirtualPortfolioDetailParameters>()
                    {
                        new VirtualPortfolioDetailParameters()
                        {
                            MarketCode = "MTA",
                            StockCode = "BGN",
                            Quantity = 1,
                            Price = 9.09m,
                            Currency = "EUR",
                            LevarageQuantity = 100,
                        }
                    }
                }).ResponseAsync;
                _logger.LogInformation("[UpsertVirtualPortfolioPositions] Done");
            });

            return this;
        }


        public VirtualBrokerCalls GetInfoPermissions()
        {
            //_startingTask = _startingTask.ContinueWith(t =>
            //{
            //    _logger.LogInformation("Starting GetInfoPermissions");
            //    _brokerService.Client.GetInfoPermissions("MANUALTEST", 0).ResponseAsync.Wait();
            //    _logger.LogInformation("Done GetInfoPermissions");
            //});

            return this;
        }

        public VirtualBrokerCalls InsertOrder(string json)
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("[InsertOrder] Starting");

                InsertOrderParameters par;
                if (!string.IsNullOrEmpty(json))
                {
                    par = JsonSerializer.Deserialize<InsertOrderParameters>(json)!;
                }
                else
                {
                    par = new InsertOrderParameters()
                    {
                        BrokerName = BrokerName.VirtualBroker,
                        MarketCode = "MTA",
                        StockCode = "A2A",
                        OrderType = OrderType.Buy,
                        Price = 2,
                        Quantity = 10,
                        ValidityDate = DateTime.Today,
                        EvaluationMode = EvaluationMode.Book,
                        OrderFast = false,
                        BrokerProperties = new BrokerBaseProperties()
                        {

                        },
                        Phase = OrderPhase.NegoziazioneContinua,
                        ManualOrder = false,
                        BestExecution = false,
                        BatchOrder = false,
                        ParkingOrder = false,
                        LeverageOrder = false,
                        StopPrice = null,
                        IcebergOrderQty = null,
                        OrderParameter = OrderParameter.NO_PARAMETER,
                        GamingId = 0,
                        PriceType = PriceTypes.L,
                        StrategyEvaluationMode = null,
                        StrategyConditionType = StrategyConditionTypes.GreaterOrEqual,
                        TakeOrderLimitPrice = null,
                        TakeOrderStopPrice = null,
                        StopOrderLimitPrice = null,
                        StopOrderStopPrice = null,
                        TakeStopOrderActive = false,
                        MainOrderTick = null,
                        TakeOrderTick = null,
                        StopOrderTick = null,
                        IsMargin = false,
                        StockType = null,
                        OperationPurpose = null,
                        ClientInfo = new ClientInfo()
                        {

                        },
                    };
                }

                par.CustomerID = _customerCredentials.CustomerId;
                par.AccessToken = _customerCredentials.OTAccessToken;

                var res = _brokerService.Client.InsertOrder(par).ResponseAsync.Result;
                _logger.LogInformation("[InsertOrder] Done");

                if (res.IsSuccesful())
                {
                    _logger.LogInformation("[ConfirmOrder] Starting");
                    _brokerService.Client.ConfirmInsertOrder(new ConfirmOrderParameters()
                    {
                        BrokerName = BrokerName.VirtualBroker,
                        AccessToken = _customerCredentials.OTAccessToken,
                        CustomerID = _customerCredentials.CustomerId,
                        OrderID = res.OrderID
                    }).ResponseAsync.Wait();

                    _logger.LogInformation("[ConfirmOrder] Done");
                }
            });

            return this;
        }

        public VirtualBrokerCalls GetOrderStatus()
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting OrderStatus");
                _brokerService.Client.GetOrderStatus(new OrderStatusFilter()
                {
                    AccessToken = "MANUALTEST",
                    AccountFilter = new AccountFilter(),
                    StockFilter = new StockFilter(),
                    ShowBuyOrders = true,
                    ShowSellOrders = true,
                    ShowLeverageOrders = false,
                    ShowSpecialOrders = false,
                    OnlyMyOrders = false,
                    OrderId = null,
                    OrderStatus = null,
                    //OrderTypology = Orders.OrderTypology.Standard,
                    FromDate = DateTime.Today,
                    ToDate = DateTime.Today,
                    OnlyOrdersWithExe = false,
                    RefreshCache = true,
                    BrokerName = BrokerName.VirtualBroker,
                    CustomerID = 1031,
                    GamingId = 0,
                    //OrderShowType = Filter.OrderShowType.ShowAll,
                })
                .ResponseAsync.Wait();
                _logger.LogInformation("Done OrderStatus");
            });

            return this;
        }

        public VirtualBrokerCalls GetOrderDetail(string orderId)
        {
            _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetOrderDetail with {j}", orderId);

                OrderDetailFilter filter = new OrderDetailFilter()
                {
                    AccessToken = "MANUALTEST",
                    BrokerName = BrokerName.VirtualBroker,
                    CustomerID = 1031,
                    OrderCode = orderId,
                };

                var result = _brokerService.Client.GetOrderDetail(filter).ResponseAsync.Result;

                if (result != null)
                {
                    _logger.LogInformation($"GetOrderDetail result: \n{result.GetFlowData(false).ToString()}");
                }
                else
                {
                    _logger.LogInformation($"GetOrderDetail result null");
                }

                _logger.LogInformation("Done GetOrderDetail");
            });

            return this;
        }

        public VirtualBrokerCalls SetLiquidity()
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _balanceService.Client.SetLiquidity(new LiquidityParam()
                {
                    AccessToken = "MANUALTEST",
                    BrokerName = BrokerName.VirtualBroker,
                    CustomerId = 1031,
                    GamingId = 0,
                    LiquidityValue = 10011
                })
                .ResponseAsync.Wait();
            });

            return this;
        }

        public VirtualBrokerCalls GetAllGames()
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetAllGames");
                //var games = gamingService.Client.GetAllMyGames(new Gaming.GameRegistrationParameters()
                //{
                //    BrokerName = OT.Hello.Base.BrokerName.VirtualBroker,
                //    CustomerId = 1031,
                //    GamingId = 0,
                //})
                //.ResponseAsync.Result;

                // NEW
                //var games = gamingService.Client.GetContests(null, null, null, true)
                //.ResponseAsync.Result;

                //_logger.LogInformation("Done GetAllGames; Games: {g}", string.Join(" ", games.Select(i => i.GamingId.ToString())));
            });

            return this;
        }

        public VirtualBrokerCalls RegisterToGame(int game)
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting RegisterToGame");
                //var games = gamingService.Client.RegisterToGame(new Gaming.GameRegistrationParameters()
                //{
                //    BrokerName = OT.Hello.Base.BrokerName.VirtualBroker,
                //    CustomerId = 1031,
                //    GamingId = game,
                //})
                //.ResponseAsync.Result;

                // NEW
                //var games = gamingService.Client.RegisterToGame(1031, game)
                //.ResponseAsync.Result;

                //_logger.LogInformation("Done RegisterToGame; game: {g}", game);
            });

            return this;
        }

        public VirtualBrokerCalls GetSubscribedGames()
        {
            _taskChain = _taskChain.ContinueWith(t =>
            {
                _logger.LogInformation("Starting GetSubscribedGames");
                //var games = gamingService.Client.GetAllMyGames(new Gaming.GameRegistrationParameters()
                //{
                //    BrokerName = OT.Hello.Base.BrokerName.VirtualBroker,
                //    CustomerId = 1031,
                //    GamingId = 0,
                //})
                //.ResponseAsync.Result.Where(i => i.RegistrationDate != null);

                // NEW
                //var games = gamingService.Client.GetContests(1031, null, null, null)
                //.ResponseAsync.Result.Where(i => i.RegistrationDate != null);

                //_logger.LogInformation("Done GetSubscribedGames; Games: {g}", string.Join(" ", games.Select(i => i.GamingId.ToString())));
            });

            return this;
        }

        #region StressTest

        public static void GetOrderStatusS()
        {
            MagicOnionClient<IBrokerService> brokerService = new MagicOnionClient<IBrokerService>("virtualbroker", 6002);
            brokerService.Client.GetOrderStatus(new OrderStatusFilter()
            {
                AccessToken = "MANUALTEST",
                AccountFilter = new AccountFilter(),
                StockFilter = new StockFilter(),
                ShowBuyOrders = true,
                ShowSellOrders = true,
                ShowLeverageOrders = false,
                ShowSpecialOrders = false,
                OnlyMyOrders = false,
                OrderId = null,
                OrderStatus = null,
                //OrderTypology = Orders.OrderTypology.Standard,
                FromDate = DateTime.Today,
                ToDate = DateTime.Today,
                OnlyOrdersWithExe = false,
                RefreshCache = true,
                BrokerName = BrokerName.VirtualBroker,
                CustomerID = 1031,
                GamingId = 0,
                //OrderShowType = Filter.OrderShowType.ShowAll,
            })
            .ResponseAsync.Wait();
        }
        #endregion

        public static async Task GetPortfolio()
        {
            MagicOnionClient<IBrokerService> vbClient = new MagicOnionClient<IBrokerService>("virtualbroker", 6002);

            var portfolio = await vbClient.Client.GetPortfolio(new PortfolioFilter()
            {
                BrokerName = BrokerName.VirtualBroker,
                CustomerID = 1038,

            });

            _ = 0;
        }

        public static async Task Account()
        {
            MagicOnionClient<IBrokerService> vbClient = new MagicOnionClient<IBrokerService>("virtualbroker", 6002);

            CancellationTokenSource cts = new CancellationTokenSource();
            var portfolio = vbClient.Client
                .WithCancellationToken(cts.Token)
                .GetAccountProperties("VB");
            await Task.Delay(100);
            //cts.Cancel();
            var r = await portfolio;

            _ = 0;
        }

        public static async Task GetLiquidity()
        {
            MagicOnionClient<IBrokerBalanceService> balanceService = new MagicOnionClient<IBrokerBalanceService>("virtualbroker", 6002);

            var res = await balanceService.Client.GetBalance(new BalanceParam()
            {
                AccessToken = "VB",
                BrokerName = BrokerName.VirtualBroker,
                CustomerId = 1031
            });

            _ = 0;
        }
    }
}
