# Piano Operativo

## Epic: Test Automation

### Feature: TEST automatico per login
- [Product Backlog Item] login tradizionale
- [Product Backlog Item] Login dummy

### Feature: TEST automatico per Operatività
- [Product Backlog Item] operatività SellaTrader Inserimento
- [Product Backlog Item] Operatività SellaTrader Modifica
- [Product Backlog Item] Operatività SellaTrader Cancellazione

### Feature: Test automatico allarmi
- [Product Backlog Item] Inserimento allarme

### Feature: Test automatico gestione Preferiti
- [Product Backlog Item] Inserimento nuovo stock all'interno di lista preferiti
- [Product Backlog Item] Cambio ordine lista preferiti
- [Product Backlog Item] cancellazione titolo lista preferiti

### Feature: Test automatico Portafoglio
- [Product Backlog Item] Aggiornamento valorizzazione portafoglio
- [Product Backlog Item] Aggiornamento nuova posizione portafoglio
- [Product Backlog Item] Aggiornamento chiusura posizione portafoglio

### Feature: Test automatico profitti e perdite
- [Product Backlog Item] valorizzazione profitti e perdite

### Feature: test automatico commit pre rilascio
- [Product Backlog Item] analisi dei micro-servizi impattati pre rilascio

## Sequenza Consigliata di Esecuzione
1. TEST automatico per login => LoginTest.cs
2. TEST automatico per Operatività SELLA => TradingFlowTest.cs
3. TEST  automatico per Operatività VIRTUAL => TradingFlowVirtualTest.cs
4. TEST automatico allarmi => AlarmsTest.cs
5. TEST automatico gestione Preferiti => PreferitiTest.cs
6. TEST automatico Portafoglio => PortafoglioTest.cs
7. TEST automatico profitti e perdite => ProfitLossTest.cs
8. TEST automatico commit pre rilascio => /CheckCode/getcommits.bat & analyze_impact.py

## Dipendenze tra Task
- I test automatici per Operatività, allarmi, gestione Preferiti, Portafoglio, profitti e perdite, e commit pre rilascio dipendono dal completamento del TEST automatico per login.

## Rischi e Note Operative
- Mancanza di assegnatari per alcuni work item potrebbe ritardare l'esecuzione.
- Il mancato completamento del TEST automatico per login potrebbe bloccare l'avanzamento degli altri test automatici.
- È importante garantire che tutti i test automatici siano eseguiti correttamente prima del rilascio per prevenire errori e problemi post-rilascio.