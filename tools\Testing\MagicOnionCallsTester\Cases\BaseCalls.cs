﻿namespace MagicOnionCallsTester.Cases
{
    public class BaseCalls
    {
        protected Task _taskChain;
        protected readonly TaskCompletionSource _startTrigger;

        public BaseCalls()
        {
            _taskChain = Task.CompletedTask;
            _startTrigger = new TaskCompletionSource();
        }

        protected void ChainTask(Func<Task> step)
        {
            _taskChain = _taskChain
                .ContinueWith(async previous =>
                {
                    await previous;
                    await _startTrigger.Task;
                    await step();
                })
                .Unwrap();
        }

        public async Task Start()
        {
            _startTrigger.SetResult();
            await _taskChain;
        }
    }
}
