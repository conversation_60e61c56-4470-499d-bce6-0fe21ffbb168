using System;

namespace TestAutomations.Common
{
    public class ApiException : Exception
    {
        // Codice di stato HTTP
        public int StatusCode { get; }
        
        // Endpoint che ha generato l'errore
        public string Endpoint { get; }
        
        // Risposta JSON o messaggio di errore dettagliato
        public string ResponseContent { get; }
        
        // Costruttore base
        public ApiException(string message, int statusCode, string endpoint, string responseContent)
            : base(message)
        {
            StatusCode = statusCode;
            Endpoint = endpoint;
            ResponseContent = responseContent;
        }
        
        // Costruttore con eccezione interna
        public ApiException(string message, int statusCode, string endpoint, string responseContent, Exception innerException)
            : base(message, innerException)
        {
            StatusCode = statusCode;
            Endpoint = endpoint;
            ResponseContent = responseContent;
        }
        
        // Override ToString per includere tutti i dettagli
        public override string ToString()
        {
            return $"ApiException: {Message}\nStatusCode: {StatusCode}\nEndpoint: {Endpoint}\nResponse: {ResponseContent}";
        }
    }
} 