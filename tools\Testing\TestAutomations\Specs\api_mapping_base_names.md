# Report Analisi API in XTradingBroker (Nomi Base)

Questa analisi cerca i nomi base delle pagine ASPX/ASHX (senza percorso o estensione) nel codice XTradingBroker.

## Sommario

- Nomi base cercati: 674
- Nomi base trovati nel codice: 33

## Riferimenti Trovati

### Nome: `OrderStatus` (50 occorrenze)

- **File**: `Services\xtradingbroker\CacheCommon\CacheCommonManager.cs`
  - **Righe**: 126
  - **Snippet**:
    - `#region OrderStatus`

- **File**: `Services\xtradingbroker\Entities\EnumParser.cs`
  - **Righe**: 8, 10, 16, 19, 22, 25, 28, 31, 35, 38, 41, 44
  - **Snippet**:
    - `public static OrderStatus EvaluateXtradingStatus(string xtradingOrderStatus)`
    - `OrderStatus status;`
    - `status = OrderStatus.Wait;`
    - ... e altri 9 snippet

- **File**: `Services\xtradingbroker\Entities\Deserializers\Converters.cs`
  - **Righe**: 12, 14, 18, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 38
  - **Snippet**:
    - `public class XTOrderStatusConverter : JsonConverter<OrderStatus>`
    - `public override OrderStatus Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)`
    - `return OrderStatus.None;  // ?`
    - ... e altri 13 snippet

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\OrderDetailDeserializer.cs`
  - **Righe**: 51, 384, 389, 392, 412
  - **Snippet**:
    - `public OrderStatus V_STATUS { set => Status = value; }`
    - `if (base.Status == OrderStatus.Wait)`
    - `base.Status = OrderStatus.Parked;`
    - ... e altri 2 snippet

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\OrderStatusDeserializer.cs`
  - **Righe**: 46, 417, 422, 425, 445
  - **Snippet**:
    - `public OrderStatus V_STATUS { set => Status = value; }`
    - `if (base.Status == OrderStatus.Wait)`
    - `base.Status = OrderStatus.Parked;`
    - ... e altri 2 snippet

- **File**: `Services\xtradingbroker\Push\Parsers\OrderParser.cs`
  - **Righe**: 57, 69, 113
  - **Snippet**:
    - `OrderStatus orderStatus = OrderStatus.None;`
    - `if (orderStatus == OrderStatus.Wait)`
    - `OrderStatus orderStatus = OrderStatus.None;`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 1827, 1832, 1836, 1884
  - **Snippet**:
    - `if (order.Status == OrderStatus.Wait)`
    - `order.Status = OrderStatus.Parked;`
    - `order.Status = OrderStatus.Batch;`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 1866, 1871, 1875, 1923
  - **Snippet**:
    - `if (order.Status == OrderStatus.Wait)`
    - `order.Status = OrderStatus.Parked;`
    - `order.Status = OrderStatus.Batch;`
    - ... e altri 1 snippet

### Nome: `Balance` (45 occorrenze)

- **File**: `Services\xtradingbroker\XtradingBrokerCore.cs`
  - **Righe**: 7
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\Extensions\BalanceAccountExtensions.cs`
  - **Righe**: 1
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\Extensions\BalanceContentExtensions.cs`
  - **Righe**: 1
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\Extensions\IServiceCollectionExtensions.cs`
  - **Righe**: 4
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\Logic\BalanceManager.cs`
  - **Righe**: 3, 8, 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`
    - `using OT.Service.XTradingBroker.Network.Models.Deserializers.Balance;`
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Balance;`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 7, 688
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`
    - `#region Balance`

- **File**: `Services\xtradingbroker\Network\Client\BalanceClient.cs`
  - **Righe**: 6, 7, 80
  - **Snippet**:
    - `using OT.Service.XTradingBroker.Network.Models.Deserializers.Balance;`
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Balance;`
    - `RequestUri = new Uri("Balance/getBalanceDetails", UriKind.Relative)`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\BalanceDeserializer.cs`
  - **Righe**: 1, 11
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `public List<BalanceItemDeserializer> Balance { get; set; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\ShortLeverageInfoDeserializer.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\AccountLiquidityDetailsDeserializer.cs`
  - **Righe**: 1, 7
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\BalanceDetailDeserializer.cs`
  - **Righe**: 1, 6
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\MarginiShortMultidayDeserializer.cs`
  - **Righe**: 1, 8
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\ReservedAmountDeserializer.cs`
  - **Righe**: 1, 7
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\TitoliDestinatiDeserializer.cs`
  - **Righe**: 1, 7
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\AccountLiquidityDetailsRequest.cs`
  - **Righe**: 1, 3
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\BalanceRequest.cs`
  - **Righe**: 1, 3
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\MarginiShortMultidayRequest.cs`
  - **Righe**: 1, 3
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\ReservedAmountRequest.cs`
  - **Righe**: 2, 4
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\SetLiquidityRequest.cs`
  - **Righe**: 1, 4
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Balance\TitoliDestinatiRequest.cs`
  - **Righe**: 1, 3
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Balance;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Balance`

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 16
  - **Snippet**:
    - `Balance = 37,`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 476
  - **Snippet**:
    - `ChangeSubscribe(SupportedPushServices.Balance, Constants.CONTENT_ACTIVATION_PARAMETER);`

- **File**: `Services\xtradingbroker\Push\Parsers\BalanceParser.cs`
  - **Righe**: 3
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`

- **File**: `Services\xtradingbroker\PushSender\PushSenderManager.cs`
  - **Righe**: 6, 743
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`
    - `case SupportedPushServices.Balance:`

- **File**: `Services\xtradingbroker\Repositories\BalanceRepository.cs`
  - **Righe**: 4, 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`
    - `using OT.Service.XTradingBroker.Network.Models.Deserializers.Balance;`

- **File**: `Services\xtradingbroker\Services\BalanceCenter.cs`
  - **Righe**: 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.Balance;`

### Nome: `OrderDetail` (41 occorrenze)

- **File**: `Services\xtradingbroker\DataLayer\Interface\IOperationsDataLayer.cs`
  - **Righe**: 17, 25, 36, 44
  - **Snippet**:
    - `Task<OrderDetail> GetOrderDetail(BrokerCustomerContext context, OrderDetailFilter filter);`
    - `Task<OrderDetail> GetOrderDetail(OperationContext ctx, OrderDetailFilter filter);`
    - `public virtual Task<OrderDetail> GetOrderDetail(OperationContext ctx, OrderDetailFilter filter)`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\DataLayer\Layers\Cache\OperationsCache.cs`
  - **Righe**: 125, 322, 324, 326, 327, 340
  - **Snippet**:
    - `public async Task<OrderDetail> GetOrderDetail(BrokerCustomerContext context, OrderDetailFilter filter)`
    - `public override async Task<OrderDetail> GetOrderDetail(OperationContext ctx, OrderDetailFilter filter)`
    - `OrderDetail result = null;`
    - ... e altri 3 snippet

- **File**: `Services\xtradingbroker\DataLayer\Layers\WebApi\OperationsClient.cs`
  - **Righe**: 141, 146
  - **Snippet**:
    - `public async Task<OrderDetail> GetOrderDetail(BrokerCustomerContext context, OrderDetailFilter filter)`
    - `OrderDetail orderDetail = await _xtradingClientFactory.Create()`

- **File**: `Services\xtradingbroker\Entities\OrderParam.cs`
  - **Righe**: 207, 257, 284
  - **Snippet**:
    - `public OrderParkedParam(OrderDetail order, bool toBatch, Market market, ClientInfo clientInfo)`
    - `public OrderParkedBatchRequest(OrderDetail order, Market market, ClientInfo clientInfo) : base(clientInfo)`
    - `public OrderBatchParam(OrderDetail order, Market market, ClientInfo clientInfo)`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 265
  - **Snippet**:
    - `public async Task<OrderDetail> GetOrderDetail_New(OrderDetailFilter orderDetailFilter, string accessToken, ClientInfo clientInfo)`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\OrderDetailDeserializer.cs`
  - **Righe**: 17, 27, 31, 32, 37
  - **Snippet**:
    - `public OrderDetailItemDeserializer OrderDetail { get; set; }`
    - `public OrderDetail OrderDetailFull`
    - `OrderDetail.OrderResultDetails = OrderTrace.Cast<OrderResultDetail>().ToList();`
    - ... e altri 2 snippet

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 237, 240
  - **Snippet**:
    - `public async Task<OrderDetail> GetOrderDetail(OrderDetailFilter filter)`
    - `OrderDetail orderDetail = await _operationsDataLayer.GetOrderDetail(context, filter);`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 344, 766, 768, 783, 790, 1910, 1911, 1916, 1922
  - **Snippet**:
    - `OrderDetail detail = await GetOrderDetail(new OrderDetailFilter()`
    - `private async Task<OrderDetail> GetOrderDetail(OrderDetailFilter filter, BrokerCustomerEntity customerEntity)`
    - `OrderDetail orderDetail = null;`
    - ... e altri 6 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 345, 768, 770, 785, 792, 1949, 1950, 1955, 1961
  - **Snippet**:
    - `OrderDetail detail = await GetOrderDetail(new OrderDetailFilter()`
    - `private async Task<OrderDetail> GetOrderDetail(OrderDetailFilter filter, BrokerCustomerEntity customerEntity)`
    - `OrderDetail orderDetail = null;`
    - ... e altri 6 snippet

### Nome: `Portfolio` (39 occorrenze)

- **File**: `Services\xtradingbroker\XtradingBrokerCore.cs`
  - **Righe**: 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`

- **File**: `Services\xtradingbroker\CacheCommon\CacheCommonManager.cs`
  - **Righe**: 7, 276
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `#region Portfolio`

- **File**: `Services\xtradingbroker\DataLayer\Interface\IOperationsDataLayer.cs`
  - **Righe**: 3, 4
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\DataLayer\Layers\Cache\OperationsCache.cs`
  - **Righe**: 6, 7
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\DataLayer\Layers\WebApi\OperationsClient.cs`
  - **Righe**: 5, 6, 292
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`
    - `_logger.LogWarning("Portfolio push: received CustomerCode but BondAccountId, CashAccoundId and DossierId were null.");`

- **File**: `Services\xtradingbroker\Entities\Deserializers\ShortLiquidityAmountRequiredDeserializer.cs`
  - **Righe**: 1
  - **Snippet**:
    - `﻿using OT.Common.Broker.Models.Portfolio;`

- **File**: `Services\xtradingbroker\Extensions\IServiceCollectionExtensions.cs`
  - **Righe**: 7, 35
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `AddSpecificBroker<ServiceBusPushMessage>(services, configuration, "XtradingPushExternal-Portfolio", false);`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 573
  - **Snippet**:
    - `#region Portfolio/ProfitLoss`

- **File**: `Services\xtradingbroker\Network\Models\GetShortLiquidityAmountRequiredRequest.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\Network\Models\NormalizePositionRequest.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\Network\Models\TransformSiToSmRequest.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\PortfolioDeserializer.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 15
  - **Snippet**:
    - `Portfolio = 34,`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 473
  - **Snippet**:
    - `ChangeSubscribe(SupportedPushServices.Portfolio, Constants.CONTENT_ACTIVATION_PARAMETER);`

- **File**: `Services\xtradingbroker\Push\Parsers\PortfolioParser.cs`
  - **Righe**: 5, 178
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `//        _logger.LogWarning("Portfolio push: received CustomerCode but BondAccountId, CashAccoundId and DossierId were null.");`

- **File**: `Services\xtradingbroker\PushSender\PushSenderManager.cs`
  - **Righe**: 8, 700
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `case SupportedPushServices.Portfolio:`

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 12, 13, 287, 432
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`
    - `#region Portfolio`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 20, 21, 2099, 2121
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`
    - `// Portfolio`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 20, 21, 2138, 2160
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`
    - `// Portfolio`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Utils\FilterHelper.cs`
  - **Righe**: 5, 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`
    - `using OT.Common.Broker.Models.Portfolio.Parameters;`

- **File**: `Services\xtradingbroker\Utils\PortfolioValorizer.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Portfolio;`

### Nome: `Messages` (33 occorrenze)

- **File**: `Services\xtradingbroker\Logic\InfoManager.cs`
  - **Righe**: 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Logic\MessageManager.cs`
  - **Righe**: 7, 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Messages;`

- **File**: `Services\xtradingbroker\Logic\UserConfigurationManager.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 13, 786, 1114
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`
    - `#region Messages`
    - `Messages = messages.ToArray(),`

- **File**: `Services\xtradingbroker\Network\Client\InfoClient.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Network\Client\MessageClient.cs`
  - **Righe**: 7
  - **Snippet**:
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Messages;`

- **File**: `Services\xtradingbroker\Network\Client\UserConfigurationClient.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Messages\PullMessageDeserializer.cs`
  - **Righe**: 7
  - **Snippet**:
    - `namespace OT.Service.XTradingBroker.Network.Models.Deserializers.Messages`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Messages\AlertsRequest.cs`
  - **Righe**: 4
  - **Snippet**:
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Messages`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Messages\MessageRequest.cs`
  - **Righe**: 2, 7, 64, 68
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Messages`
    - `public int[] Messages { get; set; }`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 13
  - **Snippet**:
    - `Messages = 32,`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 475
  - **Snippet**:
    - `ChangeSubscribe(SupportedPushServices.Messages, Constants.CONTENT_ACTIVATION_PARAMETER);`

- **File**: `Services\xtradingbroker\PushSender\PushSenderManager.cs`
  - **Righe**: 435, 676, 729, 802
  - **Snippet**:
    - `string msg = _pushNotificationsConfig.Orders.Messages[((int)order.Status).ToString()];`
    - `string msg = _pushNotificationsConfig.Orders.Messages[((int)order.Status).ToString()];`
    - `case SupportedPushServices.Messages:`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Repositories\ConfigurationRepository.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Repositories\InfoRepository.cs`
  - **Righe**: 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Repositories\MessageRepository.cs`
  - **Righe**: 7
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\ConfigurationCenter.cs`
  - **Righe**: 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\InfoCenter.cs`
  - **Righe**: 8
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\MessageCenter.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\V2\ConfigurationCenterV2.cs`
  - **Righe**: 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\V2\InfoCenterV2.cs`
  - **Righe**: 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\V2\MessageCenterV2.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Messages.Notices;`

### Nome: `ProfitLoss` (27 occorrenze)

- **File**: `Services\xtradingbroker\XtradingBrokerCore.cs`
  - **Righe**: 10
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`

- **File**: `Services\xtradingbroker\CacheCommon\CacheCommonManager.cs`
  - **Righe**: 8, 361
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `#region ProfitLoss`

- **File**: `Services\xtradingbroker\DataLayer\Interface\IOperationsDataLayer.cs`
  - **Righe**: 5, 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

- **File**: `Services\xtradingbroker\DataLayer\Layers\Cache\OperationsCache.cs`
  - **Righe**: 8, 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

- **File**: `Services\xtradingbroker\DataLayer\Layers\WebApi\OperationsClient.cs`
  - **Righe**: 7, 8
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

- **File**: `Services\xtradingbroker\Entities\ProfitLossEntries.cs`
  - **Righe**: 2
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`

- **File**: `Services\xtradingbroker\Extensions\IServiceCollectionExtensions.cs`
  - **Righe**: 8
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 573
  - **Snippet**:
    - `#region Portfolio/ProfitLoss`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\AccountLiquidityDetailsDeserializer.cs`
  - **Righe**: 74
  - **Snippet**:
    - `public decimal NProfitLoss { set => base.ProfitLoss = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\BalanceDetailDeserializer.cs`
  - **Righe**: 20, 27, 39
  - **Snippet**:
    - `public List<BalanceDetailItemDeserializer> ProfitLoss { get; set; }`
    - `ProfitLoss = new List<BalanceDetailItemDeserializer>();`
    - `["ProfitLoss"] = ProfitLoss.Cast<BalanceDetail>().ToList(),`

- **File**: `Services\xtradingbroker\Push\Parsers\PortfolioParser.cs`
  - **Righe**: 6
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`

- **File**: `Services\xtradingbroker\PushSender\PushSenderManager.cs`
  - **Righe**: 9
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 14, 15, 449
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`
    - `#region ProfitLoss`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 22, 23
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 22, 23
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

- **File**: `Services\xtradingbroker\Utils\FilterHelper.cs`
  - **Righe**: 7, 8
  - **Snippet**:
    - `using OT.Common.Broker.Models.ProfitLoss;`
    - `using OT.Common.Broker.Models.ProfitLoss.Parameters;`

### Nome: `Description` (20 occorrenze)

- **File**: `Services\xtradingbroker\XtradingBrokerCore.cs`
  - **Righe**: 214, 231, 258
  - **Snippet**:
    - `{ "Description", "A new XTrading Broker ResetStatistics job was executed" },`
    - `{ "Description", "A new XTrading Broker ResetPushConnections job was executed" },`
    - `{ "Description", "A new XTrading Broker FlushStatistics job was executed" },`

- **File**: `Services\xtradingbroker\Entities\Deserializers\LendingDeseraizlier.cs`
  - **Righe**: 204
  - **Snippet**:
    - `public string V_DESCRIPTION { set => base.Description = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\PersonalListDeserializer.cs`
  - **Righe**: 24, 25, 42, 43
  - **Snippet**:
    - `[JsonPropertyName("Description")]`
    - `public string Description { set => base.Name = value; }`
    - `[JsonPropertyName("Description")]`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\PortfolioDeserializer.cs`
  - **Righe**: 60, 309
  - **Snippet**:
    - `public string V_PRODUCT_DESCRIPTION { set => Description = value; }`
    - `public string V_PRODUCT_DESCRIPTION { set => Description = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\ProfStatementDeserializer.cs`
  - **Righe**: 15
  - **Snippet**:
    - `public string NewDescription { set => base.Description = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\MarginiShortMultidayDeserializer.cs`
  - **Righe**: 122
  - **Snippet**:
    - `public string VDescription { set => base.Description = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\ReservedAmountDeserializer.cs`
  - **Righe**: 92
  - **Snippet**:
    - `public new string Description { set => base.Description = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Messages\MessageRequest.cs`
  - **Righe**: 47
  - **Snippet**:
    - `SearchType.Description => "DESC",`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 648, 660
  - **Snippet**:
    - `{ "Description", "PushSession Reset job was executed" },`
    - `{ "Description", "A new PushSession Check job was executed" },`

- **File**: `Services\xtradingbroker\Push\Parsers\PortfolioParser.cs`
  - **Righe**: 81
  - **Snippet**:
    - `Description = content["V_PRODUCT_DESCRIPTION"],`

- **File**: `Services\xtradingbroker\PushSender\PushSenderManager.cs`
  - **Righe**: 233
  - **Snippet**:
    - `{ "Description", "A new ResetStatistics job was executed" },`

- **File**: `Services\xtradingbroker\Utils\PortfolioValorizer.cs`
  - **Righe**: 179, 211
  - **Snippet**:
    - `public string Description { get; set; }`
    - `Description = prod.Stock.Description;`

### Nome: `Login` (18 occorrenze)

- **File**: `Services\xtradingbroker\Logic\UserManager.cs`
  - **Righe**: 53, 58, 127
  - **Snippet**:
    - `return await _apiClient.Login(string.Empty, body);`
    - `return await _apiClient.Login(string.Empty, body);`
    - `_logger.LogError(ex, "Customer {CustomerId} Login Finalize fault", brokerCustomer);`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 104
  - **Snippet**:
    - `#region Login`

- **File**: `Services\xtradingbroker\Network\Client\UserClient.cs`
  - **Righe**: 39
  - **Snippet**:
    - `public async Task<LoginResponseDeserializer> Login(string ids, LoginRequest body)`

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 48
  - **Snippet**:
    - `Login = 0,`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 101, 326, 368, 417, 519, 684
  - **Snippet**:
    - `pushSession.Login();`
    - `public void Login()`
    - `throw new ApplicationException("Login denied. Reason login denied by server peer");`
    - ... e altri 3 snippet

- **File**: `Services\xtradingbroker\Repositories\AuthRepository.cs`
  - **Righe**: 73, 118
  - **Snippet**:
    - `public async Task<AuthResponse> Login(AuthParams authParams)`
    - `//_logger.LogWarning("Login: {error}, \nResponse: {resp}", step2result.Error, );`

- **File**: `Services\xtradingbroker\Services\AuthCenter.cs`
  - **Righe**: 58, 60
  - **Snippet**:
    - `public async UnaryResult<AuthResponse> Login(AuthParams authParams)`
    - `return await _authRepository.Login(authParams);`

- **File**: `Services\xtradingbroker\Services\V2\AuthCenterV2.cs`
  - **Righe**: 59, 61
  - **Snippet**:
    - `public async UnaryResult<AuthResponse> Login(BrokerName broker, AuthParams authParams)`
    - `return await _authRepository.Login(authParams);`

### Nome: `UserProfile` (17 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 1387, 1389, 1541, 1548, 1561, 1574, 1587, 1600, 1620
  - **Snippet**:
    - `HttpResponseMessage response = await _client.PostAsync("UserProfile/SetDefaultCashAcct", content);`
    - `_metricsSender.AddSample("ot_xtbroker_api_time", new string[] { "UserProfile/SetDefaultCashAcct" }, sw.ElapsedMilliseconds);`
    - `#region UserProfile`
    - ... e altri 6 snippet

- **File**: `Services\xtradingbroker\Network\Client\InfoClient.cs`
  - **Righe**: 159
  - **Snippet**:
    - `RequestUri = new Uri("UserProfile/EseguitiContoTrader", UriKind.Relative)`

- **File**: `Services\xtradingbroker\Network\Client\UserConfigurationClient.cs`
  - **Righe**: 100, 115, 130, 145, 160, 178
  - **Snippet**:
    - `RequestUri = new Uri("UserProfile/GetProfStatement", UriKind.Relative)`
    - `RequestUri = new Uri("UserProfile/SaveProfStatement", UriKind.Relative)`
    - `RequestUri = new Uri("UserProfile/SetMailNotification", UriKind.Relative)`
    - ... e altri 3 snippet

- **File**: `Services\xtradingbroker\Repositories\ConfigurationRepository.cs`
  - **Righe**: 84
  - **Snippet**:
    - `#region UserProfile`

### Nome: `Commissions` (14 occorrenze)

- **File**: `Services\xtradingbroker\Logic\InfoManager.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Network\Client\InfoClient.cs`
  - **Righe**: 4
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\OrderDetailDeserializer.cs`
  - **Righe**: 453
  - **Snippet**:
    - `public decimal? N_COMMISSION { set => base.Commissions = value; }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\BalanceDetailDeserializer.cs`
  - **Righe**: 14, 25, 37
  - **Snippet**:
    - `public List<BalanceDetailItemDeserializer> Commissions { get; set; }`
    - `Commissions = new List<BalanceDetailItemDeserializer>();`
    - `["Commissions"] = Commissions.Cast<BalanceDetail>().ToList(),`

- **File**: `Services\xtradingbroker\Repositories\InfoRepository.cs`
  - **Righe**: 5
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Services\InfoCenter.cs`
  - **Righe**: 7
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 11, 2024
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`
    - `rd.Commissions = dataTable.GetDecimalDT(row, "N_COMMISSION");`

- **File**: `Services\xtradingbroker\Services\V2\InfoCenterV2.cs`
  - **Righe**: 8
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 11, 2063
  - **Snippet**:
    - `using OT.Common.Broker.Models.Info.Commissions;`
    - `rd.Commissions = dataTable.GetDecimalDT(row, "N_COMMISSION");`

### Nome: `Logout` (14 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 205
  - **Snippet**:
    - `//Logout?`

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 49
  - **Snippet**:
    - `Logout = 1,`

- **File**: `Services\xtradingbroker\Push\PushSession.cs`
  - **Righe**: 304, 377, 388, 546, 643
  - **Snippet**:
    - `Logout();`
    - `public void Logout()`
    - `SupportedMessagesContentTypes.Logout,`
    - ... e altri 2 snippet

- **File**: `Services\xtradingbroker\Repositories\AuthRepository.cs`
  - **Righe**: 182
  - **Snippet**:
    - `public async Task<string> Logout(string accessToken)`

- **File**: `Services\xtradingbroker\Services\AuthCenter.cs`
  - **Righe**: 76, 80, 85
  - **Snippet**:
    - `public async UnaryResult<LogoutResult> Logout(string accessToken)`
    - `string customer = await _authRepository.Logout(accessToken);`
    - `_logger.LogError(ex, "Logout error: {t}", accessToken);`

- **File**: `Services\xtradingbroker\Services\V2\AuthCenterV2.cs`
  - **Righe**: 77, 81, 86
  - **Snippet**:
    - `public async UnaryResult<LogoutResult> Logout(BrokerName broker, int customerId, string accessToken)`
    - `string customer = await _authRepository.Logout(accessToken);`
    - `_logger.LogError(ex, "Logout error: {t}", accessToken);`

### Nome: `DeleteStrategy` (13 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 498
  - **Snippet**:
    - `public async Task<DeleteStrategyResponse> DeleteStrategy(string accessToken, StrategyStatusRequest body)`

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 265, 277
  - **Snippet**:
    - `public async Task<bool> DeleteStrategy(BrokerCustomerEntity customerEntity, StrategyStatusFilter filter)`
    - `DeleteStrategyResponse res = await XtradingClientFactory.Create().DeleteStrategy(customerEntity.Ids, msg);`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 701, 711, 712, 718, 719
  - **Snippet**:
    - `public async UnaryResult<DeleteStrategyResult> DeleteStrategy(StrategyStatusFilter par)`
    - `DeleteStrategyResult result = await _operationsRepository.DeleteStrategy(customerEntity, par);`
    - `_livenessProbe.Push_OK("DeleteStrategy");`
    - ... e altri 2 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 703, 713, 714, 720, 721
  - **Snippet**:
    - `public async UnaryResult<DeleteStrategyResult> DeleteStrategy(StrategyStatusFilter par)`
    - `DeleteStrategyResult result = await _operationsRepository.DeleteStrategy(customerEntity, par);`
    - `_livenessProbe.Push_OK("DeleteStrategy");`
    - ... e altri 2 snippet

### Nome: `ProfStatement` (10 occorrenze)

- **File**: `Services\xtradingbroker\Logic\UserConfigurationManager.cs`
  - **Righe**: 64
  - **Snippet**:
    - `public async Task<ProfStatement> GetProfStatement(GetProfStatementParameter param, BrokerCustomerEntity customerEntity)`

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 1543
  - **Snippet**:
    - `public async Task<ProfStatement> GetProfStatement(string accessToken, GetProfStatementRequest body)`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\ProfStatementDeserializer.cs`
  - **Righe**: 6
  - **Snippet**:
    - `public class ProfStatementDeserializer : ProfStatement`

- **File**: `Services\xtradingbroker\Repositories\ConfigurationRepository.cs`
  - **Righe**: 86
  - **Snippet**:
    - `public async Task<ProfStatement> GetProfStatement(GetProfStatementParameter param)`

- **File**: `Services\xtradingbroker\Services\ConfigurationCenter.cs`
  - **Righe**: 119, 126, 135
  - **Snippet**:
    - `public async UnaryResult<BrokerResult<ProfStatement>> GetProfStatement(GetProfStatementParameter param)`
    - `BrokerResult<ProfStatement> result = await _configurationRepository.GetProfStatement(param);`
    - `return new BrokerResult<ProfStatement>(BrokerName.Sella, "", ex);  // TODO: fix`

- **File**: `Services\xtradingbroker\Services\V2\ConfigurationCenterV2.cs`
  - **Righe**: 117, 124, 133
  - **Snippet**:
    - `public async UnaryResult<BrokerResult<ProfStatement>> GetProfStatement(GetProfStatementParameter param)`
    - `BrokerResult<ProfStatement> result = await _configurationRepository.GetProfStatement(param);`
    - `return new BrokerResult<ProfStatement>(BrokerName.Sella, "", ex);  // TODO: fix`

### Nome: `InsertStrategy` (9 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 539
  - **Snippet**:
    - `public async Task<DataSet> InsertStrategy(string accessToken, StrategyParam param, bool confirm = false)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 220, 323, 478, 492
  - **Snippet**:
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam);`
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam);`
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam, true);`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 221, 324, 479, 493
  - **Snippet**:
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam);`
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam);`
    - `DataSet output = await _xtradingClientFactory.Create().InsertStrategy(brokerCustomer.Ids, strategyParam, true);`
    - ... e altri 1 snippet

### Nome: `OptionCalculator` (9 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 1130
  - **Snippet**:
    - `RequestUri = new Uri("OptionCalculator/GetOptionCalculator", UriKind.Relative)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 1682, 1694, 1698, 1699
  - **Snippet**:
    - `public async UnaryResult<OptionsCalculatorResponse> OptionCalculator(OptionsCalculatorParameters param)`
    - `_livenessProbe.Push_OK("OptionCalculator");`
    - `_logger.LogError(ex, "OptionCalculator({AccessToken})", param.AccessToken);`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 1721, 1733, 1737, 1738
  - **Snippet**:
    - `public async UnaryResult<OptionsCalculatorResponse> OptionCalculator(OptionsCalculatorParameters param)`
    - `_livenessProbe.Push_OK("OptionCalculator");`
    - `_logger.LogError(ex, "OptionCalculator({AccessToken})", param.AccessToken);`
    - ... e altri 1 snippet

### Nome: `CommissionProfile` (8 occorrenze)

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 1987, 1992, 1997, 2002
  - **Snippet**:
    - `commissionDetail.Profiles.Add(new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.Trading, row.GetProfilCalcType("V_TYPE_CODE"), tradingVal.Value, commissionDetailTable.GetDecimalDT(row, "N_MIN_VAL"), commissionDetailTable.GetDecimalDT(row, "N_MAX_VAL")));`
    - `new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.FixedCosts, row.GetProfilCalcType("V_TYPE_SPESE_FISSE"), fixCosts.Value));`
    - `new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.BrokerFees, row.GetProfilCalcType("V_TYPE_SPESE_BROKER"), brokerFeesVal.Value, commissionDetailTable.GetDecimalDT(row, "N_MIN_SPESE_BROKER"), commissionDetailTable.GetDecimalDT(row, "N_MAX_SPESE_BROKER")));`
    - ... e altri 1 snippet

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 2026, 2031, 2036, 2041
  - **Snippet**:
    - `commissionDetail.Profiles.Add(new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.Trading, row.GetProfilCalcType("V_TYPE_CODE"), tradingVal.Value, commissionDetailTable.GetDecimalDT(row, "N_MIN_VAL"), commissionDetailTable.GetDecimalDT(row, "N_MAX_VAL")));`
    - `new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.FixedCosts, row.GetProfilCalcType("V_TYPE_SPESE_FISSE"), fixCosts.Value));`
    - `new Common.Broker.Models.Orders.Detail.CommissionProfile(CommissionProfileType.BrokerFees, row.GetProfilCalcType("V_TYPE_SPESE_BROKER"), brokerFeesVal.Value, commissionDetailTable.GetDecimalDT(row, "N_MIN_SPESE_BROKER"), commissionDetailTable.GetDecimalDT(row, "N_MAX_SPESE_BROKER")));`
    - ... e altri 1 snippet

### Nome: `TradeGroups` (8 occorrenze)

- **File**: `Services\xtradingbroker\Repositories\AuthRepository.cs`
  - **Righe**: 416
  - **Snippet**:
    - `foreach (var tradeGroup in service.TradeGroups)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 990, 1038, 1050
  - **Snippet**:
    - `tradePermission.TradeGroups = await CacheManager.GetTradeAbilsAsync(brokerCustomer.BrokerCustomer);`
    - `var tradeGroups = shortEnabledGroup.TradeGroups?.FirstOrDefault(i => i.TradeGroupId.ToString() == tradeGroupId);`
    - `var tradeGroups = leverageEnabledGroup.TradeGroups?.FirstOrDefault(i => i.TradeGroupId.ToString() == tradeGroupId);`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 1011, 1077, 1089
  - **Snippet**:
    - `tradePermission.TradeGroups = await CacheManager.GetTradeAbilsAsync(brokerCustomer.BrokerCustomer);`
    - `var tradeGroups = shortEnabledGroup.TradeGroups?.FirstOrDefault(i => i.TradeGroupId.ToString() == tradeGroupId);`
    - `var tradeGroups = leverageEnabledGroup.TradeGroups?.FirstOrDefault(i => i.TradeGroupId.ToString() == tradeGroupId);`

- **File**: `Services\xtradingbroker\Utils\CacheHelper.cs`
  - **Righe**: 157
  - **Snippet**:
    - `//                foreach (JToken tradeGroup in service["TradeGroups"])`

### Nome: `DeleteOrder` (7 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 465
  - **Snippet**:
    - `public async Task<TradingResponse> DeleteOrder(string accessToken, DeleteOrderParam orderParam, bool isBatch = false)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 633, 644, 693
  - **Snippet**:
    - `public async UnaryResult<BrokerOperationResult> DeleteOrder(DeleteOrderParameters par)`
    - `TradingResponse output = await _xtradingClientFactory.Create().DeleteOrder(brokerCustomer.Ids, orderParam, !string.IsNullOrEmpty(orderParam.BatchType));`
    - `TradingResponse output = await _xtradingClientFactory.Create().DeleteOrder(brokerCustomer.Ids, orderParam);`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 635, 646, 695
  - **Snippet**:
    - `public async UnaryResult<BrokerOperationResult> DeleteOrder(DeleteOrderParameters par)`
    - `TradingResponse output = await _xtradingClientFactory.Create().DeleteOrder(brokerCustomer.Ids, orderParam, !string.IsNullOrEmpty(orderParam.BatchType));`
    - `TradingResponse output = await _xtradingClientFactory.Create().DeleteOrder(brokerCustomer.Ids, orderParam);`

### Nome: `InsertOrder` (7 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 294
  - **Snippet**:
    - `public async Task<TradingResponse> InsertOrder(string accessToken, OrderParam orderParam)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 181, 408, 540
  - **Snippet**:
    - `public async UnaryResult<BrokerOperationResult> InsertOrder(InsertOrderParameters par)`
    - `TradingResponse output = await _xtradingClientFactory.Create().InsertOrder(brokerCustomer.Ids, orderParam);`
    - `TradingResponse output = await _xtradingClientFactory.Create().InsertOrder(brokerCustomer.Ids, orderParam);`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 182, 409, 542
  - **Snippet**:
    - `public async UnaryResult<BrokerOperationResult> InsertOrder(InsertOrderParameters par)`
    - `TradingResponse output = await _xtradingClientFactory.Create().InsertOrder(brokerCustomer.Ids, orderParam);`
    - `TradingResponse output = await _xtradingClientFactory.Create().InsertOrder(brokerCustomer.Ids, orderParam);`

### Nome: `LendingFees` (7 occorrenze)

- **File**: `Services\xtradingbroker\Entities\Deserializers\LendingDeseraizlier.cs`
  - **Righe**: 17
  - **Snippet**:
    - `public class LendingFeesItemDeserializer : LendingFees`

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 572, 584
  - **Snippet**:
    - `public async Task<List<LendingFees>> GetLendingFees(BrokerContext context, LendingFeesParameters param)`
    - `return output.Table.Cast<LendingFees>().ToList();`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 1431, 1435
  - **Snippet**:
    - `public async UnaryResult<BrokerResult<List<LendingFees>>> GetLendingFees(LendingFeesParameters param)`
    - `BrokerResult<List<LendingFees>> result;`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 1470, 1474
  - **Snippet**:
    - `public async UnaryResult<BrokerResult<List<LendingFees>>> GetLendingFees(LendingFeesParameters param)`
    - `BrokerResult<List<LendingFees>> result;`

### Nome: `LendingSearch` (5 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 1473
  - **Snippet**:
    - `public async Task<LendingSearchDeserializer> LendingSearch(string accessToken, LendingSearchRequest request)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 1482, 1495
  - **Snippet**:
    - `public async UnaryResult<LendingSearchResult> LendingSearch(LendingSearchParameters param)`
    - `object output = await _xtradingClientFactory.Create().LendingSearch(brokerCustomer.Ids, req);`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 1521, 1534
  - **Snippet**:
    - `public async UnaryResult<LendingSearchResult> LendingSearch(LendingSearchParameters param)`
    - `object output = await _xtradingClientFactory.Create().LendingSearch(brokerCustomer.Ids, req);`

### Nome: `ModifyOrder` (5 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 434
  - **Snippet**:
    - `public async Task<TradingResponse> ModifyOrder(string accessToken, ModifyOrderParam orderParam, bool isParked = false)`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 582, 624
  - **Snippet**:
    - `TradingResponse output = await _xtradingClientFactory.Create().ModifyOrder(brokerCustomer.Ids, orderParam);`
    - `output = await _xtradingClientFactory.Create().ModifyOrder(brokerCustomer.Ids, orderParam);`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 584, 626
  - **Snippet**:
    - `TradingResponse output = await _xtradingClientFactory.Create().ModifyOrder(brokerCustomer.Ids, orderParam);`
    - `output = await _xtradingClientFactory.Create().ModifyOrder(brokerCustomer.Ids, orderParam);`

### Nome: `Chart` (4 occorrenze)

- **File**: `Services\xtradingbroker\Logic\UserConfigurationManager.cs`
  - **Righe**: 13
  - **Snippet**:
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Chart;`

- **File**: `Services\xtradingbroker\Network\Client\UserConfigurationClient.cs`
  - **Righe**: 12
  - **Snippet**:
    - `using OT.Service.XTradingBroker.Network.Models.Requests.Chart;`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Chart\GetLayoutRequest.cs`
  - **Righe**: 4
  - **Snippet**:
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Chart`

- **File**: `Services\xtradingbroker\Network\Models\Requests\Chart\WebChartStudiesRequest.cs`
  - **Righe**: 3
  - **Snippet**:
    - `namespace OT.Service.XTradingBroker.Network.Models.Requests.Chart`

### Nome: `Filters` (4 occorrenze)

- **File**: `Services\xtradingbroker\Repositories\OperationsRepository.cs`
  - **Righe**: 42
  - **Snippet**:
    - `using OT.Utils.Filters;`

- **File**: `Services\xtradingbroker\Services\ServiceCenter.cs`
  - **Righe**: 52
  - **Snippet**:
    - `using OT.Utils.Filters;`

- **File**: `Services\xtradingbroker\Services\V2\ServiceCenterV2.cs`
  - **Righe**: 54
  - **Snippet**:
    - `using OT.Utils.Filters;`

- **File**: `Services\xtradingbroker\Utils\FilterHelper.cs`
  - **Righe**: 18
  - **Snippet**:
    - `namespace OT.Utils.Filters`

### Nome: `Liquidity` (4 occorrenze)

- **File**: `Services\xtradingbroker\Extensions\IServiceCollectionExtensions.cs`
  - **Righe**: 32
  - **Snippet**:
    - `AddSpecificBroker<ServiceBusPushMessage>(services, configuration, "XtradingPushExternal-Liquidity", false);`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\BalanceDetailDeserializer.cs`
  - **Righe**: 11, 24, 36
  - **Snippet**:
    - `public List<BalanceDetailItemDeserializer> Liquidity { get; set; }`
    - `Liquidity = new List<BalanceDetailItemDeserializer>();`
    - `["Liquidity"] = Liquidity.Cast<BalanceDetail>().ToList(),`

### Nome: `ReservedAmounts` (4 occorrenze)

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\ReservedAmountDeserializer.cs`
  - **Righe**: 14, 15, 28, 50
  - **Snippet**:
    - `[JsonPropertyName("ReservedAmounts")]`
    - `public List<ReservedAmountDetailsDeserializer> ReservedAmounts { get; set; }`
    - `ReservedAmounts = ReservedAmounts.Cast<ReservedAmountDetail>().ToList(),`
    - ... e altri 1 snippet

### Nome: `Book` (3 occorrenze)

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 18
  - **Snippet**:
    - `Book = 1,`

- **File**: `Services\xtradingbroker\Utils\PortfolioValorizer.cs`
  - **Righe**: 189, 191
  - **Snippet**:
    - `if (prod.Book != null)`
    - `BookLevel book = prod.Book.FirstLevel;`

### Nome: `Details` (3 occorrenze)

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\PersonalListDeserializer.cs`
  - **Righe**: 28
  - **Snippet**:
    - `public JsonArray ListItem { set => base.Details = JsonSerializer.Deserialize<PersonalListItemDetailDeserializer[]>(value).Cast<BrokerPersonalListDetail>().ToList(); }`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Balance\BalanceDetailDeserializer.cs`
  - **Righe**: 34
  - **Snippet**:
    - `Details = new Dictionary<string, List<BalanceDetail>>()`

- **File**: `Services\xtradingbroker\Network\Models\Deserializers\Operations\ElaboraConsiglioDeserializer.cs`
  - **Righe**: 32
  - **Snippet**:
    - `Details = SuggestedOrdersDetails.ConvertAll(detail => (ElaboraConsiglioDetail)detail)`

### Nome: `DummyLogin` (3 occorrenze)

- **File**: `Services\xtradingbroker\Logic\UserManager.cs`
  - **Righe**: 68
  - **Snippet**:
    - `return await _apiClient.DummyLogin(string.Empty, body);`

- **File**: `Services\xtradingbroker\Network\Client\UserClient.cs`
  - **Righe**: 69
  - **Snippet**:
    - `public async Task<LoginResponseDeserializer> DummyLogin(string ids, LoginRequest body)`

- **File**: `Services\xtradingbroker\Repositories\AuthRepository.cs`
  - **Righe**: 86
  - **Snippet**:
    - `_logger.LogWarning("DummyLogin error: {error}", dummyResult.Error);`

### Nome: `News` (3 occorrenze)

- **File**: `Services\xtradingbroker\Extensions\IServiceCollectionExtensions.cs`
  - **Righe**: 36
  - **Snippet**:
    - `//AddSpecificBroker<ServiceBusPushMessage>(services, configuration, "XtradingPushExternal-News", false);`

- **File**: `Services\xtradingbroker\Logic\MessageManager.cs`
  - **Righe**: 148
  - **Snippet**:
    - `adviceType = AdviceType.News;`

- **File**: `Services\xtradingbroker\Push\Enum.cs`
  - **Righe**: 22
  - **Snippet**:
    - `News = 36,`

### Nome: `PersonalList` (3 occorrenze)

- **File**: `Services\xtradingbroker\Network\XtradingClient.cs`
  - **Righe**: 1526, 1528, 1537
  - **Snippet**:
    - `#region PersonalList`
    - `public async Task<List<PersonalList>> GetPersonalList(string accessToken, PersonalListRequest body)`
    - `return dResult.Cast<PersonalList>().ToList();`

### Nome: `Graph` (1 occorrenze)

- **File**: `Services\xtradingbroker\Utils\InfoAbilManager.cs`
  - **Righe**: 89
  - **Snippet**:
    - `def.InfoAbil |= InfoAbil.Graph;`

### Nome: `Main` (1 occorrenze)

- **File**: `Services\xtradingbroker\Program.cs`
  - **Righe**: 12
  - **Snippet**:
    - `public static void Main(string[] args)`

## Nomi Non Trovati

### A

- `APPGraph`, `APPManagement`, `ATCLayoutsManager`, `AccountTraderExe`, `AccountTraderHandler`
- `Accruals`, `AccrualsHandler`, `ActivationRequest`, `ActivationRequestHandler`, `AddToPersonalList`
- `AdvancedModified`, `AdvancedSearch`, `AjaxHandler`, `AlarmStatus`, `AlarmStatusTablet`
- `Alarms`, `AlarmsStatus`, `AppConfigMgmt`, `AppStatistics`, `AssociationFilters`
- `AuthorizingCodeLog`, `AuthorizingCodeLogHandler`, `AutoManualOrder`

### B

- `BOEserciziJobs`, `BOSettlement`, `BOSettlementHandler`, `BOSettlementJobs`, `BOSettlementModify`
- `BOSettlementT1`, `BOSettlementTP1Handler`, `BOSettlementTP1Modify`, `BalanceGBSDetail`, `BalanceGbs`
- `BalanceGbsResponsive`, `BalanceLoan`, `BalanceTabbed`, `BaseViewer`, `BestExecutionLogs`
- `BestExecutionLogsHandler`, `BestExecutionReports`, `BestExecutionReportsHandler`, `BestWorse`, `BestWorst`
- `BetaTest`, `BetaTestHandler`, `BlackListedWhiteListedURLHandler`, `BlackListedWhiteListedURLs`, `BoSettlementOverdraftCheck`
- `BondDetails`, `BondSelector`, `BondsTransferHandler`, `BondsTransferJobDetail`, `BondsTransferManagement`
- `BondsTransferToPlatformProduct`, `BondsTransferToPlatformUser`, `BookTablet`, `BridgeCommision`, `BridgeOtherPlatform`
- `BtpItaHandler`, `BtpItaManagement`

### C

- `CFDActivationList`, `CFDClientExposure`, `CUMASSViewer`, `CacheManagement`, `CalculateInterests`
- `CalculatorBond`, `CensusCounterparty`, `CfdHelp`, `CfdHelpHandler`, `CfdWatchList`
- `ChannelStatistics`, `ChannelStatisticsHandler`, `CheapestToDeliverHandler`, `CheapestToDeliverManagement`, `CheckAppVersion`
- `CheckDevice`, `CheckProfitLoss`, `CheckSemaphores`, `CheckVersion`, `CheckVersion5`
- `ClientView`, `CollateralBonds`, `CollateralBondsEditor`, `CollateralBondsHandler`, `CollateralBondsHistory`
- `CollateralsCruscotto`, `CollateralsCruscottoHandler`, `CommissionAnagHandler`, `CommissionAnagHandler.ashx`

### Pagine (`, `CommissionReport`
- `CommissionStatistics`, `CommissioniDigressive`, `CommissioniImpostaSoglie`, `CommissionsAccounting`, `CommissionsDetail`
- `CommissionsDigressive`, `CommissionsMUC`, `CommissionsManagementPage`, `CommissionsMethod`, `CommissionsPromozioni`
- `CommissionsReport`, `CommissionsResults`, `CommissionsSearch`, `CommissionsSpeseOneri`, `CommissionsSpeseOneriDetails`
- `CommissionsStandard`, `CommissionsStatistiche`, `CommissionsStoricoModifiche`, `CommissionsValoriMassimi`, `ConfiguraCalendarioMercati`
- `ConfigurazioneConti`, `ConfigurazioneContiHandler`, `ConfirmStrategy`, `ConsoleSmit`, `ConsoleSmitFix`
- `ContabGroups`, `Contacts`, `ContractsManagement`, `ContractsManagementHandler`, `ContractsView`
- `CounterpartsAccounting`, `CourtesyPageBP`, `CreateNewAccount`, `CrossAuthentication`, `CrossAuthenticationAppSellaIt`
- `CrossAuthenticationMobile`, `Cruscotto`, `CruscottoHandler`, `CustomerAnagHandler`, `CustomerEnabled`
- `CustomerListManagement`, `CustomerSearch`, `CustomerSearchAccount`, `CustomerSearchResult`, `c-css`
- `c-css.ashx`

## Tablet

### Pagine (`

### D

- `DailyStatistics`, `DailyStatisticsHandler`, `DataManagerHandler`, `DeliberaMon`, `DeliberaMonHandler`
- `DependentsDetailView`, `DependentsView`, `DerActivationList`, `DerActivationListHandler`, `Derivatives`
- `DerivativesAccounting`, `DerivativesAccountingDetail`, `DerivativesAccountingHandler`, `DerivativesAccountingHist`, `DerivativesAssetAndMaxAmount`
- `DerivativesDeliberateNew`, `DerivativesDetails`, `DerivativesDetailsNew`, `DerivativesEnable`, `DerivativesEnableNew`
- `DerivativesHandler`, `DerivativesSerie`, `DerivativesSerieHandler`, `DerivativesTradeGroups`, `DerivativesUnderlinesDetail`
- `DerivativesVerifyAndMaxAmount`, `DetailNormalizePosition`, `DetailNotice`, `DetailOrderCommission`, `DetailsFrames`
- `Dialog`, `DigitalSignature`, `Disclaimer`, `DisclaimerMobile`, `DividendPayment`
- `DossierDetails`, `DownTimeManagement`, `DownTimeManagementHandler`, `DownloadData`, `DownloadInstrument`
- `DownloadInstrumentStepTwo`, `DownloadRemoting`, `DummyLoginIntra`, `DummyLoginRecoverSession`, `DummyLoginRecoverSessionBridge`

### E

- `ETMS_ManagementHandler`, `ETMS_ManagementView`, `EditMenu`, `EditMenuEntry`, `EditOrderCommission`
- `ElencoFestivita`, `EmirCounterpartyClearingThreshoContract`, `EmirCruscotto`, `EmirCruscottoHandler`, `EmirDelegateContract`
- `EmirDetail`, `EmirPopup`, `EmirProcessReports`, `EmirProducts`, `EmirSeries`
- `EmirUnpaired`, `EmirUtiManagement`, `EnablePush`, `EngagedAcctDetail`, `EngagedAmount`
- `EngagedAmountCFD`, `EngagedAmountHandler`, `EoniaRates`, `ExeTraderAccount`, `ExeTraderAccountHandler`
- `ExternalNotify`, `ExternalNotifyHandler`, `Extractions`

### F

- `FastJobSelection`, `FavoriteSubjectsHandler`, `FillComboSearchHandler`, `FillComboSearchHandlerApp`, `FillComboSearchHandlerFE`
- `FillComboTradingHandler`, `FlowManagementHandler`, `ForceSetback`, `FrontEndBaseHandler`, `FunctionJobsLauncher`
- `FunctionJobsLauncherHandler`, `FundDetail`, `FundsHandler`, `FundsManagement`

### G

- `GPShortMultiDay`, `GPShortMultiDayHandler`, `GeneralSettings`, `GeneralStatistics`, `GeneralStatisticsForOrders`
- `GeneralStatisticsForOrdersHandler`, `GenericError`, `GenericError.aspx`

## GetStatus.aspx

### Pagine (`, `GetStatus.aspx`

## GetVersion.aspx

### Pagine (`, `GetVersion`
- `GetVersion.aspx`

## InfoFunctions

### Handler (`, `GpSMD`, `GpSMDHandler`, `Graph_new`, `Graph_new_phone`
- `GrecheManager`, `GrecheManagerHandler`

### H

- `HSMainDetailView`, `HandlerSoglie`, `Head`, `Header`, `HelpShunting`
- `HelpTolgbs`, `HistoricalDetailSL`, `HistoricalTitleSL`, `HistoryRefCap`, `HomePageCustomer`
- `HomePageCustomerHandler`

### I

- `IPOPlacementSpecifications`, `IgAccounts`, `IgAccountsHandler`, `ImpostaSoglie`, `InfoEnabled`
- `InfoEnabledHandler`, `InfoEnabledServices`, `InfoServiceHandler`, `InfoServiceStorico`, `InfoServicesEdit`
- `InfoServicesMain`, `InsertAlarm`, `InsertOrdersGbs`, `InsertOrdersInvestor`, `InsertProduct`
- `InsertProductHandler`, `InsertProductUnderlying`, `IpoBlackListManagement`, `IpoBlackListManagementHandler`, `IpoConsoleLog`
- `IpoConsoleLogHandler`

### J

- `JSAC`, `JSAC_APP`, `JSAC_APP_CLEAN`, `JobEditor`, `JobGantt`
- `JobStart`, `JobStatistics`, `JobStatisticsHandler`, `JobStop`

### L

- `LandingPageAfterDismissionInvestor`, `LandingPageDismissionInvestor`, `LegalNotes`, `LendingFeesTablet`, `LeveragePlusHandler`
- `LeveragePlusHandler.ashx`

### Pagine (`, `LicenseManagement`, `LiquidityDestination`, `LmdAccounting`, `LmdAccountingHandler`
- `LmdAccountingHist`, `LmdEvaluationDetails`, `LmdEvaluationsHandler`, `LmdExposure`, `LoanDetail`
- `Localization`, `LogFEDetail`, `LoginIntra`, `Logoff`, `LogonSkipMessages`

### M

- `MainMenu`, `Maintenance`, `ManageChangeInitialMargin`, `ManageChangeInitialMarginHandler`, `ManageChangeInitialMarginModify`
- `ManageControlsHandler`, `ManageControlsMain`, `ManageFtpHostReport`, `ManageIgAccounts`, `ManagePersonalList`
- `ManageReportExecution`, `ManageReportExtractions`, `ManagementFilter`, `ManagementLending`, `ManagementLendingHandler`
- `ManagementPP`, `ManagementPPHandler`, `ManlevaPriips`, `ManualOrderEdit`, `ManualOrderHandler`
- `ManualOrders`, `ManualOrdersHandler`, `MarginsHandler`, `MarketAbuse`, `MarketAbuseHandler`
- `MarketHolidayCalendarHandler`, `MaxEngagedAmountCollateralAmount`, `Menu`, `MenuAdmin`, `MenuAdminBsHandler`
- `MenuHandler`, `MenuInvestimentiAPP`, `MenuInvestimetiAPPHandler`, `MessagesGbs`, `MessagesTablet`
- `Metastock`, `MifidFunctionDetail`, `MifidFunctionResp`, `MiglioriPeggiori`, `MobileLandingPage`
- `MobileLayoutHandler`, `MobileLayoutHandler.ashx`

## GenericError.aspx

### Pagine (`, `Modify`, `ModifyContract`, `ModifyContractPlus`
- `ModifyInterestRate`, `ModifyNotice`, `ModifyParkingOrder`, `ModifyRemoting`, `MonitorPerditeDerivati`
- `MoveMenuEntry`, `MultiCustomerOrdersConsoleAdv`, `MultidayLeverageParameters`, `Multilanguage`, `MultilanguageHandler`
- `MultilanguageV1`

### N

- `NewBusinessObject`, `NewFlow`, `NewFlowEx`, `NewProduct`, `NewProductHandler`
- `NewRequest`, `NewsDetail`, `NewsManager`, `NoRealTimeInfoSet`, `NoteInformative`
- `NoticeConsole`, `NoticeHandler`, `NotifyParked`

### O

- `OpenWebTradingPage.aspx`

## XRemoting

### Pagine (`, `OrderDetailsGbs`, `OrderDetailsInv`, `OrderStatusDetailView`, `OrderStatusHandler`
- `OrderStatusView`, `OrderSuggestions`, `OrderSuggestionsHandler`, `OrdersAnagHandler`, `OrdersGbs`
- `OrdersGbsNoUser`, `OrdersMenu`, `OrdersPositionsGbs`, `OrdersPositionsGbsNoUser`, `OrdersStatus`
- `OrdersStatusInvestor`, `OrdersStatusTablet`, `OrganizeFavorites`

### P

- `PaginaCortesiaNoInformativa`, `PdfRiskCosts`, `PendingRequest`, `PersonalListHandler`, `PersonalListsImport`
- `PersonalListsManagement`, `PersonalListsManager`, `PersonalViewsEdit`, `PersonalViewsManager`, `Personalize`
- `PersonalizeAPP`, `PlatformCross`, `PopUpMessages`, `PortalLinks`, `PortfolioGbs`
- `PortfolioInvestor`, `PortfolioTablet`, `PotiDetails`, `PreLoginIntra`, `PriceKeepingManagement`
- `PriipsSummary`, `ProductDetail`, `ProductDetailModify`, `ProfStatementCME`, `ProfStatementHandler`
- `ProfiledAccountHander`, `ProfiledAccounts`, `Profiling`, `ProfiloHistoryBondHandler`, `ProfiloHistoryCashHandler`
- `ProfitAndLossGbs`, `ProfitAndLossTablet`, `PropertyManagement`, `PropertyManagementHandler`, `Pwd`

### R

- `Recalculations`, `RecalculationsHandler`, `RectifySeries`, `RectifySeriesHandler`, `RectifySeriesMCF`
- `RectifySeriesMCFHandler`, `RefCap`, `RefCapHandler`, `RefCapVerify`, `RefCapVerifyDet`
- `ReferencePriceHandler`, `ReferencePriceHistory`, `ReloadConfig`, `ReloadConfigHandler`, `RemoteNameNotAvailable`
- `RemotingLicenseManagementHandler`, `RemotingManagement`, `ReportDichiarazioniBorse`, `ReportPostazioni`, `Repricing`
- `ReprocessRelevantsImport`, `ReprocessRequests`, `ReprocessRequestsHandler`, `RequestHandler`, `RequestHandlerReadOnly`
- `RequestHandlerRequired`, `RequestHandlerRequired.ashx`

### Pagine (`, `RequestHandlerV2`, `ResponseInsertOrder`, `ResponseInsertOrderInvestor`
- `ResultCustomerSearch.aspx`

## Mobile

### Pagine (`, `RiallineaTitoli`

### S

- `SATDocument`, `SATNotice`, `SalvaModificaJob`, `SalvaModificaTask`, `SalvaModificaTrigger`
- `SatViewer`, `Sconfino`, `SconfinoHandler`, `SconfinoHandler.ashx`

### Pagine (`, `SearchCW`
- `SearchCwBond`, `SearchDer`, `SearchHandler`, `SearchLendingHandler`, `SearchList`
- `SearchLogFE`, `SearchLogFeHandler`, `SearchObb`, `SearchPositions`, `SearchPositionsHandler`
- `Searchtit`, `SecurityOperations`, `Segnalazioni`, `SegnalazioniCFD`, `SegnalazioniHandler`
- `SegnalazioniMaxConcentrazione`, `SegnalazioniMaxConcentrazioneHandler`, `SegnalazioniSMD20`, `SegnalazioniSMD360`, `SegnalazioniSMDHandler`
- `SelectColor`, `SelectHomePage`, `SellaExtremeService`, `SemaphoresHandler`, `SendParkingOrder`
- `SendPdf`, `SendPdfHandler`, `SendSimulationRequest`, `SerieEdit`, `ServiceActivationHandler`
- `ServiceActivations`, `ServiceTolHandler`, `SessionFailed`, `SessionInfo.aspx`

## SessionStats.aspx

### Pagine (`, `SessionStats`
- `SessionStats.aspx`

## Settings

### Handler (`, `SetDefaultQuantity`, `SetMinimo`, `SetRefCap`, `SetbackHandler`
- `SetbackTrace`, `SettlementOrdersDer`, `ShortAndLeverage`, `ShortAndLeverageDetails`, `ShortLeva`
- `ShortLevaEdit`, `ShortLevaHandler`, `ShowInterest`, `ShowNotice`, `ShowPdf`
- `SimulationJobs`, `SimulationRequest`, `SimulationResponse`, `SmitAuoAccounts`, `SmitHandler`
- `SoundNotificationHandler`, `SpecialOrderStatus`, `SpecialOrdersHandler`, `StatHandler`, `StatisticsHandler`
- `StatusRequestHandler`, `StatusTask`, `StatusTaskHandler`, `StockChooser`, `StocksEngage`
- `StrategiesOrderStatus`, `StrategiesOrderStatusTablet`, `StrategiesOrderStatusView`, `StrategiesSearch`, `StrategiesSearchTablet`
- `StrategiesStatusHandler`, `StrategiesStatusHandler.aspx`

## WebApi

### Pagine (`, `StrategyBuilderV2`, `StrategyDetail`, `StrategyDetails`
- `StrategyDetailsHandler`, `StrategyInstrument`, `StrategyManager`, `StrategyManager.ashx`

### Pagine (`, `StrategyOrderStatusHandler`
- `SubSystemDetailView`, `SubSystemTableDetailView`, `SubSystemView`, `SubSytemTableView`, `SuperClassViewer`
- `SwiftAndFreeStatistics`, `SwiftAndFreeStatisticsHandler`, `showip.aspx`

## waiting.aspx

### Pagine (`

### T

- `TabletHome`, `TabletPersonalViews`, `TabletPersonalViews.ashx`

### Pagine (`, `TestMenuApplet`, `TestTicker`
- `TimeAndSales`, `TimesReport`, `TimesReportHandler`, `TobinTaxDebits`, `TobinTaxHandler`
- `Top`, `TradeViewChild`, `TradeViewHandler`, `TradeViewMaster`, `TraderProfileHandler`
- `TraderProfileRequest`, `TradingExtraInfo`, `TradingExtraInfo.aspx`

## showip.aspx

### Pagine (`, `TradingGbs`, `TradingGbs.aspx`

## Intranet

### Handler (`
- `TradingGroupsData`, `TradingGroupsDataInfo`, `TradingHandler`, `TradingPremium`, `TradingPremiumHandler`
- `TransTraceView`, `TreeView`

### U

- `UpdateAbilDerOnSmit`, `UserProfileAPP`, `UserProfileAccount`, `UserProfileCapRif`, `UserProfileDerCfd`
- `UserProfileHandler`, `UserProfileLiquidity`, `UserProfileLiquidityCFD`, `UserProfileLpl`, `UserProfileMail`
- `UserProfilePdf`, `UserProfileShl`, `UserProfileShl.aspx`

## Styles

### Handler (`, `UserStats`, `UserStatsHandler`
- `UtilIntraDispo`

### V

- `VideoTutorial`, `ViewBondPdf`, `ViewContracts`, `ViewDoc`, `ViewDocMaxMargin`
- `ViewGenericPDF`, `ViewListHandler.aspx`

## SessionInfo.aspx

### Pagine (`, `ViewMargins`, `ViewMarginsTablet`, `ViewNotificaRic`
- `ViewNotifyNote`, `ViewPDFBarCode`, `ViewPdf`, `ViewPortfolio`, `ViewPortfolioHandler`
- `ViewSchedaProd`, `ViewSetMinimoPDF`, `ViewWindowMarket`, `ViewWindowMarket.aspx`

## TradingFunctions

### Handler (`, `ViewerDoc`
- `ViewerPdf`, `Viewer_New`, `VirtualPortfolioDisplay`, `VirtualPortfolioDisplayTablet`, `VirtualPortfolioSimulate`
- `VirtualPortfolios`, `VirtualPortfoliosTablet`, `VisualizationDocHandler`, `VisualizationDocuments`, `VisualizationDocumentsTablet`

### W

- `W_Collateral`, `W_Maximal`, `WarnConsole`, `WarnConsoleAPP`, `WatchListHandler`
- `Watchlist`, `Watchlist.aspx`

## Internet

### Handler (`, `Webinar`, `Welcome`, `WizardSearch.aspx`

## RadControls

### Pagine (`
- `waiting.aspx`

## whereami.aspx

### Pagine (`, `whereami`

### X

- `XhtmlValidator`, `XhtmlValidator.aspx`

## SellaExtreme

### Handler (`

### _

- `_TradingHandler`

## Conclusioni

Questo report identifica dove i nomi delle pagine frontend sono utilizzati nel codice XTradingBroker.
Da notare che i nomi potrebbero apparire in contesti non correlati alle chiamate API effettive.
Un'analisi più dettagliata del codice sarebbe necessaria per confermare l'uso effettivo delle API.
