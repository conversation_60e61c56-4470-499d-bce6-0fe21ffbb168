using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class PreferitiDeleteTest : TestBase
    {
        private readonly string _username;
        private readonly string _password;
        private OTTradingClient _client;
        private readonly string _market;
        private readonly string _stock;
        private readonly string _groupName;
        private string _preferitiId;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        public PreferitiDeleteTest(string name, bool isDummy = false) : base(name, isDummy)
        {
            // O<PERSON>eni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";
            
            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);
            
            _market = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "XNAS";
            _stock = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "MSFT";
            _groupName = Environment.GetEnvironmentVariable("OT_TEST_GROUP") ?? "Test Group";
            
            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }
                
                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test di eliminazione preferiti");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }
                
                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));
                
                // Implementazione del test di eliminazione preferiti
                
                // 1. Recupera i gruppi di preferiti esistenti
                var existingGroups = await GetPreferitiGroupsAsync();
                LogTestStep("Recupero Gruppi", true, $"Trovati {existingGroups.Count} gruppi di preferiti");
                _testSteps.Add(new TestStepResult("Recupero Gruppi", true, $"Trovati {existingGroups.Count} gruppi di preferiti"));
                
                // 2. Crea un nuovo gruppo di preferiti se necessario
                bool groupExists = await CheckGroupExistsAsync();
                if (!groupExists)
                {
                    if (!await CreatePreferitiGroupAsync())
                    {
                        LogTestStep("Creazione Gruppo", false, "Impossibile creare il gruppo di preferiti");
                        _testSteps.Add(new TestStepResult("Creazione Gruppo", false, "Impossibile creare il gruppo di preferiti"));
                        return false;
                    }
                    LogTestStep("Creazione Gruppo", true, $"Gruppo '{_groupName}' creato con ID: {_preferitiId}");
                    _testSteps.Add(new TestStepResult("Creazione Gruppo", true, $"Gruppo '{_groupName}' creato con ID: {_preferitiId}"));
                    
                    // Aggiungi un titolo al gruppo
                    if (!await AddStockToPreferitiAsync())
                    {
                        LogTestStep("Aggiunta Titolo", false, $"Impossibile aggiungere il titolo {_stock} al gruppo");
                        _testSteps.Add(new TestStepResult("Aggiunta Titolo", false, $"Impossibile aggiungere il titolo {_stock} al gruppo"));
                        return false;
                    }
                    LogTestStep("Aggiunta Titolo", true, $"Titolo {_stock} aggiunto al gruppo");
                    _testSteps.Add(new TestStepResult("Aggiunta Titolo", true, $"Titolo {_stock} aggiunto al gruppo"));
                }
                else
                {
                    LogTestStep("Verifica Gruppo", true, "Gruppo esistente trovato per il test");
                    _testSteps.Add(new TestStepResult("Verifica Gruppo", true, "Gruppo esistente trovato per il test"));
                }
                
                // 3. Rimuovi il titolo dal gruppo
                if (!await RemoveStockFromPreferitiAsync())
                {
                    LogTestStep("Rimozione Titolo", false, $"Impossibile rimuovere il titolo {_stock} dal gruppo");
                    _testSteps.Add(new TestStepResult("Rimozione Titolo", false, $"Impossibile rimuovere il titolo {_stock} dal gruppo"));
                    return false;
                }
                LogTestStep("Rimozione Titolo", true, $"Titolo {_stock} rimosso dal gruppo");
                _testSteps.Add(new TestStepResult("Rimozione Titolo", true, $"Titolo {_stock} rimosso dal gruppo"));
                
                // 4. Verifica che il titolo sia stato rimosso
                if (!await VerifyStockRemovedAsync())
                {
                    LogTestStep("Verifica Rimozione", false, $"Titolo {_stock} ancora presente nel gruppo");
                    _testSteps.Add(new TestStepResult("Verifica Rimozione", false, $"Titolo {_stock} ancora presente nel gruppo"));
                    return false;
                }
                LogTestStep("Verifica Rimozione", true, $"Titolo {_stock} non più presente nel gruppo");
                _testSteps.Add(new TestStepResult("Verifica Rimozione", true, $"Titolo {_stock} non più presente nel gruppo"));
                
                // 5. Elimina il gruppo
                if (!await DeletePreferitiGroupAsync())
                {
                    LogTestStep("Eliminazione Gruppo", false, "Impossibile eliminare il gruppo di preferiti");
                    _testSteps.Add(new TestStepResult("Eliminazione Gruppo", false, "Impossibile eliminare il gruppo di preferiti"));
                    return false;
                }
                LogTestStep("Eliminazione Gruppo", true, "Gruppo eliminato con successo");
                _testSteps.Add(new TestStepResult("Eliminazione Gruppo", true, "Gruppo eliminato con successo"));
                
                // 6. Verifica che il gruppo sia stato eliminato
                if (!await VerifyGroupDeletedAsync())
                {
                    LogTestStep("Verifica Eliminazione", false, "Gruppo ancora presente");
                    _testSteps.Add(new TestStepResult("Verifica Eliminazione", false, "Gruppo ancora presente"));
                    return false;
                }
                LogTestStep("Verifica Eliminazione", true, "Gruppo non più presente");
                _testSteps.Add(new TestStepResult("Verifica Eliminazione", true, "Gruppo non più presente"));
                
                Logger.Info("Test di eliminazione preferiti completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("Eliminazione Preferiti", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("Eliminazione Preferiti", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("Eliminazione Preferiti", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("Eliminazione Preferiti", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<List<JsonElement>> GetPreferitiGroupsAsync()
        {
            try
            {
                var request = new
                {
                    // Parametri per la richiesta dei gruppi di preferiti
                };

                var response = await _client.SendRequestAsync("GET", "api/Preferiti/Groups", request);
                var groups = new List<JsonElement>();
                
                if (response.TryGetProperty("groups", out var groupsElem) && 
                    groupsElem.ValueKind == JsonValueKind.Array)
                {
                    for (int i = 0; i < groupsElem.GetArrayLength(); i++)
                    {
                        var group = groupsElem[i];
                        groups.Add(group);
                    }
                }
                
                return groups;
            }
            catch (Exception ex)
            {
                LogTestStep("Recupero Gruppi", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Recupero Gruppi", false, $"Errore: {ex.Message}"));
                return new List<JsonElement>();
            }
        }
        
        private async Task<bool> CheckGroupExistsAsync()
        {
            try
            {
                var groups = await GetPreferitiGroupsAsync();
                foreach (var group in groups)
                {
                    if (group.TryGetProperty("name", out var nameElem) && 
                        nameElem.GetString() == _groupName)
                    {
                        // Gruppo trovato, salva l'ID
                        if (group.TryGetProperty("id", out var idElem))
                        {
                            _preferitiId = idElem.GetString();
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                LogTestStep("Verifica Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Verifica Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> CreatePreferitiGroupAsync()
        {
            try
            {
                var request = new
                {
                    name = _groupName,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("POST", "api/Preferiti/CreateGroup", request);
                
                // Tentativo di estrarre l'ID del gruppo
                if (response.TryGetProperty("groupId", out var groupIdElem) && 
                    groupIdElem.ValueKind == JsonValueKind.String)
                {
                    _preferitiId = groupIdElem.GetString();
                    return !string.IsNullOrEmpty(_preferitiId);
                }
                
                // Se non trova il campo groupId, cerca in altre proprietà
                if (response.TryGetProperty("id", out var idElem) && 
                    idElem.ValueKind == JsonValueKind.String)
                {
                    _preferitiId = idElem.GetString();
                    return !string.IsNullOrEmpty(_preferitiId);
                }

                // Se ancora non è stato trovato, genera un ID fittizio (solo per demo)
                _preferitiId = $"GRP_{DateTime.Now.Ticks}";
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Creazione Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Creazione Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> AddStockToPreferitiAsync()
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    market = _market,
                    stock = _stock,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("POST", "api/Preferiti/AddStock", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Aggiunta Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Aggiunta Titolo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> RemoveStockFromPreferitiAsync()
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    market = _market,
                    stock = _stock,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("DELETE", "api/Preferiti/RemoveStock", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Rimozione Titolo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Rimozione Titolo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> VerifyStockRemovedAsync()
        {
            try
            {
                var response = await _client.SendRequestAsync("GET", $"api/Preferiti/Group/{_preferitiId}", null);
                
                if (response.TryGetProperty("stocks", out var stocksElem) && 
                    stocksElem.ValueKind == JsonValueKind.Array)
                {
                    for (int i = 0; i < stocksElem.GetArrayLength(); i++)
                    {
                        var stockItem = stocksElem[i];
                        if (stockItem.TryGetProperty("marketCode", out var marketCode) && 
                            marketCode.GetString() == _market &&
                            stockItem.TryGetProperty("stockCode", out var stockCode) && 
                            stockCode.GetString() == _stock)
                        {
                            // Titolo ancora presente
                            return false;
                        }
                    }
                }
                
                // Titolo non trovato, quindi è stato rimosso
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Verifica Rimozione", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Verifica Rimozione", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> DeletePreferitiGroupAsync()
        {
            try
            {
                var request = new
                {
                    groupId = _preferitiId,
                    // Altri parametri necessari...
                };

                var response = await _client.SendRequestAsync("DELETE", "api/Preferiti/DeleteGroup", request);
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Eliminazione Gruppo", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Eliminazione Gruppo", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        private async Task<bool> VerifyGroupDeletedAsync()
        {
            try
            {
                var groups = await GetPreferitiGroupsAsync();
                foreach (var group in groups)
                {
                    if (group.TryGetProperty("id", out var idElem) && 
                        idElem.GetString() == _preferitiId)
                    {
                        // Gruppo ancora presente
                        return false;
                    }
                }
                
                // Gruppo non trovato, quindi è stato eliminato
                return true;
            }
            catch (Exception ex)
            {
                LogTestStep("Verifica Eliminazione", false, $"Errore: {ex.Message}");
                _testSteps.Add(new TestStepResult("Verifica Eliminazione", false, $"Errore: {ex.Message}"));
                return false;
            }
        }
        
        // Restituisce i risultati dei test step
        public List<TestStepResult> GetTestSteps()
        {
            return _testSteps;
        }
    }
}
