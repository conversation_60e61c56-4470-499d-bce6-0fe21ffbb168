# TestAutomations - Strumenti di Test e Analisi per OT

Questo progetto contiene strumenti per l'automazione dei test e l'analisi dei log per l'applicazione OT.

## Indice

1. [Test Automatizzati](#test-automatizzati)
2. [Ana<PERSON><PERSON> dei Log](#analisi-dei-log)
3. [Integrazione con Kubernetes](#integrazione-con-kubernetes)
4. [Integrazione con OpenAI](#integrazione-con-openai)
5. [Configurazione](#configurazione)
6. [Esemp<PERSON> di Utilizzo](#esempi-di-utilizzo)

## Test Automatizzati

Il progetto include diversi test automatizzati per verificare il corretto funzionamento dell'applicazione OT:

### Test di Trading

- **TradingFlowTest**: Test del flusso di trading completo
- **TradingFlowVirtualTest**: Test del flusso di trading con broker virtuale
- **TradingFlowUpdateTest**: Test dell'aggiornamento degli ordini
- **TradingFlowDeleteTest**: Test della cancellazione degli ordini

### Test di Portafoglio

- **PortafoglioTest**: Test generale del portafoglio
- **PortafoglioNewPositionTest**: Test per la creazione di nuove posizioni
- **PortafoglioClosePositionTest**: Test per la chiusura di posizioni
- **PortafoglioUpdateTest**: Test per l'aggiornamento del portafoglio

### Test di Preferiti

- **PreferitiTest**: Test generale dei preferiti
- **PreferitiInsertTest**: Test per l'inserimento di preferiti
- **PreferitiModifyTest**: Test per la modifica di preferiti
- **PreferitiDeleteTest**: Test per l'eliminazione di preferiti

### Test di Profitti e Perdite

- **ProfitLossTest**: Test generale di profitti e perdite
- **ProfitLossLongPositionTest**: Test di profitti e perdite per posizioni long
- **ProfitLossShortPositionTest**: Test di profitti e perdite per posizioni short

### Altri Test

- **LoginTest**: Test di login
- **AlarmsTest**: Test degli allarmi

## Analisi dei Log

Il progetto include strumenti per l'analisi dei log:

### LogAnalyzer

La classe `LogAnalyzer` fornisce metodi per analizzare i log dei test:

```csharp
// Analizza i risultati dei test
string analysis = LogAnalyzer.AnalyzeTestSteps(testSteps);

// Analizza i pattern nei log delle richieste
string patterns = LogAnalyzer.AnalyzeRequestLogPatterns(requestLogs);

// Riassume i log delle richieste
string summary = LogAnalyzer.SummarizeRequestLogs(requestLogs);

// Riassume i log Kubernetes
string k8sSummary = LogAnalyzer.SummarizeKubernetesLogs(k8sLogs);
```

## Integrazione con Kubernetes

### KubernetesLogFetcher

La classe `KubernetesLogFetcher` permette di recuperare i log dai deployment Kubernetes:

```csharp
// Login ad Azure
var loginResult = await KubernetesLogFetcher.LoginToAzureAsync();

// Recupera i log di un deployment
string logs = await KubernetesLogFetcher.FetchLatestLogsAsync(
    deployment: "cacheprovider",
    ns: "ot",
    tailLines: 1000,
    sinceSeconds: 3600);
```

#### Autenticazione Azure

Prima di recuperare i log, è necessario autenticarsi ad Azure:

```csharp
var loginResult = await KubernetesLogFetcher.LoginToAzureAsync();
if (loginResult.Success)
{
    Console.WriteLine("Login ad Azure completato con successo");
}
else
{
    Console.WriteLine($"Errore durante il login ad Azure: {loginResult.Message}");
}
```

#### Recupero dei Log

```csharp
// Recupera le ultime 1000 righe
string logs = await KubernetesLogFetcher.FetchLatestLogsAsync("cacheprovider", "ot", 1000);

// Recupera i log degli ultimi 10 minuti (600 secondi)
string logs = await KubernetesLogFetcher.FetchLatestLogsAsync("cacheprovider", "ot", 1000, 600);

// Recupera tutti i log
string logs = await KubernetesLogFetcher.FetchLatestLogsAsync("cacheprovider", "ot", -1);
```

## Integrazione con OpenAI

### OpenAILogAnalyzer

La classe `OpenAILogAnalyzer` permette di analizzare i log utilizzando l'API di OpenAI:

```csharp
// Crea un'istanza dell'analizzatore
var analyzer = new OpenAILogAnalyzer();

// Analizza i log
string analysis = await analyzer.AnalyzeLogsAsync(logs, prompt);

// Analizza i log di un deployment Kubernetes
string k8sAnalysis = await analyzer.AnalyzeKubernetesLogsAsync(
    deployment: "cacheprovider",
    ns: "ot",
    prompt: "Analizza questi log e identifica eventuali errori",
    tailLines: 1000,
    sinceSeconds: 3600);

// Analizza i log di più deployment
var deployments = new List<string> { "cacheprovider", "api-gateway", "auth-service" };
string multiAnalysis = await analyzer.AnalyzeMultipleKubernetesLogsAsync(
    deployments: deployments,
    ns: "ot",
    prompt: "Identifica correlazioni tra i problemi nei diversi servizi",
    tailLines: 500,
    sinceSeconds: 1800);
```

## Configurazione

### File secrets.json

Il progetto utilizza un file `secrets.json` per la configurazione:

```json
{
  "OT_API_BASE_URL": "https://ot.tst.sella.it",
  "OT_USERNAME": "username",
  "OT_PASSWORD": "password",
  "OT_TEST_MARKET": "MTA",
  "OT_TEST_STOCK": "AMP",
  "OT_TEST_ORDER_TYPE": "0",
  "OT_TEST_PRICE": "10.5",
  "OT_TEST_QUANTITY": "1",
  "OPENAI_API_KEY": "your-openai-api-key",
  "AZURE_TENANT_ID": "your-tenant-id",
  "AZURE_CLIENT_ID": "your-client-id",
  "AZURE_CLIENT_SECRET": "your-client-secret",
  "AZURE_AKS_CLUSTER_NAME": "ot-aks-cluster",
  "AZURE_RESOURCE_GROUP": "ot-resource-group"
}
```

### Variabili d'Ambiente

In alternativa, è possibile utilizzare variabili d'ambiente con gli stessi nomi.

## Esempi di Utilizzo

### Analisi dei Log Kubernetes

```csharp
// Effettua il login ad Azure
var loginResult = await KubernetesLogFetcher.LoginToAzureAsync();

// Recupera i log
var logs = await KubernetesLogFetcher.FetchLatestLogsAsync("cacheprovider", "ot", 500);

// Analizza i log con OpenAI
var analyzer = new OpenAILogAnalyzer();
var analysis = await analyzer.AnalyzeLogsAsync(logs, "Identifica eventuali errori");
Console.WriteLine(analysis);
```

### Debug di un Problema Specifico

```csharp
var analyzer = new OpenAILogAnalyzer();
var deployments = new List<string> { 
    "cacheprovider", 
    "api-gateway", 
    "auth-service"
};

var prompt = "Analizza questi log per identificare la causa del problema di timeout nelle richieste API";
var analysis = await analyzer.AnalyzeMultipleKubernetesLogsAsync(
    deployments,
    "ot",
    prompt,
    300,
    1800);

Console.WriteLine(analysis);
```

## Prerequisiti

1. **Azure CLI**: Necessario per l'autenticazione ad Azure
2. **kubectl**: Necessario per interagire con Kubernetes
3. **.NET Core 6.0+**: Necessario per eseguire l'applicazione

## Note

- I log vengono salvati automaticamente nella directory `logs` con un timestamp nel nome del file
- L'analisi con OpenAI richiede una chiave API valida
- Per l'autenticazione ad Azure, è necessario configurare le credenziali nel file `secrets.json` o nelle variabili d'ambiente
