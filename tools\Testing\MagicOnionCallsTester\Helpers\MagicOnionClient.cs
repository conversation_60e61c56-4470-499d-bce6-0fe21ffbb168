﻿using Grpc.Net.Client;
using MagicOnion;
using MagicOnion.Client;

namespace MagicOnionCallsTester.Helpers
{
    public class MagicOnionClient<T> where T : IService<T>
    {
        public T Client { get; private set; }

        public MagicOnionClient(string host, int port, int maxReceiveMessageLength = 104857600) : this(false, host, port, maxReceiveMessageLength) { }
        public MagicOnionClient(bool https, string host, int port, int maxReceiveMessageLength = 104857600) // default 20 MB max
        {
            var httpHandler = new HttpClientHandler();
            // Return `true` to allow certificates that are untrusted/invalid
            httpHandler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
            var channel = GrpcChannel.ForAddress($"{(https ? "https" : "http")}://{host}:{port}", new GrpcChannelOptions() { MaxReceiveMessageSize = maxReceiveMessageLength, HttpHandler = httpHandler });
            Client = MagicOnionClient.Create<T>(channel);
        }
    }
}
