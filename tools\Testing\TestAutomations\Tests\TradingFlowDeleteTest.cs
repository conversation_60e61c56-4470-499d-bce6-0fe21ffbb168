using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class TradingFlowDeleteTest : TestBase
    {
        private readonly string[] _args;
        private OTTradingClient _client;
        private readonly string _username;
        private readonly string _password;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        // Parametri di configurazione per retry
        private const int MaxRetryAttempts = 3;
        private const int InitialRetryDelayMs = 1000;
        private const double RetryBackoffFactor = 1.5;

        public TradingFlowDeleteTest(string name, string[] args = null, bool isDummy = false) : base(name, isDummy)
        {
            _args = args ?? new string[0];

            // Ottieni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test di trading flow
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test di trading flow Delete");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // Esegui il test scenario
                bool testResult = await ExecuteTradingFlowDelete();

                if (!testResult)
                {
                    Logger.Error("Test di trading flow Delete fallito");
                    return false;
                }

                Logger.Info("Test di trading flow Delete completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("TradingFlowDelete", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlowDelete", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("TradingFlowDelete", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlowDelete", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        public async Task<bool> ExecuteTradingFlowDelete()
        {
            try
            {
                // Esegui il test scenario
                bool result = await RunTestScenarioAsync(_args);

                // Aggiungiamo un log riepilogativo
                LogTestStep("Flusso Trading Delete", result, result ? "Test completato con successo" : "Test fallito");

                return result;
            }
            catch (Exception ex)
            {
                LogTestStep("Flusso Trading Delete", false, $"Errore durante l'esecuzione: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> RunTestScenarioAsync(string[] args)
        {
            // Parametri di test da env
            var marketCode = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "MTA";
            var stockCode = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "BGN";
            var orderType = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_ORDER_TYPE"), out var ot) ? ot : 0;
            var price = double.TryParse(Environment.GetEnvironmentVariable("OT_TEST_PRICE"), out var p) ? p : 55.9;
            var quantity = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_QUANTITY"), out var q) ? q : 1;

            Logger.Info($"Parametri di test: Mercato={marketCode}, Titolo={stockCode}, Tipo={orderType}, Prezzo={price}, Quantità={quantity}");

            string orderId = null;
            var testSteps = new List<TestStepResult>();

            try
            {
                // 1. Verifica saldo disponibile (solo per acquisti)
                if (orderType == 0) // 0=acquisto
                {
                    bool enoughBalance = await CheckAccountBalanceAsync(price, quantity);
                    if (!enoughBalance)
                    {
                        LogTestStep("VerificaSaldo", false, "Saldo insufficiente per l'acquisto");
                        testSteps.Add(new TestStepResult("VerificaSaldo", false, "Saldo insufficiente per l'acquisto"));
                        return false;
                    }
                    LogTestStep("VerificaSaldo", true, "Saldo sufficiente per l'acquisto");
                    testSteps.Add(new TestStepResult("VerificaSaldo", true, "Saldo sufficiente per l'acquisto"));
                }

                // 2. Inserimento ordine iniziale
                var brokerName = 2; // Sella
                var insertResp = await InsertOrderWithRetryAsync(brokerName, marketCode, stockCode, orderType, price, quantity);
                orderId = JsonResponseHelper.TryExtractOrderId(insertResp);

                if (string.IsNullOrEmpty(orderId))
                {
                    LogTestStep("InserimentoOrdine", false, "Impossibile ottenere l'ID dell'ordine");
                    testSteps.Add(new TestStepResult("InserimentoOrdine", false, "Impossibile ottenere l'ID dell'ordine"));
                    return false;
                }

                LogTestStep("InserimentoOrdine", true, $"OrderID: {orderId}");
                testSteps.Add(new TestStepResult("InserimentoOrdine", true, $"OrderID: {orderId}"));

                // 2.5 Conferma ordine
                var confirmResp = await ConfirmOrderWithRetryAsync(orderId, 0);
                bool confirmSuccess = JsonResponseHelper.IsConfirmationSuccessful(confirmResp);

                if (!confirmSuccess)
                {
                    LogTestStep("ConfermaOrdine", false, "Conferma ordine fallita");
                    testSteps.Add(new TestStepResult("ConfermaOrdine", false, "Conferma ordine fallita"));
                    return false;
                }

                LogTestStep("ConfermaOrdine", true, "Ordine confermato con successo");
                testSteps.Add(new TestStepResult("ConfermaOrdine", true, "Ordine confermato con successo"));

                // 3. Cancellazione ordine
                var deleteResp = await DeleteOrderWithRetryAsync(brokerName, orderId);
                bool deleteSuccess = JsonResponseHelper.IsDeleteSuccessful(deleteResp);

                if (!deleteSuccess)
                {
                    LogTestStep("CancellazioneOrdine", false, "Cancellazione ordine fallita");
                    testSteps.Add(new TestStepResult("CancellazioneOrdine", false, "Cancellazione ordine fallita"));
                    return false;
                }

                LogTestStep("CancellazioneOrdine", true, "Ordine cancellato con successo");
                testSteps.Add(new TestStepResult("CancellazioneOrdine", true, "Ordine cancellato con successo"));

                // 3.5 Conferma cancellazione
                var confirmDeleteResp = await ConfirmOrderWithRetryAsync(orderId, 2);
                bool confirmDeleteSuccess = JsonResponseHelper.IsConfirmationSuccessful(confirmResp);

                if (!confirmSuccess)
                {
                    LogTestStep("ConfermaOrdine", false, "Conferma cancellazione ordine fallita");
                    testSteps.Add(new TestStepResult("ConfermaOrdine", false, "Conferma cancellazione ordine fallita"));
                    return false;
                }

                LogTestStep("ConfermaOrdine", true, "Ordine confermato con successo");
                testSteps.Add(new TestStepResult("ConfermaOrdine", true, "Ordine cancellato con successo"));

                // 4. Verifica che l'ordine sia stato effettivamente cancellato
                var verifyDelete = await VerifyOrderDeletedAsync(orderId);

                if (!verifyDelete)
                {
                    LogTestStep("VerificaCancellazione", false, "Verifica cancellazione ordine fallita");
                    testSteps.Add(new TestStepResult("VerificaCancellazione", false, "Verifica cancellazione ordine fallita"));
                    return false;
                }

                LogTestStep("VerificaCancellazione", true, "Verifica cancellazione ordine completata con successo");
                testSteps.Add(new TestStepResult("VerificaCancellazione", true, "Verifica cancellazione ordine completata con successo"));

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante l'esecuzione del test scenario: {ex.Message}");
                return false;
            }
        }
        private async Task<bool> CheckAccountBalanceAsync(double price, int quantity)
        {
            try
            {
                // GetAccountBalanceAsync ora restituisce direttamente il valore di AVAILABLE_LIQUIDITY
                decimal availableBalance = await _client.GetAccountBalanceAsync();
                decimal requiredAmount = (decimal)(price * quantity);

                Logger.Info($"Saldo disponibile: {availableBalance}, Importo richiesto: {requiredAmount}");
                return availableBalance >= requiredAmount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante la verifica del saldo: {ex.Message}");
                // In caso di errore, assumiamo che il saldo sia sufficiente
                return true;
            }
        }

        private async Task<JsonElement> ConfirmOrderWithRetryAsync(string orderId, int type)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di conferma ordine {orderId}");
                    return await _client.ConfirmOrderAsync((int)BrokerName.Sella, orderId, type);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di conferma ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile confermare l'ordine dopo tutti i tentativi");
        }
        private async Task<JsonElement> InsertOrderWithRetryAsync(int broker, string market, string stock, int orderType, double price, int quantity)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di inserimento ordine");
                    return await _client.InsertOrderAsync(broker, market, stock, orderType, price, quantity);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di inserimento ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile inserire l'ordine dopo tutti i tentativi");
        }

        private async Task<JsonElement> DeleteOrderWithRetryAsync(int broker, string orderId)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di cancellazione ordine {orderId}");
                    // Aggiungiamo il metodo DeleteOrderAsync al client
                    return await _client.DeleteOrderAsync(broker, orderId);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di cancellazione ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile cancellare l'ordine dopo tutti i tentativi");
        }

        private async Task<bool> VerifyOrderDeletedAsync(string orderId)
        {
            try
            {
                // Tentiamo di ottenere lo stato dell'ordine
                var statusResp = await _client.GetOrderStatusAsync((int)BrokerName.Sella, orderId);

                // Se l'ordine è stato cancellato, dovremmo ottenere uno stato "cancelled" o "deleted"
                if (statusResp.TryGetProperty("status", out var statusElem) &&
                    statusElem.ValueKind == JsonValueKind.String)
                {
                    string status = statusElem.GetString().ToLower();
                    return status == "cancelled" || status == "deleted";
                }

                if (statusResp.TryGetProperty("orderStatus", out var orderStatusElem) &&
                    orderStatusElem.ValueKind == JsonValueKind.String)
                {
                    string orderStatus = orderStatusElem.GetString().ToLower();
                    return orderStatus == "cancelled" || orderStatus == "deleted";
                }

                // Se non troviamo uno stato specifico, assumiamo che la cancellazione non sia andata a buon fine
                return false;
            }
            catch (ApiException ex)
            {
                // Se otteniamo un errore 404 (Not Found), potrebbe significare che l'ordine è stato cancellato
                if (ex.StatusCode == 404)
                {
                    Logger.Info("Ordine non trovato (404), considerato come cancellato con successo");
                    return true;
                }

                Logger.Error($"Errore API durante la verifica della cancellazione: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante la verifica della cancellazione: {ex.Message}");
                return false;
            }
        }
    }
}
