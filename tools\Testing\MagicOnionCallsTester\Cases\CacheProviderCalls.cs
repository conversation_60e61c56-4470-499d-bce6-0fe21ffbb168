﻿using MagicOnionCallsTester.Helpers;
using Microsoft.Extensions.Logging;
using OT.Common.Cache.Grpc;
using OT.Common.Customer.Models.Account;

namespace MagicOnionCallsTester.Cases
{
    public class CacheProviderCalls : BaseCalls
    {
        private readonly MagicOnionClient<ICacheSearchService> _cacheService;
        private readonly ILogger _logger;

        public CacheProviderCalls(
            ILogger logger
            ) : base()
        {
            _logger = logger;

            _cacheService = new MagicOnionClient<ICacheSearchService>("localhost", 6000);
        }

        public CacheProviderCalls Test_Generic()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Generic] Starting");

                // NON COMMITTARE

                // ...
                //_logger.LogInformation("[Generic] Result: {r}", );

                _logger.LogInformation("[Generic] Done");
            });
            return this;
        }

        public CacheProviderCalls GetDropdownCombinationsTree(CustomerLanguage lang = CustomerLanguage.Italian)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("Starting GetDropdownCombinationsTree");
                var response = await _cacheService.Client.GetDropdownCombinationsTree((int)lang).ResponseAsync;
                _logger.LogInformation("Done GetDropdownCombinationsTree");
            });

            return this;
        }
    }
}
