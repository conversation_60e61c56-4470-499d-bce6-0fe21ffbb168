﻿namespace MagicOnionCallsTester.Models.Customer
{
    public class BrokerCustomerCredentials
    {
        public static BrokerCustomerCredentials Default => new BrokerCustomerCredentials()
        {
            Username = "464459",
            CustomerId = 1116
        };

        public string OTAccessToken { get; set; }
        public string Username { get; set; }

        public int CustomerId { get; set; }

        public BrokerCustomerCredentials()
        {
            OTAccessToken = "MANUALTEST";
            Username = string.Empty;
        }

        public BrokerCustomerCredentials(string brokerCustomer, int customerId, string accessToken) : base()
        {
            Username = brokerCustomer;
            CustomerId = customerId;
            OTAccessToken = accessToken;
        }
    }
}
