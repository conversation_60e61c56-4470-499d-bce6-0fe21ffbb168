﻿a valle della chiamata di GetTradingInfo, dalla quale si reperiscono le informazioni del titolo, 

è necessario inserire i campi in InsertOrder: (vedi sotto) 

AspNetCoreEnvironment	pro	
CustomerIp	************	
CustomerId	1116	
AppVersion	2.0.39	
Status	OK	
UserAgent	SellaTrader/56 CFNetwork/3826.500.111.2.2 Darwin/24.4.0	
MarketCode	MTA	
UserId	********	
StockCode	BGN	
GamingId	0	
CashAccountId	895247	
Broker	Sella	
ChannelType	OTSELLATRADER	
DeviceType	SellaTraderAndroidPhone	
DossierId	898999	
OrderId	null	
ResultWarnings	Attenzione: Titolo o strumento sottostante soggetto a Bail in. Rischi illustrati nell'informativa in accesso alla piattaforma e nella sezione Servizi - Avvisi - <PERSON><PERSON> soggetti a Bail in.	
StopPrice	null	
Quantity	1	
Price	55.9	
ResultStatus	Ready	
ResultErrorCode	Undefined	
TempOrderId	e7c6f106-a4c5-4350-95cf-6bcf01f270b2	
IsBestExecution	False	
StopOrderStopPrice	null	
StopOrderTick	null	
StrategyConditionType	null	
IsOrderfast	False	
IsManualOrder	False	
OldOrderPhase	null	
EvaluationMode	null	
IsMargin	null	
StockType	null	
OperationPurpose	null	
OrderSource	None	
TakeOrderStopPrice	null	
PositionId	0	
OrderPhase	NegoziazioneContinua	
OrderParameter	null	
CustomerCode	null	
PriceType	null	
BondAccountId	1300770	
ValidityDate	null	
IsTakeStopOrderActive	False	
IsLeverageOrder	False	
IcebergOrderQty	null	
TakeOrderTick	null	
IsParkingOrder	False	
TakeOrderLimitPrice	null	
OrderType	Buy	
MainOrderTick	null	
StrategyEvaluatioNMode	null	
StopOrderLimitPrice	null	
IsBatchOrder	False


effettuata la chiamata di insert order questa deve essere confermata con la ConfirmInsertOrder: 

AspNetCoreEnvironment	pro	
CustomerIp	***********	
CustomerId	1116	
AppVersion	2.0.37	
Status	OK	
UserAgent	okhttp/4.10.0	
UserId	********	
Broker	Sella	
ChannelType	OTSELLATRADER	
DeviceType	SellaTraderAndroidPhone	
OrderId	**************	
TempOrderId	e7c6f106-a4c5-4350-95cf-6bcf01f270b2	
ResultStatus	Executed	
ResultErrorCode	Ok



effettuata la conferma e su un ordine che non ha avuto eseguito, rifiutato e affini è possibile fare la modifica. 

è possibile usare l'api UpdateOrder per eseguire modifica del campo puntuale.

AspNetCoreEnvironment	pro	
CustomerIp	**************	
CustomerId	1140	
AppVersion	2.0.23	
Status	OK	
UserAgent	SellaTrader/8 CFNetwork/1568.200.51 Darwin/24.1.0	
UserId	********	
Broker	Sella	
GamingId	-1	
ChannelType	OTSELLATRADER	
DeviceType	SellaTraderAndroidPhone	
OrderId	20250220215065	
StopPrice	null	
Quantity	2	
ResultStatus	Ready	
Price	2.135	
ResultErrorCode	Undefined	
IsFast	False

