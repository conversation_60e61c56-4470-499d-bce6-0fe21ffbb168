using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using TestAutomations.Utils;

namespace TestAutomations.Common
{
    public abstract class TestBase
    {
        // Nome del test
        public string Name { get; }
        
        // Indica se usare modalità dummy per il login
        public bool IsDummy { get; set; }
        
        // Logger condiviso
        protected static readonly Logger Logger = Logger.GetInstance();
        
        // Costruttore
        protected TestBase(string name, bool isDummy = false)
        {
            Name = name ?? throw new ArgumentNullException(nameof(name));
            IsDummy = isDummy;
        }
        
        // Metodo principale da implementare nei test derivati
        public abstract Task<bool> ExecuteAsync();
        
        // Helper per registrare i passaggi del test
        protected void LogTestStep(string step, bool success, string details = null)
        {
            string status = success ? "SUCCESS" : "FAILED";
            string message = $"[{Name}] {step}: {status}";
            
            if (!string.IsNullOrEmpty(details))
                message += $" - {details}";
                
            if (success)
                Logger.Info(message);
            else
                Logger.Error(message);
        }
    }
} 