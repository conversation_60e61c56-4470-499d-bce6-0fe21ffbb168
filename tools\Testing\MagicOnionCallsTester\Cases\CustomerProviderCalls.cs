﻿using MagicOnionCallsTester.Helpers;
using MagicOnionCallsTester.Models.Customer;
using Microsoft.Extensions.Logging;
using OT.Common.Customer.Grpc;
using OT.Common.Customer.Models.Account;
using OT.Common.Customer.Models.Views;
using System.Text.Json;

namespace MagicOnionCallsTester.Cases
{
    public class CustomerProviderCalls : BaseCalls
    {
        private readonly MagicOnionClient<ICustomerService> _customerService;
        private readonly MagicOnionClient<IAccountService> _accountService;
        private readonly MagicOnionClient<ICustomerManagementService> _cpManagementService;

        private readonly BrokerCustomerCredentials _customerCredentials;
        private readonly ILogger _logger;

        public CustomerProviderCalls(
            BrokerCustomerCredentials customerCredentials,
            ILogger logger
            ) : base()
        {
            _logger = logger;
            _customerCredentials = customerCredentials;

            _accountService = new MagicOnionClient<IAccountService>("localhost", 6005);
            _customerService = new MagicOnionClient<ICustomerService>("localhost", 6005);
            _cpManagementService = new MagicOnionClient<ICustomerManagementService>("localhost", 6005);
        }

        public CustomerProviderCalls Test_Generic()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Generic] Starting");

                // NON COMMITTARE

                // ...
                //_logger.LogInformation("[Generic] Result: {r}", );

                _logger.LogInformation("[Generic] Done");
            });
            return this;
        }

        public CustomerProviderCalls Test_SetCustomer(string alias)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("Starting SetCustomer");
                await _customerService.Client.SetCustomer(new OTCustomer()
                {
                    Alias = alias,
                    Password = Guid.NewGuid().ToString(),
                    Email = alias
                }).ResponseAsync;
                _logger.LogInformation("Done SetCustomer");
            });

            return this;
        }

        public CustomerProviderCalls Test_SavePersonalList(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[SavePersonalList] Starting");

                var pl = new PersonalList()
                {
                    CustomerId = _customerCredentials.CustomerId,
                    Name = $"MANUAL-{Guid.NewGuid().ToString()[0..4]}",
                    ViewTemplateName = "AZIONARIO_XTRADING",
                    Details = new List<PersonalListEntry>()
                    {
                        new PersonalListEntry()
                        {
                            MarketCode = "MTA",
                            StockCode = "ENI",
                            Order = 0
                        }
                    }
                };

                var result = await _accountService.Client.SavePersonalList(pl)
                .ResponseAsync;

                _logger.LogInformation("[SavePersonalList] Result: {r}", result);

                _logger.LogInformation("[SavePersonalList] Done");
            });

            return this;
        }

        public CustomerProviderCalls Test_GetPersonalView()
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[Starting] GetPersonalViewNew");

                var result = await _accountService.Client.GetPersonalViewNEW(_customerCredentials.CustomerId, null, null, 1, true)
                .ResponseAsync;

                _logger.LogInformation("[GetPersonalViewNew] Result: {r}", string.Join(';', result.Select(i => JsonSerializer.Serialize(i))));

                _logger.LogInformation("[GetPersonalViewNew] Done");
            });

            return this;
        }

        public CustomerProviderCalls Test_SavePersonalView(string json)
        {
            ChainTask(async () =>
            {
                _logger.LogInformation("[SavePersonalViewNew] Starting");

                var result = await _accountService.Client.SavePersonalViewNEW(new PersonalView()
                {
                    //ViewId = "MANUAL_ID",
                    //Name = "MANUAL_UPDATE",
                    Name = $"MANUAL-{DateTime.Now:yyyyMMddHHmmss}",
                    Description = $"Manual test {DateTime.Now:yyyyMMddHHmmss}",
                    CustomerId = _customerCredentials.CustomerId,
                    TemplateName = "AZIONARIO_XTRADING",
                    Columns = new List<Col>()
                    {
                        new Col()
                        {
                            Name = "ASK_PRICE_1",
                            Description = "Lettera",
                            Caption = "Lettera",
                            CaptionWeb = "Lettera",
                            Precision = null,
                            PositiveNegative = false,
                            Blink = true,
                            //Type = ColType.,
                            //Alignment = 2,
                            //Origin = 1,
                            //Section = 2,
                            //PlatformType = 31
                        },
                        new Col()
                        {
                            Name = "BID_PRICE_1",
                            Description = "Denaro",
                            Caption = "Denaro",
                            CaptionWeb = "Denaro",
                            Precision = null,
                            PositiveNegative = false,
                            Blink = true,
                            //Type = ColType.,
                            //Alignment = 2,
                            //Origin = 1,
                            //Section = 2,
                            //PlatformType = 31
                        },
                    }
                })
                .ResponseAsync;

                _logger.LogInformation("[SavePersonalViewNew] Result: {r}", JsonSerializer.Serialize(result));

                _logger.LogInformation("[Done] SavePersonalViewNew");
            });

            return this;
        }
    }
}