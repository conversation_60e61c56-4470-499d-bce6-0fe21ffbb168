using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using TestAutomations.Utils;
using TestAutomations.Models;
using TestAutomations.Clients;
using TestAutomations.Common;

namespace TestAutomations.Tests
{
    public class TradingFlowTest : TestBase
    {
        private readonly string[] _args;
        private OTTradingClient _client;
        private readonly string _username;
        private readonly string _password;
        private readonly List<TestStepResult> _testSteps = new List<TestStepResult>();

        // Parametri di configurazione per retry
        private const int MaxRetryAttempts = 3;
        private const int InitialRetryDelayMs = 1000;
        private const double RetryBackoffFactor = 1.5;

        public TradingFlowTest(string name, string[] args = null, bool isDummy = false) : base(name, isDummy)
        {
            _args = args ?? new string[0];

            // Ottieni credenziali dalle variabili d'ambiente o usa valori di default
            _username = Environment.GetEnvironmentVariable("OT_USERNAME") ?? "00464459";
            _password = Environment.GetEnvironmentVariable("OT_PASSWORD") ?? "password";

            var baseUrl = Environment.GetEnvironmentVariable("OT_API_BASE_URL") ?? "https://ot.tst.sella.it";
            _client = new OTTradingClient(baseUrl);

            Logger.Info($"Test inizializzato con modalità login: {(IsDummy ? "Dummy" : "Normale")}");
            Logger.Info($"Username: {_username}, API URL: {baseUrl}");
        }

        public override async Task<bool> ExecuteAsync()
        {
            try
            {
                // Esegui login prima di procedere con il test di trading flow
                bool loginSuccess;
                if (IsDummy)
                {
                    Logger.Info($"Esecuzione dummy login come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: true);
                }
                else
                {
                    Logger.Info($"Esecuzione login normale come {_username}...");
                    loginSuccess = await _client.LoginAsync(_username, _password, isDummy: false);
                }

                if (!loginSuccess)
                {
                    LogTestStep("Login", false, "Login fallito prima dell'esecuzione del test di trading flow");
                    _testSteps.Add(new TestStepResult("Login", false, "Login fallito"));
                    return false;
                }

                LogTestStep("Login", true, "Login completato con successo");
                _testSteps.Add(new TestStepResult("Login", true, "Login completato con successo"));

                // Esegui il test scenario
                bool testResult = await ExecuteTradingFlow();

                if (!testResult)
                {
                    Logger.Error("Test di trading flow fallito");
                    return false;
                }

                Logger.Info("Test di trading flow completato con successo");
                return true;
            }
            catch (ApiException ex)
            {
                Logger.Error($"Errore API durante l'esecuzione del test: {ex.Message}");
                LogTestStep("TradingFlow", false, $"Errore API: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlow", false, $"Errore API: {ex.Message}"));
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"Eccezione durante l'esecuzione del test: {ex.Message}");
                Logger.Error(ex.StackTrace);
                LogTestStep("TradingFlow", false, $"Errore generico: {ex.Message}");
                _testSteps.Add(new TestStepResult("TradingFlow", false, $"Errore generico: {ex.Message}"));
                return false;
            }
        }

        public async Task<bool> ExecuteTradingFlow()
        {
            try
            {
                // Esegui il test scenario
                bool result = await RunTestScenarioAsync(_args);

                // Aggiungiamo un log riepilogativo
                LogTestStep("Flusso Trading", result, result ? "Test completato con successo" : "Test fallito");

                return result;
            }
            catch (Exception ex)
            {
                LogTestStep("Flusso Trading", false, $"Errore durante l'esecuzione: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> RunTestScenarioAsync(string[] args)
        {
            // Parametri di test da env
            var marketCode = Environment.GetEnvironmentVariable("OT_TEST_MARKET") ?? "MTA";
            var stockCode = Environment.GetEnvironmentVariable("OT_TEST_STOCK") ?? "BGN";
            var orderType = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_ORDER_TYPE"), out var ot) ? ot : 0;
            var price = double.TryParse(Environment.GetEnvironmentVariable("OT_TEST_PRICE"), out var p) ? p : 0;
            var quantity = int.TryParse(Environment.GetEnvironmentVariable("OT_TEST_QUANTITY"), out var q) ? q : 1;

            Logger.Info($"Parametri di test: Mercato={marketCode}, Titolo={stockCode}, Tipo={orderType}, Prezzo={price}, Quantità={quantity}");

            string orderId = null;
            var testSteps = new List<TestStepResult>();

            try
            {
                // 1. Verifica saldo disponibile (solo per acquisti)
                if (orderType == 0) // 0=acquisto
                {
                    bool enoughBalance = await CheckAccountBalanceAsync(price, quantity);
                    if (!enoughBalance)
                    {
                        LogTestStep("VerificaSaldo", false, "Saldo insufficiente per l'acquisto");
                        testSteps.Add(new TestStepResult("VerificaSaldo", false, "Saldo insufficiente per l'acquisto"));
                        return false;
                    }
                    LogTestStep("VerificaSaldo", true, "Saldo sufficiente per l'acquisto");
                    testSteps.Add(new TestStepResult("VerificaSaldo", true, "Saldo sufficiente per l'acquisto"));
                }

                // 2. Inserimento ordine
                var brokerName = 2; // Sella
                var insertResp = await InsertOrderWithRetryAsync(brokerName, marketCode, stockCode, orderType, price, quantity);
                orderId = JsonResponseHelper.TryExtractOrderId(insertResp);

                if (string.IsNullOrEmpty(orderId))
                {
                    LogTestStep("InserimentoOrdine", false, $"Impossibile ottenere l'ID dell'ordine. Errori: {JsonResponseHelper.ExtractErrorsFromResponse(insertResp)}");
                    testSteps.Add(new TestStepResult("InserimentoOrdine", false, "Impossibile ottenere l'ID dell'ordine"));
                    return false;
                }

                LogTestStep("InserimentoOrdine", true, $"OrderID: {orderId}");
                testSteps.Add(new TestStepResult("InserimentoOrdine", true, $"OrderID: {orderId}"));

                // 3. Conferma ordine
                var confirmResp = await ConfirmOrderWithRetryAsync(brokerName, orderId);
                bool confirmSuccess = JsonResponseHelper.IsConfirmationSuccessful(confirmResp);
                orderId = JsonResponseHelper.TryExtractOrderId(confirmResp);

                if (!confirmSuccess)
                {
                    LogTestStep("ConfermaOrdine", false, "Conferma ordine fallita");
                    testSteps.Add(new TestStepResult("ConfermaOrdine", false, "Conferma ordine fallita"));
                    return false;
                }

                LogTestStep("ConfermaOrdine", true, "Ordine confermato con successo");
                testSteps.Add(new TestStepResult("ConfermaOrdine", true, "Ordine confermato con successo"));

                // 4. Monitoraggio stato ordine
                var orderStatus = await WaitForOrderCompletionAsync(orderId);
                if (orderStatus == null || !JsonResponseHelper.IsOrderCompleted(orderStatus))
                {
                    LogTestStep("MonitoraggioOrdine", false, "Ordine non completato entro il timeout");
                    testSteps.Add(new TestStepResult("MonitoraggioOrdine", false, "Ordine non completato entro il timeout"));
                    return false;
                }

                LogTestStep("MonitoraggioOrdine", true, $"Ordine completato con stato: {GetOrderStatusDescription(orderStatus)}");
                testSteps.Add(new TestStepResult("MonitoraggioOrdine", true, $"Ordine completato con stato: {GetOrderStatusDescription(orderStatus)}"));

                // 5. Verifica portafoglio (solo se l'ordine è stato eseguito)
                int executionType = JsonResponseHelper.IsOrderExecuted(orderStatus);
                if (executionType > 0)
                {
                    if(executionType == 1)
                    {
                         quantity = JsonResponseHelper.GetExecutedQuantity(orderStatus);
                    }

                    bool portfolioUpdated = await VerifyPortfolioUpdateAsync(stockCode, orderType == 0 ? quantity : -quantity);
                    if (!portfolioUpdated)
                    {
                        LogTestStep("VerificaPortafoglio", false, "Portafoglio non aggiornato correttamente");
                        testSteps.Add(new TestStepResult("VerificaPortafoglio", false, "Portafoglio non aggiornato correttamente"));
                        return false;
                    }

                    LogTestStep("VerificaPortafoglio", true, "Portafoglio aggiornato correttamente");
                    testSteps.Add(new TestStepResult("VerificaPortafoglio", true, "Portafoglio aggiornato correttamente"));
                }
                else
                {
                    LogTestStep("VerificaPortafoglio", true, "Verifica portafoglio saltata (ordine non eseguito)");
                    testSteps.Add(new TestStepResult("VerificaPortafoglio", true, "Verifica portafoglio saltata (ordine non eseguito)"));
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante l'esecuzione del test scenario: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> CheckAccountBalanceAsync(double price, int quantity)
        {
            try
            {
                // GetAccountBalanceAsync ora restituisce direttamente il valore di AVAILABLE_LIQUIDITY
                decimal availableBalance = await _client.GetAccountBalanceAsync();
                decimal requiredAmount = (decimal)(price * quantity);

                Logger.Info($"Saldo disponibile: {availableBalance}, Importo richiesto: {requiredAmount}");
                return availableBalance >= requiredAmount;
            }
            catch (Exception ex)
            {
                Logger.Error($"Errore durante la verifica del saldo: {ex.Message}");
                // In caso di errore, assumiamo che il saldo sia sufficiente
                return true;
            }
        }

        private async Task<JsonElement> InsertOrderWithRetryAsync(int broker, string market, string stock, int orderType, double price, int quantity)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di inserimento ordine");
                    return await _client.InsertOrderAsync(broker, market, stock, orderType, price, quantity);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di inserimento ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile inserire l'ordine dopo tutti i tentativi");
        }

        private async Task<JsonElement> ConfirmOrderWithRetryAsync(int broker, string orderId)
        {
            int attempt = 0;
            int delayMs = InitialRetryDelayMs;

            while (attempt < MaxRetryAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Tentativo {attempt}/{MaxRetryAttempts} di conferma ordine {orderId}");
                    return await _client.ConfirmOrderAsync((int)BrokerName.Sella, orderId);
                }
                catch (Exception ex)
                {
                    if (attempt == MaxRetryAttempts)
                    {
                        Logger.Error($"Tutti i tentativi di conferma ordine falliti: {ex.Message}");
                        throw;
                    }

                    Logger.Warning($"Tentativo {attempt} fallito: {ex.Message}. Nuovo tentativo tra {delayMs}ms");
                    await Task.Delay(delayMs);
                    delayMs = (int)(delayMs * RetryBackoffFactor);
                }
            }

            throw new Exception("Impossibile confermare l'ordine dopo tutti i tentativi");
        }

        private async Task<JsonElement?> WaitForOrderCompletionAsync(string orderId)
        {
            const int maxAttempts = 3;
            int attempt = 0;
            int delayMs = 2000;

            while (attempt < maxAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Verifica stato ordine {orderId} - Tentativo {attempt}/{maxAttempts}");
                    var statusResp = await _client.GetOrderStatusAsync((int)BrokerName.Sella, orderId);

                    if (JsonResponseHelper.IsOrderCompleted(statusResp))
                    {
                        Logger.Info($"Ordine {orderId} completato");
                        return statusResp;
                    }

                    string currentStatus = GetOrderStatusDescription(statusResp);
                    Logger.Info($"Ordine {orderId} in stato: {currentStatus}. Attesa...");

                    await Task.Delay(delayMs);
                    delayMs = Math.Min(delayMs * 2, 30000); // Max 30 secondi tra i tentativi
                }
                catch (Exception ex)
                {
                    Logger.Warning($"Errore durante la verifica dello stato dell'ordine: {ex.Message}");
                    await Task.Delay(delayMs);
                }
            }

            Logger.Warning($"Timeout durante l'attesa del completamento dell'ordine {orderId}");
            return null;
        }

        private async Task<bool> VerifyPortfolioUpdateAsync(string stockCode, int expectedChange)
        {
            const int maxAttempts = 5;
            int attempt = 0;
            int delayMs = 2000;
            bool found = false;
            bool quantityMatch = false;

            while (attempt < maxAttempts)
            {
                attempt++;
                try
                {
                    Logger.Info($"Verifica aggiornamento portafoglio per {stockCode} - Tentativo {attempt}/{maxAttempts}");
                    var portfolioResp = await _client.GetPortfolioAsync((int)BrokerName.Sella);

                    // Cerca il titolo nel portafoglio
                    found = false;
                    quantityMatch = false;

                    // Prima prova con la nuova struttura
                    if (portfolioResp.TryGetProperty("Data", out var dataElem) &&
                        dataElem.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var dataItem in dataElem.EnumerateArray())
                        {
                            if (dataItem.TryGetProperty("Values", out var valuesElem) &&
                                valuesElem.ValueKind == JsonValueKind.Object &&
                                valuesElem.TryGetProperty("STOCK_CODE", out var stockCodeElem) &&
                                stockCodeElem.ValueKind == JsonValueKind.String &&
                                stockCodeElem.GetString() == stockCode)
                            {
                                found = true;
                                
                                if (valuesElem.TryGetProperty("POSITION_QUANTITY", out var quantityElem) &&
                                    quantityElem.ValueKind == JsonValueKind.Number)
                                {
                                    int actualQuantity = quantityElem.GetInt32();
                                    if (actualQuantity == expectedChange)
                                    {
                                        quantityMatch = true;
                                        Logger.Info($"Titolo {stockCode} trovato nel portafoglio con quantità corretta: {actualQuantity}");
                                    }
                                    else
                                    {
                                        Logger.Warning($"Titolo {stockCode} trovato ma la quantità non corrisponde: {actualQuantity} vs {expectedChange}");
                                    }
                                }
                                else
                                {
                                    Logger.Info($"Titolo {stockCode} trovato nel portafoglio ma non è possibile verificare la quantità");
                                }
                                
                                break; // Interrompiamo il ciclo perché abbiamo trovato il titolo
                            }
                        }
                    }

                    // Se abbiamo trovato il titolo, usciamo dal ciclo di retry
                    if (found)
                    {
                        return quantityMatch;
                    }

                    if (!found)
                    {
                        Logger.Warning($"Titolo {stockCode} non trovato nel portafoglio. Attesa...");
                        await Task.Delay(delayMs);
                        delayMs = Math.Min(delayMs * 2, 10000); // Max 10 secondi tra i tentativi
                    }
                }
                catch (Exception ex)
                {
                    Logger.Warning($"Errore durante la verifica del portafoglio: {ex.Message}");
                    await Task.Delay(delayMs);
                }
            }

            Logger.Warning($"Timeout durante la verifica dell'aggiornamento del portafoglio per {stockCode}");
            return false;
        }

        private string GetOrderStatusDescription(JsonElement? statusResponse)
        {
            if (statusResponse == null)
                return "Stato sconosciuto";

            // Struttura API OT: Data[i].Status (come definito nello swagger)
            if (statusResponse.Value.TryGetProperty("Data", out var dataElem) &&
                dataElem.ValueKind == JsonValueKind.Array)
            {
                foreach (var orderData in dataElem.EnumerateArray())
                {
                    if (orderData.TryGetProperty("Status", out var statusElem))
                    {
                        // Status è un intero secondo l'enum OrderStatus nello swagger
                        if (statusElem.ValueKind == JsonValueKind.Number)
                        {
                            int status = statusElem.GetInt32();
                            // Mappa degli stati numerici basata sull'enum OrderStatus dello swagger
                            var statusMap = new Dictionary<int, string>
                            {
                                { 0, "None" },
                                { 1, "Wait" },
                                { 2, "Accepted" },
                                { 4, "Rejected" },
                                { 8, "PartialDeleted" },
                                { 16, "Deleted" },
                                { 32, "PartialExecuted" },
                                { 64, "Executed" },
                                { 128, "NotExecuted" },
                                { 512, "Parked" },
                                { 1024, "Sent" },
                                { 2048, "Batch" }
                            };

                            return statusMap.TryGetValue(status, out var statusStr)
                                ? $"{statusStr} ({status})"
                                : $"Unknown ({status})";
                        }
                    }
                }
            }

            return "Stato sconosciuto";
        }
    }

    public static class JsonElementExtensions
    {
        public static JsonElement? GetPropertyOrNull(this JsonElement element, string propertyName)
        {
            return element.TryGetProperty(propertyName, out var value) ? value : (JsonElement?)null;
        }
    }
}



